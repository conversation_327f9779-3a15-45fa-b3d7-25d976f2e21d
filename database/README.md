# 数据库相关文件

包含数据库迁移脚本、种子数据和数据库模式定义。

## 目录结构

```
database/
├── migrations/       # 数据库迁移脚本
├── seeds/           # 种子数据
├── schemas/         # 数据库模式定义
└── scripts/         # 数据库工具脚本
```

## 数据库设计

### 核心模块

1. **用户与权限模块**
   - `users` - 用户账户
   - `user_sessions` - 用户会话

2. **知识本体模块**
   - `knowledge_points` - 知识点定义
   - `prerequisite_relation` - 知识点先修关系

3. **题库管理模块**
   - `questions` - 题目主表
   - `question_assets` - 题目媒资
   - `item_param` - IRT参数

4. **关联映射模块**
   - `item_kp_map` - 题-知识点映射（Q-矩阵）
   - `question_relation` - 题-题关系

5. **知识空间模块**
   - `knowledge_state` - 知识状态
   - `state_transition` - 状态转移

6. **标注工作流模块**
   - `annotation_tasks` - 标注任务
   - `annotation_logs` - 操作日志

7. **版本管理模块**
   - `question_versions` - 题目版本
   - `kp_versions` - 知识点版本

### 数据库扩展

- **LTREE** - 用于知识点层级结构
- **pgRouting** - 用于图算法计算

## 迁移管理

使用 Alembic 进行数据库版本管理：

```bash
# 创建新迁移
alembic revision --autogenerate -m "添加新表"

# 执行迁移
alembic upgrade head

# 查看迁移历史
alembic history

# 回滚到指定版本
alembic downgrade <revision>
```

## 种子数据

`seeds/` 目录包含初始化数据：

- `users.sql` - 初始用户数据
- `knowledge_points.sql` - 基础知识点
- `sample_questions.sql` - 示例题目

加载种子数据：
```bash
psql -d annotation -f seeds/users.sql
psql -d annotation -f seeds/knowledge_points.sql
```

## 数据库脚本

`scripts/` 目录包含常用数据库操作脚本：

- `init_db.sql` - 数据库初始化
- `create_extensions.sql` - 创建必要扩展
- `backup.sh` - 数据备份脚本
- `restore.sh` - 数据恢复脚本

## 性能优化

### 索引策略

1. **知识点查询**
   ```sql
   CREATE INDEX idx_kp_path ON knowledge_points USING GIST (path);
   CREATE INDEX idx_kp_parent ON knowledge_points (parent_id);
   ```

2. **题目搜索**
   ```sql
   CREATE INDEX idx_question_content ON questions USING GIN (to_tsvector('simple', content->>'stem'));
   CREATE INDEX idx_question_type ON questions (q_type, is_active);
   ```

3. **关联查询**
   ```sql
   CREATE INDEX idx_item_kp_map_kp ON item_kp_map (kp_id) INCLUDE (item_id, confidence);
   CREATE INDEX idx_question_relation_dst ON question_relation (dst_q_id) WHERE rel_type = 'prerequisite';
   ```

### 分区策略

对于大量历史数据的表，考虑按时间分区：

```sql
-- 按月分区annotation_logs表
CREATE TABLE annotation_logs_y2024m01 PARTITION OF annotation_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

## 数据一致性

### 约束检查

1. **先修关系无环检查**
   ```sql
   -- 触发器检测新增边是否形成环
   CREATE OR REPLACE FUNCTION check_prerequisite_cycle()
   RETURNS TRIGGER AS $$
   -- 实现环检测逻辑
   $$;
   ```

2. **IRT参数一致性**
   ```sql
   -- 检查prerequisite关系的难度单调性
   SELECT src.question_id, dst.question_id
   FROM question_relation qr
   JOIN item_param src ON qr.src_q_id = src.question_id
   JOIN item_param dst ON qr.dst_q_id = dst.question_id
   WHERE qr.rel_type = 'prerequisite' AND src.b > dst.b + 0.2;
   ```

## 备份与恢复

### 定期备份
```bash
# 全量备份
pg_dump annotation > backup_$(date +%Y%m%d).sql

# 仅数据备份
pg_dump --data-only annotation > data_backup_$(date +%Y%m%d).sql
```

### 恢复数据
```bash
# 恢复数据库
psql annotation < backup_20240101.sql

# 恢复特定表
pg_restore --table=questions backup.dump
```

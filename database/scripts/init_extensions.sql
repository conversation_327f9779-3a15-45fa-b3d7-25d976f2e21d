-- 数据库扩展初始化脚本
-- 为自适应学习数据标注系统创建必要的PostgreSQL扩展

-- 创建LTREE扩展（用于知识点层级结构）
CREATE EXTENSION IF NOT EXISTS ltree;

-- 创建UUID扩展（用于生成UUID）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建pgcrypto扩展（用于加密功能）
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 创建pg_trgm扩展（用于文本相似度搜索）
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 创建unaccent扩展（用于去除重音符号）
CREATE EXTENSION IF NOT EXISTS unaccent;

-- 创建btree_gin扩展（用于复合索引）
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 创建btree_gist扩展（用于范围查询）
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- 如果需要图算法支持，可以启用pgrouting（需要额外安装）
-- CREATE EXTENSION IF NOT EXISTS pgrouting;

-- 创建全文搜索配置
CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS chinese (COPY = simple);

-- 设置数据库参数
ALTER DATABASE annotation SET timezone TO 'Asia/Shanghai';
ALTER DATABASE annotation SET default_text_search_config TO 'chinese';

-- 创建自定义函数：检测环路
CREATE OR REPLACE FUNCTION detect_cycle_in_prerequisites()
RETURNS TRIGGER AS $$
DECLARE
    cycle_detected BOOLEAN := FALSE;
BEGIN
    -- 使用递归CTE检测是否会形成环路
    WITH RECURSIVE prerequisite_path AS (
        -- 起始点：新插入的边的终点
        SELECT NEW.post_kp_id as kp_id, ARRAY[NEW.post_kp_id] as path, 0 as depth
        
        UNION ALL
        
        -- 递归：沿着先修关系向前查找
        SELECT pr.post_kp_id, path || pr.post_kp_id, depth + 1
        FROM prerequisite_relation pr
        JOIN prerequisite_path pp ON pr.pre_kp_id = pp.kp_id
        WHERE NOT (pr.post_kp_id = ANY(path)) -- 避免无限循环
          AND depth < 100 -- 限制递归深度
    )
    SELECT EXISTS(
        SELECT 1 FROM prerequisite_path 
        WHERE kp_id = NEW.pre_kp_id
    ) INTO cycle_detected;
    
    IF cycle_detected THEN
        RAISE EXCEPTION '插入此先修关系会形成环路，操作被拒绝';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建自定义函数：更新知识点路径
CREATE OR REPLACE FUNCTION update_knowledge_point_path()
RETURNS TRIGGER AS $$
BEGIN
    -- 当知识点的父级发生变化时，更新其路径
    IF TG_OP = 'UPDATE' AND (OLD.parent_id IS DISTINCT FROM NEW.parent_id) THEN
        -- 更新当前节点的路径
        IF NEW.parent_id IS NULL THEN
            NEW.path = NEW.kp_id::text::ltree;
        ELSE
            SELECT path || NEW.kp_id::text INTO NEW.path
            FROM knowledge_points 
            WHERE kp_id = NEW.parent_id;
        END IF;
        
        -- 更新所有子节点的路径
        WITH RECURSIVE children AS (
            SELECT kp_id, parent_id, NEW.path as new_base_path
            FROM knowledge_points 
            WHERE parent_id = NEW.kp_id
            
            UNION ALL
            
            SELECT kp.kp_id, kp.parent_id, c.new_base_path || kp.kp_id::text
            FROM knowledge_points kp
            JOIN children c ON kp.parent_id = c.kp_id
        )
        UPDATE knowledge_points 
        SET path = c.new_base_path || knowledge_points.kp_id::text
        FROM children c
        WHERE knowledge_points.kp_id = c.kp_id;
        
    ELSIF TG_OP = 'INSERT' THEN
        -- 插入新节点时设置路径
        IF NEW.parent_id IS NULL THEN
            NEW.path = NEW.kp_id::text::ltree;
        ELSE
            SELECT path || NEW.kp_id::text INTO NEW.path
            FROM knowledge_points 
            WHERE kp_id = NEW.parent_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建自定义函数：计算知识点深度
CREATE OR REPLACE FUNCTION calculate_kp_depth(kp_path ltree)
RETURNS INTEGER AS $$
BEGIN
    RETURN nlevel(kp_path) - 1;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 创建自定义函数：获取知识点祖先
CREATE OR REPLACE FUNCTION get_kp_ancestors(kp_path ltree)
RETURNS TABLE(ancestor_id INTEGER, depth INTEGER) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        subpath(kp_path, 0, level)::text::INTEGER as ancestor_id,
        level as depth
    FROM generate_series(1, nlevel(kp_path) - 1) as level;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 创建自定义函数：获取知识点后代
CREATE OR REPLACE FUNCTION get_kp_descendants(root_path ltree)
RETURNS TABLE(descendant_id INTEGER, relative_path ltree) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        kp_id as descendant_id,
        subpath(path, nlevel(root_path)) as relative_path
    FROM knowledge_points
    WHERE path <@ root_path AND path != root_path;
END;
$$ LANGUAGE plpgsql STABLE;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '数据库扩展和自定义函数初始化完成';
    RAISE NOTICE '已创建扩展: ltree, uuid-ossp, pgcrypto, pg_trgm, unaccent, btree_gin, btree_gist';
    RAISE NOTICE '已创建自定义函数: detect_cycle_in_prerequisites, update_knowledge_point_path, calculate_kp_depth, get_kp_ancestors, get_kp_descendants';
END $$;

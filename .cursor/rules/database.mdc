---
alwaysApply: true
---
# 数据库访问规则
数据库为Supabase项目，完全兼容postgresql

## 数据库连接配置

### 连接信息
- **连接字符串**: `postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres`
- **数据库类型**: PostgreSQL (Supabase)
- **编码**: UTF-8
- **时区**: UTC

### 环境变量设置
```bash
DATABASE_URL=postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres
DB_HOST=aws-0-ap-southeast-1.pooler.supabase.com
DB_PORT=6543
DB_NAME=postgres
DB_USER=postgres.qlpqyctsezzfzdgvfvir
DB_PASSWORD=UZyzHE1hy9kOddMv
```

## 数据库架构规范

### 核心表结构
1. **用户与权限模块**
   - `users` - 系统账户
   - 角色类型: `admin`, `annotator`, `reviewer`, `viewer`, `engine`

2. **知识本体模块**
   - `knowledge_points` - 知识点/技能定义
   - `prerequisite_relation` - 知识点先修关系

3. **题库模块**
   - `questions` - 题目主表
   - `question_assets` - 媒资文件
   - `item_param` - IRT 参数

4. **映射关系模块**
   - `item_kp_map` - 题-知识点映射(Q-矩阵)
   - `question_relation` - 题-题关系

5. **知识空间模块**
   - `knowledge_state` - 知识状态
   - `state_transition` - 状态转移

6. **标注工作流模块**
   - `annotation_tasks` - 标注任务
   - `annotation_logs` - 操作审计

7. **版本管理模块**
   - `question_versions` - 题目快照
   - `kp_versions` - 知识点快照

8. **模型参数模块**
   - `skill_param` - BKT/技能参数

### 数据类型约定
- 主键: `BIGSERIAL` 或 `SERIAL`
- 时间戳: `TIMESTAMPTZ`
- JSON 数据: `JSONB`
- 布尔值: `BOOLEAN`
- 路径: `LTREE` (知识点层级)
- 位向量: `BIT VARYING`

### 枚举类型
```sql
-- 题型枚举
CREATE TYPE q_type AS ENUM ('SC', 'MC', 'TF', 'Cloze', 'Essay');

-- 题-题关系枚举
CREATE TYPE q_rel_type AS ENUM ('complements', 'progresses_to', 'equivalent', 'prerequisite', 'revision');

-- 任务状态枚举
CREATE TYPE task_state AS ENUM ('pending', 'in_progress', 'review', 'done');
```

## 开发规范

### SQL 编写规范
1. **命名约定**
   - 表名: 小写下划线分隔 (`knowledge_points`)
   - 字段名: 小写下划线分隔 (`created_at`)
   - 外键: `表名_id` 格式 (`user_id`)
   - 主键 `id`

2. **索引策略**
   - 主键自动创建索引
   - 外键字段创建索引
   - 查询频繁字段创建复合索引
   - 全文搜索使用 GIN 索引

3. **约束规则**
   - 所有表必须有主键
   - 外键关系明确定义
   - 非空约束合理设置
   - 唯一约束防止重复

### 数据完整性
1. **引用完整性**
   - 所有外键必须有对应的主键
   - 级联删除策略明确定义

2. **业务约束**
   - 先修关系无环检查
   - 知识点层级路径一致性
   - 题目-知识点映射有效性

3. **版本控制**
   - 所有修改记录到审计表
   - 关键数据变更创建快照
   - 支持版本回滚功能

### 性能优化
1. **查询优化**
   - 使用适当的索引
   - 避免 N+1 查询
   - 使用 EXPLAIN 分析查询计划

2. **数据分区**
   - 大表考虑按时间分区
   - 历史数据归档策略

3. **缓存策略**
   - 频繁查询数据使用 Redis 缓存
   - 知识空间状态内存缓存


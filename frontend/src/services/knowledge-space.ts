/**
 * 知识空间管理API服务
 */

import { api } from './api'

// 类型定义
export interface KnowledgeState {
  state_id: number
  state_vector: boolean[]
  state_hash: string
  state_type: number
  is_valid: boolean
  mastery_count: number
  total_count: number
  mastery_ratio: number
  description?: string
  meta_data?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface StateTransition {
  transition_id: number
  from_state_id: number
  to_state_id: number
  transition_type: number
  probability: number
  trigger_kp_id?: number
  trigger_question_id?: number
  created_at: string
  updated_at: string
}

export interface KnowledgeSpaceStats {
  total_knowledge_points: number
  total_states: number
  valid_states: number
  total_transitions: number
  empty_state_id?: number
  full_state_id?: number
}

export interface KnowledgeSpaceOverview {
  knowledge_points: Array<{
    kp_id: number
    name: string
    code: string
    description?: string
    is_leaf: boolean
  }>
  states: KnowledgeState[]
  transitions: StateTransition[]
  stats: KnowledgeSpaceStats
}

export interface KnowledgeSpaceBuildRequest {
  knowledge_point_ids?: number[]
  force_rebuild?: boolean
  include_invalid_states?: boolean
}

export interface KnowledgeSpaceBuildResponse {
  success: boolean
  message: string
  stats?: KnowledgeSpaceStats
  build_time: number
}

export interface KnowledgeStateQuery {
  state_type?: number
  is_valid?: boolean
  min_mastery_ratio?: number
  max_mastery_ratio?: number
  knowledge_point_ids?: number[]
}

export interface StateTransitionQuery {
  from_state_id?: number
  to_state_id?: number
  transition_type?: number
  trigger_kp_id?: number
  min_probability?: number
}

/**
 * 知识空间管理API服务类
 */
export class KnowledgeSpaceService {
  /**
   * 构建知识空间
   */
  static async buildKnowledgeSpace(request: KnowledgeSpaceBuildRequest): Promise<KnowledgeSpaceBuildResponse> {
    const response = await api.post('/knowledge-space/build', request)
    return response.data
  }

  /**
   * 获取知识空间概览
   */
  static async getKnowledgeSpaceOverview(kpIds?: number[]): Promise<KnowledgeSpaceOverview> {
    const params = new URLSearchParams()
    if (kpIds && kpIds.length > 0) {
      kpIds.forEach(id => params.append('kp_ids', id.toString()))
    }
    
    const response = await api.get(`/knowledge-space/overview?${params}`)
    return response.data
  }

  /**
   * 查询知识状态
   */
  static async getKnowledgeStates(
    query: KnowledgeStateQuery = {},
    skip = 0,
    limit = 100
  ): Promise<KnowledgeState[]> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    })

    if (query.state_type !== undefined) params.append('state_type', query.state_type.toString())
    if (query.is_valid !== undefined) params.append('is_valid', query.is_valid.toString())
    if (query.min_mastery_ratio !== undefined) params.append('min_mastery_ratio', query.min_mastery_ratio.toString())
    if (query.max_mastery_ratio !== undefined) params.append('max_mastery_ratio', query.max_mastery_ratio.toString())
    if (query.knowledge_point_ids) {
      query.knowledge_point_ids.forEach(id => params.append('knowledge_point_ids', id.toString()))
    }

    const response = await api.get(`/knowledge-space/states?${params}`)
    return response.data
  }

  /**
   * 获取单个知识状态
   */
  static async getKnowledgeState(stateId: number): Promise<KnowledgeState> {
    const response = await api.get(`/knowledge-space/states/${stateId}`)
    return response.data
  }

  /**
   * 查询状态转移
   */
  static async getStateTransitions(
    query: StateTransitionQuery = {},
    skip = 0,
    limit = 100
  ): Promise<StateTransition[]> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    })

    if (query.from_state_id !== undefined) params.append('from_state_id', query.from_state_id.toString())
    if (query.to_state_id !== undefined) params.append('to_state_id', query.to_state_id.toString())
    if (query.transition_type !== undefined) params.append('transition_type', query.transition_type.toString())
    if (query.trigger_kp_id !== undefined) params.append('trigger_kp_id', query.trigger_kp_id.toString())
    if (query.min_probability !== undefined) params.append('min_probability', query.min_probability.toString())

    const response = await api.get(`/knowledge-space/transitions?${params}`)
    return response.data
  }

  /**
   * 获取单个状态转移
   */
  static async getStateTransition(transitionId: number): Promise<StateTransition> {
    const response = await api.get(`/knowledge-space/transitions/${transitionId}`)
    return response.data
  }

  /**
   * 获取状态的出边转移
   */
  static async getStateOutgoingTransitions(stateId: number): Promise<StateTransition[]> {
    const response = await api.get(`/knowledge-space/states/${stateId}/transitions`)
    return response.data
  }

  /**
   * 获取状态的入边转移
   */
  static async getStateIncomingTransitions(stateId: number): Promise<StateTransition[]> {
    const response = await api.get(`/knowledge-space/states/${stateId}/incoming-transitions`)
    return response.data
  }

  /**
   * 清除知识空间
   */
  static async clearKnowledgeSpace(kpIds?: number[]): Promise<void> {
    const params = new URLSearchParams()
    if (kpIds && kpIds.length > 0) {
      kpIds.forEach(id => params.append('kp_ids', id.toString()))
    }
    
    await api.delete(`/knowledge-space/states?${params}`)
  }
}

// 状态类型枚举
export const StateType = {
  EMPTY: 0,
  INTERMEDIATE: 1,
  FULL: 2,
  INVALID: 3,
} as const

// 转移类型枚举
export const TransitionType = {
  LEARNING: 0,
  FORGETTING: 1,
  ASSESSMENT: 2,
} as const

// 状态类型标签
export const StateTypeLabels = {
  [StateType.EMPTY]: '空状态',
  [StateType.INTERMEDIATE]: '中间状态',
  [StateType.FULL]: '满状态',
  [StateType.INVALID]: '无效状态',
} as const

// 转移类型标签
export const TransitionTypeLabels = {
  [TransitionType.LEARNING]: '学习转移',
  [TransitionType.FORGETTING]: '遗忘转移',
  [TransitionType.ASSESSMENT]: '评估转移',
} as const

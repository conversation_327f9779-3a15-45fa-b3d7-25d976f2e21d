/**
 * 用户管理相关 API 服务
 */

import api from './api'
import { User, UserRole } from '@/types/auth'

export interface CreateUserData {
  username: string
  email: string
  password: string
  full_name: string
  role: UserR<PERSON>
}

export interface UpdateUserData {
  email?: string
  full_name?: string
  role?: UserRole
  is_active?: boolean
}

export interface UserListResponse {
  items: User[]
  total: number
  skip: number
  limit: number
}

export const usersService = {
  /**
   * 获取用户列表
   */
  async getUsers(skip = 0, limit = 100): Promise<UserListResponse> {
    const response = await api.get('/users', {
      params: { skip, limit },
    })
    return response.data
  },

  /**
   * 根据ID获取用户
   */
  async getUser(userId: string): Promise<User> {
    const response = await api.get(`/users/${userId}`)
    return response.data
  },

  /**
   * 创建用户
   */
  async createUser(data: CreateUserData): Promise<User> {
    const response = await api.post('/users', data)
    return response.data
  },

  /**
   * 更新用户
   */
  async updateUser(userId: string, data: UpdateUserData): Promise<User> {
    const response = await api.put(`/users/${userId}`, data)
    return response.data
  },

  /**
   * 删除用户
   */
  async deleteUser(userId: string): Promise<void> {
    await api.delete(`/users/${userId}`)
  },

  /**
   * 激活用户
   */
  async activateUser(userId: string): Promise<void> {
    await api.post(`/users/${userId}/activate`)
  },

  /**
   * 停用用户
   */
  async deactivateUser(userId: string): Promise<void> {
    await api.post(`/users/${userId}/deactivate`)
  },
}

/**
 * 版本管理服务
 */

import { api } from './api'

export interface VersionBase {
  version_tag?: string
  change_description?: string
}

export interface QuestionVersionCreate extends VersionBase {
  question_id: number
  change_type: ChangeType
  snapshot_data: Record<string, any>
  diff_data?: Record<string, any>
}

export interface KpVersionCreate extends VersionBase {
  kp_id: number
  change_type: ChangeType
  snapshot_data: Record<string, any>
  diff_data?: Record<string, any>
}

export interface QuestionVersionResponse extends VersionBase {
  version_id: number
  question_id: number
  version_number: number
  change_type: ChangeType
  snapshot_data: Record<string, any>
  diff_data?: Record<string, any>
  is_current: boolean
  is_published: boolean
  created_by: number
  created_at: string
  updated_at: string
}

export interface KpVersionResponse extends VersionBase {
  version_id: number
  kp_id: number
  version_number: number
  change_type: ChangeType
  snapshot_data: Record<string, any>
  diff_data?: Record<string, any>
  is_current: boolean
  is_published: boolean
  created_by: number
  created_at: string
  updated_at: string
}

export interface VersionCompareRequest {
  source_version_id: number
  target_version_id: number
}

export interface VersionCompareResponse {
  source_version: QuestionVersionResponse | KpVersionResponse
  target_version: QuestionVersionResponse | KpVersionResponse
  differences: Record<string, any>
}

export interface VersionRollbackRequest {
  target_version_id: number
  rollback_reason: string
}

export interface VersionRollbackResponse {
  success: boolean
  new_version: QuestionVersionResponse | KpVersionResponse
  message: string
}

export interface VersionPublishRequest {
  version_id: number
  publish_note?: string
}

export interface VersionPublishResponse {
  success: boolean
  version: QuestionVersionResponse | KpVersionResponse
  message: string
}

export interface VersionStatsResponse {
  total_versions: number
  published_versions: number
  draft_versions: number
  recent_changes: number
  change_type_stats: Record<string, number>
}

export enum ChangeType {
  CREATE = 0,
  UPDATE = 1,
  DELETE = 2,
  RESTORE = 3
}

export const ChangeTypeLabels = {
  [ChangeType.CREATE]: '创建',
  [ChangeType.UPDATE]: '更新',
  [ChangeType.DELETE]: '删除',
  [ChangeType.RESTORE]: '恢复'
}

export class VersionsService {
  /**
   * 创建题目版本
   */
  static async createQuestionVersion(data: QuestionVersionCreate): Promise<QuestionVersionResponse> {
    const response = await api.post('/versions/questions', data)
    return response.data
  }

  /**
   * 创建知识点版本
   */
  static async createKpVersion(data: KpVersionCreate): Promise<KpVersionResponse> {
    const response = await api.post('/versions/knowledge-points', data)
    return response.data
  }

  /**
   * 获取题目版本列表
   */
  static async getQuestionVersions(
    questionId: number,
    skip: number = 0,
    limit: number = 100
  ): Promise<QuestionVersionResponse[]> {
    const response = await api.get(`/versions/questions/${questionId}`, {
      params: { skip, limit }
    })
    return response.data
  }

  /**
   * 获取知识点版本列表
   */
  static async getKpVersions(
    kpId: number,
    skip: number = 0,
    limit: number = 100
  ): Promise<KpVersionResponse[]> {
    const response = await api.get(`/versions/knowledge-points/${kpId}`, {
      params: { skip, limit }
    })
    return response.data
  }

  /**
   * 根据ID获取题目版本
   */
  static async getQuestionVersionById(versionId: number): Promise<QuestionVersionResponse> {
    const response = await api.get(`/versions/question/${versionId}`)
    return response.data
  }

  /**
   * 根据ID获取知识点版本
   */
  static async getKpVersionById(versionId: number): Promise<KpVersionResponse> {
    const response = await api.get(`/versions/knowledge-point/${versionId}`)
    return response.data
  }

  /**
   * 比较版本
   */
  static async compareVersions(
    request: VersionCompareRequest,
    versionType: 'question' | 'kp' = 'question'
  ): Promise<VersionCompareResponse> {
    const response = await api.post('/versions/compare', request, {
      params: { version_type: versionType }
    })
    return response.data
  }

  /**
   * 回滚版本
   */
  static async rollbackVersion(
    request: VersionRollbackRequest,
    versionType: 'question' | 'kp' = 'question'
  ): Promise<VersionRollbackResponse> {
    const response = await api.post('/versions/rollback', request, {
      params: { version_type: versionType }
    })
    return response.data
  }

  /**
   * 发布版本
   */
  static async publishVersion(
    request: VersionPublishRequest,
    versionType: 'question' | 'kp' = 'question'
  ): Promise<VersionPublishResponse> {
    const response = await api.post('/versions/publish', request, {
      params: { version_type: versionType }
    })
    return response.data
  }

  /**
   * 获取版本统计信息
   */
  static async getVersionStats(): Promise<VersionStatsResponse> {
    const response = await api.get('/versions/stats')
    return response.data
  }
}

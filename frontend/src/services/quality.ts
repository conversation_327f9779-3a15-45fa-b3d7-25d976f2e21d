/**
 * 质量检测相关的API服务
 */

import { api } from './api'

export interface QualityCheckResult {
  timestamp: string
  basic_checks: QualityCheckCategory
  business_rules: QualityCheckCategory
  consistency_checks: QualityCheckCategory
  anomaly_detection: QualityCheckCategory
  quality_score: number
  summary: QualitySummary
}

export interface QualityCheckCategory {
  score: number
  [key: string]: any
}

export interface QualitySummary {
  quality_level: 'excellent' | 'good' | 'fair' | 'poor'
  total_issues: number
  critical_issues: number
  recommendations: string[]
}

export interface QualityDashboardData {
  overview: {
    total_questions: number
    annotated_questions: number
    annotation_coverage: number
    total_mappings: number
    avg_mappings_per_question: number
  }
  recent_activity: {
    recent_annotations: number
    daily_average: number
  }
  user_performance: Array<{
    user_id: number
    annotation_count: number
    avg_confidence: number
  }>
}

export interface QualityIssue {
  category: string
  check_type: string
  severity: 'high' | 'medium' | 'low' | 'none'
  description: string
  details: any
}

export interface QualityReport {
  report_id: string
  generated_at: string
  generated_by: number
  quality_score: number
  quality_level: string
  total_issues: number
  critical_issues: number
  sections: {
    [key: string]: {
      title: string
      score: number
      checks: any
    }
  }
  recommendations: string[]
}

/**
 * 执行质量检测
 */
export const runQualityCheck = async (): Promise<{
  message: string
  results: QualityCheckResult
}> => {
  const response = await api.get('/quality/check')
  return response.data
}

/**
 * 获取质量监控仪表板数据
 */
export const getQualityDashboard = async (): Promise<{
  message: string
  data: QualityDashboardData
}> => {
  const response = await api.get('/quality/dashboard')
  return response.data
}

/**
 * 获取质量统计信息
 */
export const getQualityStats = async (): Promise<{
  message: string
  data: any
}> => {
  const response = await api.get('/quality/stats')
  return response.data
}

/**
 * 生成质量报告
 */
export const generateQualityReport = async (): Promise<{
  message: string
  report: QualityReport
}> => {
  const response = await api.get('/quality/report')
  return response.data
}

/**
 * 获取质量问题列表
 */
export const getQualityIssues = async (params?: {
  severity?: string
  category?: string
  limit?: number
}): Promise<{
  message: string
  total: number
  issues: QualityIssue[]
}> => {
  const response = await api.get('/quality/issues', { params })
  return response.data
}

/**
 * 修复质量问题
 */
export const fixQualityIssue = async (
  issueType: string,
  issueData: any
): Promise<{
  message: string
  issue_data: any
  status: string
}> => {
  const response = await api.post(`/quality/fix/${issueType}`, issueData)
  return response.data
}

/**
 * 获取质量等级的颜色
 */
export const getQualityLevelColor = (level: string): string => {
  switch (level) {
    case 'excellent':
      return 'text-green-600'
    case 'good':
      return 'text-blue-600'
    case 'fair':
      return 'text-yellow-600'
    case 'poor':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

/**
 * 获取质量等级的中文名称
 */
export const getQualityLevelName = (level: string): string => {
  switch (level) {
    case 'excellent':
      return '优秀'
    case 'good':
      return '良好'
    case 'fair':
      return '一般'
    case 'poor':
      return '较差'
    default:
      return '未知'
  }
}

/**
 * 获取严重程度的颜色
 */
export const getSeverityColor = (severity: string): string => {
  switch (severity) {
    case 'high':
      return 'text-red-600 bg-red-50'
    case 'medium':
      return 'text-yellow-600 bg-yellow-50'
    case 'low':
      return 'text-blue-600 bg-blue-50'
    default:
      return 'text-gray-600 bg-gray-50'
  }
}

/**
 * 获取严重程度的中文名称
 */
export const getSeverityName = (severity: string): string => {
  switch (severity) {
    case 'high':
      return '严重'
    case 'medium':
      return '中等'
    case 'low':
      return '轻微'
    case 'none':
      return '无'
    default:
      return '未知'
  }
}

/**
 * 知识点管理相关的API服务
 */

import { api } from './api'

export interface KnowledgePoint {
  kpId: number
  name: string
  code: string
  description?: string
  difficultyLevel?: number
  parentId?: number
  path: string
  isLeaf: boolean
  createdBy: number
  createdAt: string
  updatedAt: string
}

export interface KnowledgePointCreateRequest {
  name: string
  code: string
  description?: string
  difficultyLevel?: number
  parentId?: number
}

export interface KnowledgePointUpdateRequest {
  name?: string
  code?: string
  description?: string
  difficultyLevel?: number
  parentId?: number
}

export interface KnowledgePointListResponse {
  items: KnowledgePoint[]
  total: number
  skip: number
  limit: number
}

export interface KnowledgePointTreeNode {
  kpId: number
  name: string
  code: string
  description?: string
  difficultyLevel?: number
  isLeaf: boolean
  children: KnowledgePointTreeNode[]
}

export interface KnowledgePointTreeResponse {
  tree: KnowledgePointTreeNode[]
}

export interface KnowledgePointFilters {
  search?: string
  subject?: string
  parentId?: string
  isLeaf?: boolean
  difficultyLevel?: number
}

export interface PrerequisiteRelation {
  preKpId: number
  postKpId: number
  source: number
  confidence: number
  preKp?: KnowledgePoint
  postKp?: KnowledgePoint
}

/**
 * 知识点管理API服务类
 */
export class KnowledgePointsService {
  /**
   * 获取知识点列表
   */
  static async getKnowledgePoints(
    skip = 0,
    limit = 20,
    filters: KnowledgePointFilters = {}
  ): Promise<KnowledgePointListResponse> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    })

    if (filters.search) params.append('search', filters.search)
    if (filters.subject) params.append('subject', filters.subject)
    if (filters.parentId) params.append('parent_id', filters.parentId)
    if (filters.isLeaf !== undefined) params.append('is_leaf', filters.isLeaf.toString())
    if (filters.difficultyLevel) params.append('difficulty_level', filters.difficultyLevel.toString())

    const response = await api.get(`/knowledge-points?${params}`)
    return response.data
  }

  /**
   * 获取知识点树形结构
   */
  static async getKnowledgeTree(subject?: string): Promise<KnowledgePointTreeResponse> {
    const params = subject ? `?subject=${encodeURIComponent(subject)}` : ''
    const response = await api.get(`/knowledge-points/tree${params}`)
    return response.data
  }

  /**
   * 根据ID获取知识点详情
   */
  static async getKnowledgePoint(id: number): Promise<KnowledgePoint> {
    const response = await api.get(`/knowledge-points/${id}`)
    return response.data
  }

  /**
   * 创建新知识点
   */
  static async createKnowledgePoint(data: KnowledgePointCreateRequest): Promise<KnowledgePoint> {
    const response = await api.post('/knowledge-points', data)
    return response.data
  }

  /**
   * 更新知识点
   */
  static async updateKnowledgePoint(id: number, data: KnowledgePointUpdateRequest): Promise<KnowledgePoint> {
    const response = await api.put(`/knowledge-points/${id}`, data)
    return response.data
  }

  /**
   * 删除知识点
   */
  static async deleteKnowledgePoint(id: number): Promise<void> {
    await api.delete(`/knowledge-points/${id}`)
  }

  /**
   * 获取知识点的子知识点
   */
  static async getKnowledgePointChildren(id: number): Promise<KnowledgePoint[]> {
    const response = await api.get(`/knowledge-points/${id}/children`)
    return response.data.children
  }

  /**
   * 获取知识点的先修知识点
   */
  static async getKnowledgePointPrerequisites(id: number): Promise<PrerequisiteRelation[]> {
    const response = await api.get(`/knowledge-points/${id}/prerequisites`)
    return response.data.prerequisites
  }

  /**
   * 验证知识点数据
   */
  static validateKnowledgePoint(data: Partial<KnowledgePointCreateRequest>): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!data.name?.trim()) {
      errors.push('知识点名称不能为空')
    }

    if (!data.code?.trim()) {
      errors.push('知识点编码不能为空')
    }

    if (data.code && !/^[A-Za-z0-9_-]+$/.test(data.code)) {
      errors.push('知识点编码只能包含字母、数字、下划线和连字符')
    }

    if (data.difficultyLevel !== undefined && (data.difficultyLevel < 1 || data.difficultyLevel > 5)) {
      errors.push('难度等级必须在1-5之间')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 获取难度等级选项
   */
  static getDifficultyLevels(): Array<{ value: number; label: string }> {
    return [
      { value: 1, label: '非常简单' },
      { value: 2, label: '简单' },
      { value: 3, label: '中等' },
      { value: 4, label: '困难' },
      { value: 5, label: '非常困难' }
    ]
  }

  /**
   * 构建知识点路径显示
   */
  static buildPathDisplay(path: string): string {
    return path.split('.').join(' > ')
  }

  /**
   * 检查是否可以设置为父节点（避免循环）
   */
  static canSetAsParent(currentId: number, targetParentId: number, tree: KnowledgePointTreeNode[]): boolean {
    // 简化实现：检查targetParentId是否是currentId的后代
    const findNode = (nodes: KnowledgePointTreeNode[], id: number): KnowledgePointTreeNode | null => {
      for (const node of nodes) {
        if (node.kpId === id) return node
        const found = findNode(node.children, id)
        if (found) return found
      }
      return null
    }

    const isDescendant = (node: KnowledgePointTreeNode, ancestorId: number): boolean => {
      if (node.kpId === ancestorId) return true
      return node.children.some(child => isDescendant(child, ancestorId))
    }

    const currentNode = findNode(tree, currentId)
    if (!currentNode) return true

    return !isDescendant(currentNode, targetParentId)
  }
}

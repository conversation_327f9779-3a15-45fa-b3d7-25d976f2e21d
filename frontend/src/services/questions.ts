/**
 * 题目管理相关的API服务
 */

import { api } from './api'
import type { Question, QuestionContent } from '@/types'

export interface QuestionCreateRequest {
  content: QuestionContent
  qType: number
  difficultyLvl?: number
  answerKey?: any
  analysis?: string
  source?: string
  tags?: string[]
}

export interface QuestionUpdateRequest {
  content?: QuestionContent
  qType?: number
  difficultyLvl?: number
  answerKey?: any
  analysis?: string
  source?: string
  tags?: string[]
  isActive?: boolean
}

export interface QuestionListResponse {
  items: Question[]
  total: number
  skip: number
  limit: number
}

export interface QuestionImportItem {
  content: QuestionContent
  qType: number
  difficultyLvl?: number
  answerKey?: any
  analysis?: string
  source?: string
  tags?: string[]
}

export interface QuestionImportRequest {
  questions: QuestionImportItem[]
  overwriteExisting: boolean
}

export interface QuestionImportResponse {
  successCount: number
  errorCount: number
  errors: string[]
  createdQuestions: number[]
}

export interface QuestionFilters {
  search?: string
  subject?: string
  difficulty?: string
  qType?: string
  irtReady?: boolean
}

/**
 * 题目管理API服务类
 */
export class QuestionsService {
  /**
   * 获取题目列表
   */
  static async getQuestions(
    skip = 0,
    limit = 20,
    filters: QuestionFilters = {}
  ): Promise<QuestionListResponse> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    })

    if (filters.search) params.append('search', filters.search)
    if (filters.subject) params.append('subject', filters.subject)
    if (filters.difficulty) params.append('difficulty', filters.difficulty)
    if (filters.qType) params.append('q_type', filters.qType)
    if (filters.irtReady !== undefined) params.append('irt_ready', filters.irtReady.toString())

    const response = await api.get(`/questions?${params}`)
    return response.data
  }

  /**
   * 根据ID获取题目详情
   */
  static async getQuestion(id: string): Promise<Question> {
    const response = await api.get(`/questions/${id}`)
    return response.data
  }

  /**
   * 创建新题目
   */
  static async createQuestion(data: QuestionCreateRequest): Promise<Question> {
    const response = await api.post('/questions', data)
    return response.data
  }

  /**
   * 更新题目
   */
  static async updateQuestion(id: string, data: QuestionUpdateRequest): Promise<Question> {
    const response = await api.put(`/questions/${id}`, data)
    return response.data
  }

  /**
   * 删除题目
   */
  static async deleteQuestion(id: string): Promise<void> {
    await api.delete(`/questions/${id}`)
  }

  /**
   * 获取题目关联的知识点
   */
  static async getQuestionKnowledgePoints(id: string): Promise<any[]> {
    const response = await api.get(`/questions/${id}/knowledge-points`)
    return response.data.knowledge_points
  }

  /**
   * 批量导入题目
   */
  static async importQuestions(data: QuestionImportRequest): Promise<QuestionImportResponse> {
    const response = await api.post('/questions/import', data)
    return response.data
  }

  /**
   * 导出题目
   */
  static async exportQuestions(questionIds?: number[]): Promise<Blob> {
    const params = questionIds ? { question_ids: questionIds } : {}
    const response = await api.post('/questions/export', params, {
      responseType: 'blob'
    })
    return response.data
  }

  /**
   * 获取所有学科列表
   */
  static async getSubjects(): Promise<string[]> {
    const response = await api.get('/questions/subjects/')
    return response.data.subjects
  }

  /**
   * 获取所有难度级别
   */
  static async getDifficulties(): Promise<number[]> {
    const response = await api.get('/questions/difficulties/')
    return response.data.difficulties
  }

  /**
   * 获取题目类型列表
   */
  static getQuestionTypes(): Array<{ value: number; label: string }> {
    return [
      { value: 0, label: '单选题' },
      { value: 1, label: '多选题' },
      { value: 2, label: '判断题' },
      { value: 3, label: '填空题' },
      { value: 4, label: '简答题' },
      { value: 5, label: '论述题' },
      { value: 6, label: '匹配题' },
      { value: 7, label: '排序题' },
      { value: 8, label: '计算题' },
      { value: 9, label: '编程题' },
      { value: 10, label: '复合题' }
    ]
  }

  /**
   * 验证题目数据
   */
  static validateQuestion(data: Partial<QuestionCreateRequest>): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!data.content?.stem?.trim()) {
      errors.push('题干不能为空')
    }

    if (data.qType === undefined || data.qType < 0 || data.qType > 10) {
      errors.push('请选择有效的题目类型')
    }

    // 选择题必须有选项
    if ((data.qType === 0 || data.qType === 1) && (!data.content?.options || data.content.options.length === 0)) {
      errors.push('选择题必须设置选项')
    }

    if (data.difficultyLvl !== undefined && (data.difficultyLvl < 1 || data.difficultyLvl > 5)) {
      errors.push('难度等级必须在1-5之间')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

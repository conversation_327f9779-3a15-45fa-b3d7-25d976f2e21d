/**
 * 标注相关的API服务
 */

import { api } from './api'
import type { Question } from '@/types'
import type { KnowledgePoint } from './knowledge-points'

export interface ItemKpMapping {
  questionId: number
  kpId: number
  isRequired: boolean
  weight: number
  confidence: number
  source: number
  createdBy: number
  createdAt: string
  updatedAt: string
  question?: Question
  knowledgePoint?: KnowledgePoint
}

export interface ItemKpMappingCreateRequest {
  questionId: number
  kpId: number
  isRequired: boolean
  weight: number
  confidence: number
  source: number
}

export interface ItemKpMappingUpdateRequest {
  isRequired?: boolean
  weight?: number
  confidence?: number
  source?: number
}

export interface ItemKpMappingListResponse {
  items: ItemKpMapping[]
  total: number
  skip: number
  limit: number
}

export interface BatchAnnotationRequest {
  questionIds: number[]
  kpId: number
  isRequired: boolean
  weight: number
  confidence: number
  source: number
}

export interface BatchAnnotationResponse {
  successCount: number
  errorCount: number
  errors: string[]
}

export interface AnnotationStats {
  totalQuestions: number
  annotatedQuestions: number
  totalMappings: number
  avgMappingsPerQuestion: number
  annotationCoverage: number
}

export interface QMatrix {
  questions: Question[]
  knowledgePoints: KnowledgePoint[]
  matrix: number[][]
  metadata: {
    questionCount: number
    kpCount: number
    mappingCount: number
    generatedAt: string
  }
}

/**
 * 标注管理API服务类
 */
export class AnnotationService {
  /**
   * 获取题目-知识点映射列表
   */
  static async getMappings(
    skip = 0,
    limit = 20,
    questionId?: number,
    kpId?: number
  ): Promise<ItemKpMappingListResponse> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    })

    if (questionId) params.append('question_id', questionId.toString())
    if (kpId) params.append('kp_id', kpId.toString())

    const response = await api.get(`/annotation/mappings?${params}`)
    return response.data
  }

  /**
   * 创建题目-知识点映射
   */
  static async createMapping(data: ItemKpMappingCreateRequest): Promise<ItemKpMapping> {
    const response = await api.post('/annotation/mappings', data)
    return response.data
  }

  /**
   * 更新题目-知识点映射
   */
  static async updateMapping(
    questionId: number,
    kpId: number,
    data: ItemKpMappingUpdateRequest
  ): Promise<ItemKpMapping> {
    const response = await api.put(`/annotation/mappings/${questionId}/${kpId}`, data)
    return response.data
  }

  /**
   * 删除题目-知识点映射
   */
  static async deleteMapping(questionId: number, kpId: number): Promise<void> {
    await api.delete(`/annotation/mappings/${questionId}/${kpId}`)
  }

  /**
   * 批量标注
   */
  static async batchAnnotate(data: BatchAnnotationRequest): Promise<BatchAnnotationResponse> {
    const response = await api.post('/annotation/batch-annotate', data)
    return response.data
  }

  /**
   * 获取标注统计信息
   */
  static async getAnnotationStats(): Promise<AnnotationStats> {
    const response = await api.get('/annotation/stats')
    return response.data
  }

  /**
   * 获取Q矩阵
   */
  static async getQMatrix(): Promise<QMatrix> {
    const response = await api.get('/annotation/q-matrix')
    return response.data
  }

  /**
   * 导出Q矩阵
   */
  static async exportQMatrix(): Promise<Blob> {
    const response = await api.get('/annotation/q-matrix', {
      responseType: 'blob'
    })
    return response.data
  }

  /**
   * 获取标注来源选项
   */
  static getAnnotationSources(): Array<{ value: number; label: string }> {
    return [
      { value: 0, label: '专家标注' },
      { value: 1, label: '算法推断' },
      { value: 2, label: '数据导入' }
    ]
  }

  /**
   * 验证映射数据
   */
  static validateMapping(data: Partial<ItemKpMappingCreateRequest>): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!data.questionId) {
      errors.push('请选择题目')
    }

    if (!data.kpId) {
      errors.push('请选择知识点')
    }

    if (data.weight !== undefined && (data.weight < 0 || data.weight > 10)) {
      errors.push('权重必须在0-10之间')
    }

    if (data.confidence !== undefined && (data.confidence < 0 || data.confidence > 1)) {
      errors.push('置信度必须在0-1之间')
    }

    if (data.source !== undefined && (data.source < 0 || data.source > 2)) {
      errors.push('请选择有效的标注来源')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 计算标注覆盖率
   */
  static calculateCoverage(totalQuestions: number, annotatedQuestions: number): number {
    if (totalQuestions === 0) return 0
    return Math.round((annotatedQuestions / totalQuestions) * 100)
  }

  /**
   * 格式化置信度显示
   */
  static formatConfidence(confidence: number): string {
    return `${Math.round(confidence * 100)}%`
  }

  /**
   * 获取权重颜色
   */
  static getWeightColor(weight: number): string {
    if (weight >= 0.8) return 'text-green-600'
    if (weight >= 0.5) return 'text-yellow-600'
    return 'text-red-600'
  }

  /**
   * 获取置信度颜色
   */
  static getConfidenceColor(confidence: number): string {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }
}

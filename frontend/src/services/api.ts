/**
 * API 服务配置
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { toast } from '@/components/ui/use-toast'

// API 基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证 token
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // 处理认证错误
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      window.location.href = '/login'
      return Promise.reject(error)
    }

    // 处理权限错误
    if (error.response?.status === 403) {
      toast({
        title: '权限不足',
        description: error.response.data?.detail || '您没有权限执行此操作',
        variant: 'destructive',
      })
      return Promise.reject(error)
    }

    // 处理其他错误
    if (error.response?.status >= 400) {
      toast({
        title: '请求失败',
        description: error.response.data?.detail || '请求失败，请稍后重试',
        variant: 'destructive',
      })
    }

    return Promise.reject(error)
  }
)

export { api }
export default api

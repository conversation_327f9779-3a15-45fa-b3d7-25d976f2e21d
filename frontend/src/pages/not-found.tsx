/**
 * 404页面
 */

import { But<PERSON> } from '@/components/ui/button'
import { useNavigate } from 'react-router-dom'

export function NotFoundPage() {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900">404</h1>
        <p className="text-xl text-gray-600 mt-4">页面未找到</p>
        <p className="text-gray-500 mt-2">抱歉，您访问的页面不存在。</p>
        <Button 
          onClick={() => navigate('/dashboard')} 
          className="mt-6"
        >
          返回首页
        </Button>
      </div>
    </div>
  )
}

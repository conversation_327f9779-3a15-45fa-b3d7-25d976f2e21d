/**
 * 知识点管理页面
 */

import { useState } from 'react'
import { KnowledgePointList } from '@/components/knowledge-points/knowledge-point-list'
import { KnowledgePointForm } from '@/components/knowledge-points/knowledge-point-form'
import { KnowledgePointTree } from '@/components/knowledge-points/knowledge-point-tree'
import type { KnowledgePoint } from '@/services/knowledge-points'

type ViewMode = 'list' | 'tree' | 'form'

export function KnowledgePointsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedKnowledgePoint, setSelectedKnowledgePoint] = useState<KnowledgePoint | undefined>()
  const [parentId, setParentId] = useState<number | undefined>()
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleCreateNew = (newParentId?: number) => {
    setSelectedKnowledgePoint(undefined)
    setParentId(newParentId)
    setViewMode('form')
  }

  const handleEdit = (kp: KnowledgePoint) => {
    setSelectedKnowledgePoint(kp)
    setParentId(undefined)
    setViewMode('form')
  }

  const handleView = (kp: KnowledgePoint) => {
    // 这里可以实现详情查看功能
    console.log('查看知识点:', kp)
  }

  const handleDelete = async (kp: KnowledgePoint) => {
    if (confirm(`确定要删除知识点"${kp.name}"吗？`)) {
      try {
        // 这里应该调用删除API
        console.log('删除知识点:', kp.kpId)
        setRefreshTrigger(prev => prev + 1)
      } catch (error) {
        console.error('删除失败:', error)
      }
    }
  }

  const handleSave = (kp: KnowledgePoint) => {
    setViewMode('list')
    setSelectedKnowledgePoint(undefined)
    setParentId(undefined)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleCancel = () => {
    setViewMode('list')
    setSelectedKnowledgePoint(undefined)
    setParentId(undefined)
  }

  const handleViewTree = () => {
    setViewMode('tree')
  }

  const handleBackToList = () => {
    setViewMode('list')
  }

  // 如果有parentId，需要预设表单的父级知识点
  const formKnowledgePoint = selectedKnowledgePoint || (parentId ? {
    ...({} as KnowledgePoint),
    parentId
  } : undefined)

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">知识点管理</h1>
        <p className="text-muted-foreground">
          管理知识点层级结构和先修关系
        </p>
      </div>

      {viewMode === 'list' && (
        <KnowledgePointList
          key={refreshTrigger}
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onView={handleView}
          onDelete={handleDelete}
          onViewTree={handleViewTree}
        />
      )}

      {viewMode === 'tree' && (
        <KnowledgePointTree
          key={refreshTrigger}
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBackToList={handleBackToList}
        />
      )}

      {viewMode === 'form' && (
        <KnowledgePointForm
          knowledgePoint={formKnowledgePoint}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      )}
    </div>
  )
}

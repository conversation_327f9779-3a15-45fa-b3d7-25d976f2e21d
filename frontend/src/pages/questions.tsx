/**
 * 题目管理页面
 */

import { useState } from 'react'
import { QuestionList } from '@/components/questions/question-list'
import { QuestionForm } from '@/components/questions/question-form'
import { QuestionDetail } from '@/components/questions/question-detail'
import type { Question } from '@/types'

type ViewMode = 'list' | 'form' | 'detail'

export function QuestionsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedQuestion, setSelectedQuestion] = useState<Question | undefined>()
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleCreateNew = () => {
    setSelectedQuestion(undefined)
    setViewMode('form')
  }

  const handleEdit = (question: Question) => {
    setSelectedQuestion(question)
    setViewMode('form')
  }

  const handleView = (question: Question) => {
    setSelectedQuestion(question)
    setViewMode('detail')
  }

  const handleDelete = async (question: Question) => {
    if (confirm(`确定要删除题目"${question.content.stem.substring(0, 50)}..."吗？`)) {
      try {
        // 这里应该调用删除API
        console.log('删除题目:', question.questionId)
        setRefreshTrigger(prev => prev + 1)
      } catch (error) {
        console.error('删除失败:', error)
      }
    }
  }

  const handleSave = (question: Question) => {
    setViewMode('list')
    setSelectedQuestion(undefined)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleCancel = () => {
    setViewMode('list')
    setSelectedQuestion(undefined)
  }

  const handleClose = () => {
    setViewMode('list')
    setSelectedQuestion(undefined)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">题目管理</h1>
        <p className="text-muted-foreground">
          管理和编辑题目信息
        </p>
      </div>

      {viewMode === 'list' && (
        <QuestionList
          key={refreshTrigger}
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onView={handleView}
          onDelete={handleDelete}
        />
      )}

      {viewMode === 'form' && (
        <QuestionForm
          question={selectedQuestion}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      )}

      {viewMode === 'detail' && selectedQuestion && (
        <QuestionDetail
          question={selectedQuestion}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onClose={handleClose}
        />
      )}
    </div>
  )
}

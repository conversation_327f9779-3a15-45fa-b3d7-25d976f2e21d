/**
 * 用户管理页面
 */

import { useEffect, useState } from 'react'
import { Plus, Search, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from '@/components/ui/use-toast'
import { UserTable } from '@/components/users/user-table'
import { UserForm } from '@/components/users/user-form'
import { useUsersStore } from '@/stores/users'
import { User } from '@/types/auth'
import { CreateUserData, UpdateUserData } from '@/services/users'

export function UsersPage() {
  const {
    users,
    total,
    loading,
    error,
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    activateUser,
    deactivateUser,
    clearError,
  } = useUsersStore()

  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [deletingUser, setDeletingUser] = useState<User | null>(null)

  // 过滤用户列表
  const filteredUsers = users.filter((user) =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  useEffect(() => {
    if (error) {
      toast({
        title: '操作失败',
        description: error,
        variant: 'destructive',
      })
      clearError()
    }
  }, [error, clearError])

  const handleCreateUser = async (data: CreateUserData) => {
    try {
      await createUser(data)
      setShowCreateDialog(false)
      toast({
        title: '创建成功',
        description: '用户创建成功',
      })
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleUpdateUser = async (data: UpdateUserData) => {
    if (!editingUser) return

    try {
      await updateUser(editingUser.user_id.toString(), data)
      setEditingUser(null)
      toast({
        title: '更新成功',
        description: '用户信息更新成功',
      })
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleDeleteUser = async () => {
    if (!deletingUser) return

    try {
      await deleteUser(deletingUser.user_id.toString())
      setDeletingUser(null)
      toast({
        title: '删除成功',
        description: '用户删除成功',
      })
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleActivateUser = async (user: User) => {
    try {
      await activateUser(user.user_id.toString())
      toast({
        title: '激活成功',
        description: `用户 ${user.username} 已激活`,
      })
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleDeactivateUser = async (user: User) => {
    try {
      await deactivateUser(user.user_id.toString())
      toast({
        title: '停用成功',
        description: `用户 ${user.username} 已停用`,
      })
    } catch (error) {
      // 错误已在store中处理
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
        <p className="text-muted-foreground">
          管理系统用户和权限
        </p>
      </div>

      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索用户..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-[300px]"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchUsers()}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          添加用户
        </Button>
      </div>

      {/* 用户统计 */}
      <div className="text-sm text-muted-foreground">
        共 {total} 个用户，显示 {filteredUsers.length} 个
      </div>

      {/* 用户表格 */}
      <UserTable
        users={filteredUsers}
        onEdit={setEditingUser}
        onDelete={setDeletingUser}
        onActivate={handleActivateUser}
        onDeactivate={handleDeactivateUser}
      />

      {/* 创建用户对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>创建用户</DialogTitle>
          </DialogHeader>
          <UserForm
            onSubmit={handleCreateUser}
            onCancel={() => setShowCreateDialog(false)}
            loading={loading}
          />
        </DialogContent>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑用户</DialogTitle>
          </DialogHeader>
          {editingUser && (
            <UserForm
              user={editingUser}
              onSubmit={handleUpdateUser}
              onCancel={() => setEditingUser(null)}
              loading={loading}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={!!deletingUser} onOpenChange={() => setDeletingUser(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除用户 "{deletingUser?.username}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

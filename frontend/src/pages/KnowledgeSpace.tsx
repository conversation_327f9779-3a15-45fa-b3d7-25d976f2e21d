/**
 * 知识空间管理页面
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Play, Trash2, Eye, Network, BarChart3 } from 'lucide-react'
import { simpleToast as toast } from '@/hooks/use-toast'

import {
  KnowledgeSpaceService,
  KnowledgeSpaceOverview,
  KnowledgeSpaceBuildRequest,
  StateType,
  StateTypeLabels,
  TransitionType,
  TransitionTypeLabels
} from '@/services/knowledge-space'
import { KnowledgePointsService } from '@/services/knowledge-points'

const KnowledgeSpace: React.FC = () => {
  const [overview, setOverview] = useState<KnowledgeSpaceOverview | null>(null)
  const [loading, setLoading] = useState(true)
  const [building, setBuilding] = useState(false)
  const [clearing, setClearing] = useState(false)

  // 加载知识空间概览
  const loadOverview = async () => {
    try {
      setLoading(true)
      const data = await KnowledgeSpaceService.getKnowledgeSpaceOverview()
      setOverview(data)
    } catch (error) {
      console.error('加载知识空间概览失败:', error)
      toast.error('加载知识空间概览失败')
    } finally {
      setLoading(false)
    }
  }

  // 构建知识空间
  const buildKnowledgeSpace = async (forceRebuild = false) => {
    try {
      setBuilding(true)
      
      const request: KnowledgeSpaceBuildRequest = {
        force_rebuild: forceRebuild,
        include_invalid_states: false
      }
      
      const result = await KnowledgeSpaceService.buildKnowledgeSpace(request)
      
      if (result.success) {
        toast.success(result.message)
        await loadOverview() // 重新加载概览
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error('构建知识空间失败:', error)
      toast.error('构建知识空间失败')
    } finally {
      setBuilding(false)
    }
  }

  // 清除知识空间
  const clearKnowledgeSpace = async () => {
    if (!confirm('确定要清除所有知识空间数据吗？此操作不可撤销。')) {
      return
    }

    try {
      setClearing(true)
      await KnowledgeSpaceService.clearKnowledgeSpace()
      toast.success('知识空间已清除')
      await loadOverview() // 重新加载概览
    } catch (error) {
      console.error('清除知识空间失败:', error)
      toast.error('清除知识空间失败')
    } finally {
      setClearing(false)
    }
  }

  useEffect(() => {
    loadOverview()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">知识空间管理</h1>
          <p className="text-muted-foreground mt-2">
            基于知识空间理论构建和管理知识结构
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={() => buildKnowledgeSpace(false)}
            disabled={building}
            className="flex items-center gap-2"
          >
            {building ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            构建知识空间
          </Button>
          
          <Button
            variant="outline"
            onClick={() => buildKnowledgeSpace(true)}
            disabled={building}
          >
            强制重建
          </Button>
          
          <Button
            variant="destructive"
            onClick={clearKnowledgeSpace}
            disabled={clearing}
            className="flex items-center gap-2"
          >
            {clearing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            清除空间
          </Button>
        </div>
      </div>

      {/* 统计信息卡片 */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">知识点数量</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.stats.total_knowledge_points}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">知识状态数量</CardTitle>
              <Network className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.stats.total_states}</div>
              <p className="text-xs text-muted-foreground">
                有效状态: {overview.stats.valid_states}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">状态转移数量</CardTitle>
              <Network className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.stats.total_transitions}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">特殊状态</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="text-sm">
                  空状态: {overview.stats.empty_state_id ? `#${overview.stats.empty_state_id}` : '无'}
                </div>
                <div className="text-sm">
                  满状态: {overview.stats.full_state_id ? `#${overview.stats.full_state_id}` : '无'}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 主要内容区域 */}
      {overview ? (
        <Tabs defaultValue="states" className="space-y-4">
          <TabsList>
            <TabsTrigger value="states">知识状态</TabsTrigger>
            <TabsTrigger value="transitions">状态转移</TabsTrigger>
            <TabsTrigger value="knowledge-points">知识点</TabsTrigger>
          </TabsList>

          <TabsContent value="states" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>知识状态列表</CardTitle>
                <CardDescription>
                  显示所有生成的知识状态及其属性
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {overview.states.slice(0, 10).map((state) => (
                    <div
                      key={state.state_id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-4">
                        <div className="font-mono text-sm">
                          #{state.state_id}
                        </div>
                        <Badge variant={state.is_valid ? "default" : "destructive"}>
                          {StateTypeLabels[state.state_type as keyof typeof StateTypeLabels]}
                        </Badge>
                        <div className="text-sm text-muted-foreground">
                          掌握率: {(state.mastery_ratio * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ({state.mastery_count}/{state.total_count})
                        </div>
                      </div>
                      <div className="font-mono text-xs bg-muted px-2 py-1 rounded">
                        {state.state_vector.map(v => v ? '1' : '0').join('')}
                      </div>
                    </div>
                  ))}
                  
                  {overview.states.length > 10 && (
                    <div className="text-center text-muted-foreground">
                      还有 {overview.states.length - 10} 个状态...
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transitions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>状态转移列表</CardTitle>
                <CardDescription>
                  显示知识状态之间的转移关系
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {overview.transitions.slice(0, 10).map((transition) => (
                    <div
                      key={transition.transition_id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-4">
                        <div className="font-mono text-sm">
                          #{transition.from_state_id} → #{transition.to_state_id}
                        </div>
                        <Badge>
                          {TransitionTypeLabels[transition.transition_type as keyof typeof TransitionTypeLabels]}
                        </Badge>
                        <div className="text-sm text-muted-foreground">
                          概率: {(transition.probability * 100).toFixed(1)}%
                        </div>
                        {transition.trigger_kp_id && (
                          <div className="text-sm text-muted-foreground">
                            触发知识点: #{transition.trigger_kp_id}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {overview.transitions.length > 10 && (
                    <div className="text-center text-muted-foreground">
                      还有 {overview.transitions.length - 10} 个转移...
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="knowledge-points" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>相关知识点</CardTitle>
                <CardDescription>
                  参与知识空间构建的知识点列表
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {overview.knowledge_points.map((kp) => (
                    <div
                      key={kp.kp_id}
                      className="p-4 border rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium">{kp.name}</div>
                        <Badge variant={kp.is_leaf ? "default" : "secondary"}>
                          {kp.is_leaf ? "叶子节点" : "父节点"}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">
                        编码: {kp.code}
                      </div>
                      {kp.description && (
                        <div className="text-sm text-muted-foreground">
                          {kp.description}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <Alert>
          <AlertDescription>
            暂无知识空间数据，请先构建知识空间。
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

export default KnowledgeSpace

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  fullName: string
  role: UserRole
  createdAt: string
  lastLogin?: string
}

export type UserRole = 'admin' | 'annotator' | 'reviewer' | 'viewer' | 'engine'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  tokenType: string
  user: User
}

// 知识点相关类型
export interface KnowledgePoint {
  kpId: number
  parentId?: number
  name: string
  code: string
  path: string
  description?: string
  difficultyLevel?: number
  isLeaf: boolean
  createdBy: number
  createdAt: string
  updatedAt: string
  children?: KnowledgePoint[]
}

export interface PrerequisiteRelation {
  preKpId: number
  postKpId: number
  source: 0 | 1 | 2 // 0=expert, 1=algorithm, 2=import
  confidence: number
  createdBy: string
  createdAt: string
}

// 题目相关类型
export interface Question {
  questionId: number
  content: QuestionContent
  qType: QuestionType
  difficultyLvl?: number
  irtReady: boolean
  answerKey?: any
  analysis?: string
  source?: string
  tags?: string[]
  isActive: boolean
  createdBy: number
  createdAt: string
  updatedAt: string
}

export interface QuestionContent {
  stem: string
  options?: string[]
  passages?: string[]
  mediaUrls?: string[]
}

export type QuestionType = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10

export interface QuestionAsset {
  id: string
  questionId: string
  uri: string
  mediaType: string
  altText?: string
}

export interface ItemParam {
  questionId: string
  a: number // 区分度
  b: number // 难度
  c: number // 猜测率
  lastCalibrated: string
}

// 标注相关类型
export interface ItemKpMap {
  itemId: string
  kpId: number
  relationType: 0 | 1 // 0=assess, 1=require
  confidence: number
}

export interface QuestionRelation {
  srcQId: string
  dstQId: string
  relType: QuestionRelationType
  confidence: number
  createdBy: string
  createdAt: string
}

export type QuestionRelationType = 
  | 'complements'
  | 'progresses_to'
  | 'equivalent'
  | 'prerequisite'
  | 'revision'

// 标注任务相关类型
export interface AnnotationTask {
  id: string
  taskType: 0 | 1 | 2 // 0=Q-KP, 1=Q-Rel, 2=Prereq
  payload: any
  assignees: string[]
  reviewerId?: string
  state: TaskState
  createdBy: string
  createdAt: string
  updatedAt: string
}

export type TaskState = 'pending' | 'in_progress' | 'review' | 'done'

export interface AnnotationLog {
  id: string
  taskId: string
  questionId?: string
  operation: string
  detail: any
  operatorId: string
  ts: string
}

// 知识空间相关类型
export interface KnowledgeState {
  id: string
  stateVector: string
  isMinimal?: boolean
  isMaximal?: boolean
  origin: 0 | 1 | 2 // 0=alg闭包, 1=observed, 2=expert
  createdAt: string
}

export interface StateTransition {
  fromState: string
  toState: string
  learnedKp: number
  edgeWeight?: number
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 表单相关类型
export interface SearchFilters {
  keyword?: string
  qType?: QuestionType
  difficultyLvl?: number
  kpIds?: number[]
  isActive?: boolean
  dateRange?: [string, string]
}

export interface AnnotationFilters {
  taskType?: number
  state?: TaskState
  assigneeId?: string
  dateRange?: [string, string]
}

// 统计相关类型
export interface DashboardStats {
  totalQuestions: number
  totalKnowledgePoints: number
  totalAnnotationTasks: number
  completedTasks: number
  pendingTasks: number
  annotationProgress: number
}

// 错误类型
export interface ApiError {
  message: string
  code?: string
  details?: any
}

// 主题相关类型
export type Theme = 'dark' | 'light' | 'system'

// 导出所有类型
export * from './auth'
export * from './api'

// API 基础类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  timestamp?: string
}

export interface ApiError {
  message: string
  code?: string
  details?: Record<string, any>
  timestamp?: string
}

export interface PaginationParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// 请求配置
export interface RequestConfig {
  method?: HttpMethod
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
  withCredentials?: boolean
}

// API 端点配置
export interface ApiEndpoint {
  url: string
  method: HttpMethod
  requiresAuth?: boolean
  timeout?: number
}

// 文件上传相关
export interface FileUploadResponse {
  url: string
  filename: string
  size: number
  mimeType: string
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

// 批量操作
export interface BatchOperation<T> {
  operation: 'create' | 'update' | 'delete'
  items: T[]
}

export interface BatchResponse<T> {
  successful: T[]
  failed: Array<{
    item: T
    error: string
  }>
  totalProcessed: number
  successCount: number
  failureCount: number
}

// 搜索和过滤
export interface SearchParams {
  q?: string
  filters?: Record<string, any>
  pagination?: PaginationParams
}

export interface FilterOption {
  label: string
  value: string | number
  count?: number
}

// 导出相关
export interface ExportRequest {
  format: 'csv' | 'xlsx' | 'json'
  filters?: Record<string, any>
  fields?: string[]
}

export interface ExportResponse {
  downloadUrl: string
  filename: string
  expiresAt: string
}

// 导入相关
export interface ImportRequest {
  file: File
  options?: Record<string, any>
}

export interface ImportResponse {
  jobId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  totalRows?: number
  processedRows?: number
  errors?: Array<{
    row: number
    message: string
  }>
}

// WebSocket 消息类型
export interface WebSocketMessage<T = any> {
  type: string
  data: T
  timestamp: string
  id?: string
}

// 实时更新事件
export interface RealtimeEvent<T = any> {
  event: string
  resource: string
  action: 'created' | 'updated' | 'deleted'
  data: T
  userId?: string
  timestamp: string
}

// 健康检查
export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded'
  version: string
  uptime: number
  dependencies: Record<string, {
    status: 'up' | 'down'
    responseTime?: number
    lastCheck: string
  }>
}

// 系统信息
export interface SystemInfo {
  version: string
  environment: string
  buildTime: string
  gitCommit?: string
  features: string[]
}

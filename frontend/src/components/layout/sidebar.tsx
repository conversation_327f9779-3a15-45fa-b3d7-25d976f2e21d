/**
 * 侧边栏导航组件
 */

import { cn } from '@/utils'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  LayoutDashboard,
  FileText,
  Brain,
  Network,
  Tags,
  Upload,
  Users,
  Settings,
  BarChart3,
  FileBarChart,
  GitBranch
} from 'lucide-react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/auth'

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Sidebar({ className }: SidebarProps) {
  const location = useLocation()
  const navigate = useNavigate()
  const { user } = useAuthStore()

  const routes = [
    {
      label: '仪表板',
      icon: LayoutDashboard,
      href: '/dashboard',
      color: 'text-sky-500',
    },
    {
      label: '题目管理',
      icon: FileText,
      href: '/questions',
      color: 'text-violet-500',
    },
    {
      label: '知识点管理',
      icon: Brain,
      href: '/knowledge-points',
      color: 'text-pink-700',
    },
    {
      label: '知识空间',
      icon: Network,
      href: '/knowledge-space',
      color: 'text-blue-600',
    },
    {
      label: '数据标注',
      icon: Tags,
      href: '/annotation',
      color: 'text-orange-700',
    },
    {
      label: '导入导出',
      icon: Upload,
      href: '/import-export',
      color: 'text-green-600',
    },
    {
      label: '设置',
      icon: Settings,
      href: '/settings',
      color: 'text-gray-700',
    },
  ]

  // 质量检测路由
  const qualityRoutes = [
    {
      label: '质量监控',
      icon: BarChart3,
      href: '/quality/dashboard',
      color: 'text-purple-600',
    },
    {
      label: '质量报告',
      icon: FileBarChart,
      href: '/quality/report',
      color: 'text-indigo-600',
    },
  ]

  // 版本管理路由
  const versionRoutes = [
    {
      label: '版本管理',
      icon: GitBranch,
      href: '/versions',
      color: 'text-cyan-600',
    },
  ]

  // 管理员专用路由
  const adminRoutes = [
    {
      label: '用户管理',
      icon: Users,
      href: '/admin/users',
      color: 'text-emerald-500',
    },
  ]

  const onNavigate = (href: string) => {
    navigate(href)
  }

  return (
    <div className={cn('pb-12 min-h-screen', className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            导航
          </h2>
          <div className="space-y-1">
            {routes.map((route) => (
              <Button
                key={route.href}
                variant={location.pathname === route.href ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => onNavigate(route.href)}
              >
                <route.icon className={cn('mr-2 h-4 w-4', route.color)} />
                {route.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 质量检测菜单 */}
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            质量检测
          </h2>
          <div className="space-y-1">
            {qualityRoutes.map((route) => (
              <Button
                key={route.href}
                variant={location.pathname === route.href ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => onNavigate(route.href)}
              >
                <route.icon className={cn('mr-2 h-4 w-4', route.color)} />
                {route.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 版本管理菜单 */}
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            版本管理
          </h2>
          <div className="space-y-1">
            {versionRoutes.map((route) => (
              <Button
                key={route.href}
                variant={location.pathname === route.href ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => onNavigate(route.href)}
              >
                <route.icon className={cn('mr-2 h-4 w-4', route.color)} />
                {route.label}
              </Button>
            ))}
          </div>
        </div>

        {user?.role === 'admin' && (
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
              管理
            </h2>
            <div className="space-y-1">
              {adminRoutes.map((route) => (
                <Button
                  key={route.href}
                  variant={location.pathname === route.href ? 'secondary' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => onNavigate(route.href)}
                >
                  <route.icon className={cn('mr-2 h-4 w-4', route.color)} />
                  {route.label}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

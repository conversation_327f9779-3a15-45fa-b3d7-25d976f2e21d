/**
 * 题目详情查看组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Edit, Trash2, X } from 'lucide-react'
import { QuestionsService } from '@/services/questions'
import type { Question } from '@/types'

interface QuestionDetailProps {
  question: Question
  onEdit?: (question: Question) => void
  onDelete?: (question: Question) => void
  onClose?: () => void
}

export function QuestionDetail({ question, onEdit, onDelete, onClose }: QuestionDetailProps) {
  const [knowledgePoints, setKnowledgePoints] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const questionTypes = QuestionsService.getQuestionTypes()

  useEffect(() => {
    loadKnowledgePoints()
  }, [question.questionId])

  const loadKnowledgePoints = async () => {
    try {
      setLoading(true)
      const kps = await QuestionsService.getQuestionKnowledgePoints(question.questionId.toString())
      setKnowledgePoints(kps)
    } catch (error) {
      console.error('加载关联知识点失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getQuestionTypeLabel = (qType: number) => {
    const type = questionTypes.find(t => t.value === qType)
    return type?.label || '未知类型'
  }

  const getDifficultyLabel = (level?: number) => {
    if (!level) return '未设置'
    const labels = {
      1: '非常简单',
      2: '简单', 
      3: '中等',
      4: '困难',
      5: '非常困难'
    }
    return labels[level as keyof typeof labels] || '未知'
  }

  const renderAnswerKey = () => {
    if (!question.answerKey) return '未设置'

    if (question.qType === 0) { // 单选题
      const correctIndex = question.answerKey.correct
      if (typeof correctIndex === 'number' && question.content.options) {
        return `选项 ${String.fromCharCode(65 + correctIndex)}: ${question.content.options[correctIndex]}`
      }
    }

    if (question.qType === 1) { // 多选题
      const correctIndices = question.answerKey.correct
      if (Array.isArray(correctIndices) && question.content.options) {
        return correctIndices.map((index: number) => 
          `选项 ${String.fromCharCode(65 + index)}: ${question.content.options![index]}`
        ).join(', ')
      }
    }

    if (question.qType === 2) { // 判断题
      return question.answerKey.correct ? '正确' : '错误'
    }

    // 其他题型
    return question.answerKey.answer || '未设置'
  }

  const renderOptions = () => {
    if (!question.content.options || question.content.options.length === 0) {
      return null
    }

    return (
      <div className="space-y-2">
        {question.content.options.map((option, index) => (
          <div key={index} className="flex items-start gap-3">
            <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium">
              {String.fromCharCode(65 + index)}
            </div>
            <div className="flex-1">{option}</div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>题目详情</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => onEdit?.(question)}>
              <Edit className="w-4 h-4 mr-2" />
              编辑
            </Button>
            <Button variant="outline" size="sm" onClick={() => onDelete?.(question)}>
              <Trash2 className="w-4 h-4 mr-2" />
              删除
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 基本信息 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-500">题目ID</div>
            <div>{question.questionId}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-500">题目类型</div>
            <Badge variant="outline">{getQuestionTypeLabel(question.qType)}</Badge>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-500">难度等级</div>
            <div>{getDifficultyLabel(question.difficultyLvl)}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-500">状态</div>
            <div className="flex gap-2">
              {question.isActive ? (
                <Badge className="bg-green-100 text-green-800">启用</Badge>
              ) : (
                <Badge className="bg-gray-100 text-gray-800">禁用</Badge>
              )}
              {question.irtReady && (
                <Badge className="bg-blue-100 text-blue-800">IRT已校准</Badge>
              )}
            </div>
          </div>
        </div>

        <Separator />

        {/* 题目内容 */}
        <div>
          <div className="text-sm font-medium text-gray-500 mb-2">题干</div>
          <div className="bg-gray-50 p-4 rounded-lg">
            {question.content.stem}
          </div>
        </div>

        {/* 选项 */}
        {question.content.options && question.content.options.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-500 mb-2">选项</div>
            <div className="bg-gray-50 p-4 rounded-lg">
              {renderOptions()}
            </div>
          </div>
        )}

        {/* 正确答案 */}
        <div>
          <div className="text-sm font-medium text-gray-500 mb-2">正确答案</div>
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            {renderAnswerKey()}
          </div>
        </div>

        {/* 题目解析 */}
        {question.analysis && (
          <div>
            <div className="text-sm font-medium text-gray-500 mb-2">题目解析</div>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              {question.analysis}
            </div>
          </div>
        )}

        {/* 题目来源 */}
        {question.source && (
          <div>
            <div className="text-sm font-medium text-gray-500 mb-2">题目来源</div>
            <div>{question.source}</div>
          </div>
        )}

        {/* 标签 */}
        {question.tags && question.tags.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-500 mb-2">标签</div>
            <div className="flex flex-wrap gap-2">
              {question.tags.map(tag => (
                <Badge key={tag} variant="secondary">{tag}</Badge>
              ))}
            </div>
          </div>
        )}

        <Separator />

        {/* 关联知识点 */}
        <div>
          <div className="text-sm font-medium text-gray-500 mb-2">关联知识点</div>
          {loading ? (
            <div className="text-gray-500">加载中...</div>
          ) : knowledgePoints.length > 0 ? (
            <div className="space-y-2">
              {knowledgePoints.map((kp, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{kp.name}</div>
                    <div className="text-sm text-gray-500">编码: {kp.code}</div>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant={kp.is_required ? "default" : "secondary"}>
                      {kp.is_required ? '必需' : '可选'}
                    </Badge>
                    <Badge variant="outline">
                      权重: {kp.weight}
                    </Badge>
                    <Badge variant="outline">
                      置信度: {Math.round(kp.confidence * 100)}%
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500">暂无关联知识点</div>
          )}
        </div>

        <Separator />

        {/* 创建信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
          <div>
            <div className="font-medium">创建时间</div>
            <div>{new Date(question.createdAt).toLocaleString()}</div>
          </div>
          <div>
            <div className="font-medium">更新时间</div>
            <div>{new Date(question.updatedAt).toLocaleString()}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

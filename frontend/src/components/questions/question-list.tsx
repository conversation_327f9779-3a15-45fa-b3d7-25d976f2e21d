/**
 * 题目列表组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, Plus, Edit, Trash2, Eye } from 'lucide-react'
import { QuestionsService, type QuestionFilters } from '@/services/questions'
import type { Question } from '@/types'

interface QuestionListProps {
  onEdit?: (question: Question) => void
  onView?: (question: Question) => void
  onDelete?: (question: Question) => void
  onCreateNew?: () => void
}

export function QuestionList({ onEdit, onView, onDelete, onCreateNew }: QuestionListProps) {
  const [questions, setQuestions] = useState<Question[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [filters, setFilters] = useState<QuestionFilters>({})
  const [subjects, setSubjects] = useState<string[]>([])
  const [difficulties, setDifficulties] = useState<number[]>([])

  const questionTypes = QuestionsService.getQuestionTypes()

  useEffect(() => {
    loadQuestions()
    loadFilterOptions()
  }, [currentPage, filters])

  const loadQuestions = async () => {
    try {
      setLoading(true)
      const skip = (currentPage - 1) * pageSize
      const response = await QuestionsService.getQuestions(skip, pageSize, filters)
      setQuestions(response.items)
      setTotal(response.total)
    } catch (error) {
      console.error('加载题目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadFilterOptions = async () => {
    try {
      const [subjectsData, difficultiesData] = await Promise.all([
        QuestionsService.getSubjects(),
        QuestionsService.getDifficulties()
      ])
      setSubjects(subjectsData)
      setDifficulties(difficultiesData)
    } catch (error) {
      console.error('加载筛选选项失败:', error)
    }
  }

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
    setCurrentPage(1)
  }

  const handleFilterChange = (key: keyof QuestionFilters, value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      [key]: value === 'all' ? undefined : value 
    }))
    setCurrentPage(1)
  }

  const getQuestionTypeLabel = (qType: number) => {
    const type = questionTypes.find(t => t.value === qType)
    return type?.label || '未知类型'
  }

  const getDifficultyBadge = (level?: number) => {
    if (!level) return null
    
    const colors = {
      1: 'bg-green-100 text-green-800',
      2: 'bg-blue-100 text-blue-800',
      3: 'bg-yellow-100 text-yellow-800',
      4: 'bg-orange-100 text-orange-800',
      5: 'bg-red-100 text-red-800'
    }
    
    return (
      <Badge className={colors[level as keyof typeof colors]}>
        难度 {level}
      </Badge>
    )
  }

  const totalPages = Math.ceil(total / pageSize)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>题目管理</CardTitle>
          <Button onClick={onCreateNew}>
            <Plus className="w-4 h-4 mr-2" />
            新建题目
          </Button>
        </div>
        
        {/* 搜索和筛选 */}
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索题目内容、解析或来源..."
                className="pl-10"
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>
          
          <Select onValueChange={(value) => handleFilterChange('subject', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="选择学科" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部学科</SelectItem>
              {subjects.map(subject => (
                <SelectItem key={subject} value={subject}>
                  {subject}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select onValueChange={(value) => handleFilterChange('difficulty', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="选择难度" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部难度</SelectItem>
              {difficulties.map(difficulty => (
                <SelectItem key={difficulty} value={difficulty.toString()}>
                  难度 {difficulty}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select onValueChange={(value) => handleFilterChange('qType', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="题目类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              {questionTypes.map(type => (
                <SelectItem key={type.value} value={type.value.toString()}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>题目内容</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>难度</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {questions.map((question) => (
                  <TableRow key={question.questionId}>
                    <TableCell>
                      <div className="max-w-md">
                        <div className="font-medium truncate">
                          {question.content.stem}
                        </div>
                        {question.source && (
                          <div className="text-sm text-gray-500">
                            来源: {question.source}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getQuestionTypeLabel(question.qType)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getDifficultyBadge(question.difficultyLvl)}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {question.isActive ? (
                          <Badge className="bg-green-100 text-green-800">
                            启用
                          </Badge>
                        ) : (
                          <Badge className="bg-gray-100 text-gray-800">
                            禁用
                          </Badge>
                        )}
                        {question.irtReady && (
                          <Badge className="bg-blue-100 text-blue-800">
                            IRT已校准
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(question.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onView?.(question)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit?.(question)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete?.(question)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  共 {total} 条记录，第 {currentPage} / {totalPages} 页
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => prev - 1)}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(prev => prev + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

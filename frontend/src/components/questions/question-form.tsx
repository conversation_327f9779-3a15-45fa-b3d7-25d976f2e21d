/**
 * 题目表单组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Plus, Minus, Save, X } from 'lucide-react'
import { QuestionsService, type QuestionCreateRequest, type QuestionUpdateRequest } from '@/services/questions'
import type { Question, QuestionContent } from '@/types'

interface QuestionFormProps {
  question?: Question
  onSave?: (question: Question) => void
  onCancel?: () => void
}

export function QuestionForm({ question, onSave, onCancel }: QuestionFormProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  
  // 表单数据
  const [formData, setFormData] = useState({
    stem: '',
    options: [''],
    qType: 0,
    difficultyLvl: undefined as number | undefined,
    answerKey: {} as any,
    analysis: '',
    source: '',
    tags: [] as string[]
  })
  
  const [tagInput, setTagInput] = useState('')
  
  const questionTypes = QuestionsService.getQuestionTypes()
  
  useEffect(() => {
    if (question) {
      setFormData({
        stem: question.content.stem,
        options: question.content.options || [''],
        qType: question.qType,
        difficultyLvl: question.difficultyLvl,
        answerKey: question.answerKey || {},
        analysis: question.analysis || '',
        source: question.source || '',
        tags: question.tags || []
      })
    }
  }, [question])

  const handleStemChange = (value: string) => {
    setFormData(prev => ({ ...prev, stem: value }))
  }

  const handleQTypeChange = (value: string) => {
    const qType = parseInt(value)
    setFormData(prev => ({ 
      ...prev, 
      qType,
      // 重置选项和答案
      options: qType === 0 || qType === 1 ? ['', '', '', ''] : [''],
      answerKey: {}
    }))
  }

  const handleOptionChange = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => i === index ? value : opt)
    }))
  }

  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, '']
    }))
  }

  const removeOption = (index: number) => {
    if (formData.options.length > 1) {
      setFormData(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      }))
    }
  }

  const handleAnswerKeyChange = (value: any) => {
    setFormData(prev => ({ ...prev, answerKey: value }))
  }

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const content: QuestionContent = {
      stem: formData.stem,
      options: needsOptions() ? formData.options.filter(opt => opt.trim()) : undefined
    }
    
    const requestData = question ? {
      content,
      qType: formData.qType,
      difficultyLvl: formData.difficultyLvl,
      answerKey: formData.answerKey,
      analysis: formData.analysis,
      source: formData.source,
      tags: formData.tags
    } as QuestionUpdateRequest : {
      content,
      qType: formData.qType,
      difficultyLvl: formData.difficultyLvl,
      answerKey: formData.answerKey,
      analysis: formData.analysis,
      source: formData.source,
      tags: formData.tags
    } as QuestionCreateRequest
    
    // 验证数据
    const validation = QuestionsService.validateQuestion(requestData)
    if (!validation.isValid) {
      setErrors(validation.errors)
      return
    }
    
    try {
      setLoading(true)
      setErrors([])
      
      let result: Question
      if (question) {
        result = await QuestionsService.updateQuestion(question.questionId.toString(), requestData as QuestionUpdateRequest)
      } else {
        result = await QuestionsService.createQuestion(requestData as QuestionCreateRequest)
      }
      
      onSave?.(result)
    } catch (error: any) {
      setErrors([error.response?.data?.detail || '保存失败'])
    } finally {
      setLoading(false)
    }
  }

  const needsOptions = () => {
    return formData.qType === 0 || formData.qType === 1 // 单选题或多选题
  }

  const renderAnswerKeyInput = () => {
    if (formData.qType === 0) { // 单选题
      return (
        <div>
          <Label>正确答案</Label>
          <Select onValueChange={(value) => handleAnswerKeyChange({ correct: parseInt(value) })}>
            <SelectTrigger>
              <SelectValue placeholder="选择正确答案" />
            </SelectTrigger>
            <SelectContent>
              {formData.options.map((_, index) => (
                <SelectItem key={index} value={index.toString()}>
                  选项 {String.fromCharCode(65 + index)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )
    }
    
    if (formData.qType === 1) { // 多选题
      return (
        <div>
          <Label>正确答案（多选）</Label>
          <div className="space-y-2">
            {formData.options.map((_, index) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.answerKey.correct?.includes(index)}
                  onChange={(e) => {
                    const current = formData.answerKey.correct || []
                    const newCorrect = e.target.checked
                      ? [...current, index]
                      : current.filter((i: number) => i !== index)
                    handleAnswerKeyChange({ correct: newCorrect })
                  }}
                />
                <span>选项 {String.fromCharCode(65 + index)}</span>
              </label>
            ))}
          </div>
        </div>
      )
    }
    
    if (formData.qType === 2) { // 判断题
      return (
        <div>
          <Label>正确答案</Label>
          <Select onValueChange={(value) => handleAnswerKeyChange({ correct: value === 'true' })}>
            <SelectTrigger>
              <SelectValue placeholder="选择正确答案" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">正确</SelectItem>
              <SelectItem value="false">错误</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )
    }
    
    // 其他题型
    return (
      <div>
        <Label>参考答案</Label>
        <Textarea
          value={formData.answerKey.answer || ''}
          onChange={(e) => handleAnswerKeyChange({ answer: e.target.value })}
          placeholder="请输入参考答案..."
        />
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{question ? '编辑题目' : '新建题目'}</CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 错误提示 */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <ul className="list-disc list-inside text-red-600">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {/* 题目类型 */}
          <div>
            <Label>题目类型 *</Label>
            <Select value={formData.qType.toString()} onValueChange={handleQTypeChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {questionTypes.map(type => (
                  <SelectItem key={type.value} value={type.value.toString()}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* 题干 */}
          <div>
            <Label>题干 *</Label>
            <Textarea
              value={formData.stem}
              onChange={(e) => handleStemChange(e.target.value)}
              placeholder="请输入题目内容..."
              rows={4}
            />
          </div>
          
          {/* 选项（仅选择题） */}
          {needsOptions() && (
            <div>
              <Label>选项</Label>
              <div className="space-y-2">
                {formData.options.map((option, index) => (
                  <div key={index} className="flex gap-2">
                    <div className="w-8 flex items-center justify-center bg-gray-100 rounded">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <Input
                      value={option}
                      onChange={(e) => handleOptionChange(index, e.target.value)}
                      placeholder={`选项 ${String.fromCharCode(65 + index)}`}
                      className="flex-1"
                    />
                    {formData.options.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeOption(index)}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addOption}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  添加选项
                </Button>
              </div>
            </div>
          )}
          
          {/* 答案 */}
          {renderAnswerKeyInput()}
          
          {/* 难度等级 */}
          <div>
            <Label>难度等级</Label>
            <Select 
              value={formData.difficultyLvl?.toString() || ''} 
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                difficultyLvl: value ? parseInt(value) : undefined 
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择难度等级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">不设置</SelectItem>
                <SelectItem value="1">1 - 非常简单</SelectItem>
                <SelectItem value="2">2 - 简单</SelectItem>
                <SelectItem value="3">3 - 中等</SelectItem>
                <SelectItem value="4">4 - 困难</SelectItem>
                <SelectItem value="5">5 - 非常困难</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* 题目解析 */}
          <div>
            <Label>题目解析</Label>
            <Textarea
              value={formData.analysis}
              onChange={(e) => setFormData(prev => ({ ...prev, analysis: e.target.value }))}
              placeholder="请输入题目解析..."
              rows={3}
            />
          </div>
          
          {/* 题目来源 */}
          <div>
            <Label>题目来源</Label>
            <Input
              value={formData.source}
              onChange={(e) => setFormData(prev => ({ ...prev, source: e.target.value }))}
              placeholder="请输入题目来源..."
            />
          </div>
          
          {/* 标签 */}
          <div>
            <Label>标签</Label>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="输入标签..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" variant="outline" onClick={addTag}>
                  添加
                </Button>
              </div>
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                      {tag} <X className="w-3 h-3 ml-1" />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex gap-4">
            <Button type="submit" disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? '保存中...' : '保存'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              取消
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

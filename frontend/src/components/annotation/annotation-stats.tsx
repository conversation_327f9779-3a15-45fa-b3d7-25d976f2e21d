/**
 * 标注统计组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { X, Download, BarChart3, FileText, Target } from 'lucide-react'
import { AnnotationService, type AnnotationStats } from '@/services/annotation'

interface AnnotationStatsProps {
  onClose?: () => void
  onExportQMatrix?: () => void
}

export function AnnotationStatsComponent({ onClose, onExportQMatrix }: AnnotationStatsProps) {
  const [stats, setStats] = useState<AnnotationStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setLoading(true)
      const statsData = await AnnotationService.getAnnotationStats()
      setStats(statsData)
    } catch (error) {
      console.error('加载统计信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExportQMatrix = async () => {
    try {
      const blob = await AnnotationService.exportQMatrix()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `q-matrix-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('导出Q矩阵失败:', error)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">加载中...</div>
        </CardContent>
      </Card>
    )
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center text-red-500">加载统计信息失败</div>
        </CardContent>
      </Card>
    )
  }

  const coveragePercentage = AnnotationService.calculateCoverage(
    stats.totalQuestions, 
    stats.annotatedQuestions
  )

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            标注统计信息
          </CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExportQMatrix}>
              <Download className="w-4 h-4 mr-2" />
              导出Q矩阵
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 总体统计 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <FileText className="w-8 h-8 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {stats.totalQuestions}
            </div>
            <div className="text-sm text-gray-600">总题目数</div>
          </div>
          
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Target className="w-8 h-8 text-green-500" />
            </div>
            <div className="text-2xl font-bold text-green-600">
              {stats.annotatedQuestions}
            </div>
            <div className="text-sm text-gray-600">已标注题目</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <BarChart3 className="w-8 h-8 text-purple-500" />
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {stats.totalMappings}
            </div>
            <div className="text-sm text-gray-600">总标注关系</div>
          </div>
          
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Badge className="bg-orange-100 text-orange-800 text-lg px-3 py-1">
                {stats.avgMappingsPerQuestion.toFixed(1)}
              </Badge>
            </div>
            <div className="text-sm text-gray-600">平均每题标注数</div>
          </div>
        </div>
        
        {/* 标注覆盖率 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold">标注覆盖率</h3>
            <Badge 
              variant={coveragePercentage >= 80 ? "default" : coveragePercentage >= 50 ? "secondary" : "destructive"}
              className="text-sm"
            >
              {coveragePercentage}%
            </Badge>
          </div>
          
          <Progress 
            value={stats.annotationCoverage * 100} 
            className="h-3"
          />
          
          <div className="flex justify-between text-sm text-gray-500 mt-1">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
          
          <div className="mt-2 text-sm text-gray-600">
            {stats.totalQuestions - stats.annotatedQuestions > 0 ? (
              <>还有 {stats.totalQuestions - stats.annotatedQuestions} 个题目未标注</>
            ) : (
              <>所有题目都已完成标注</>
            )}
          </div>
        </div>
        
        {/* 标注质量指标 */}
        <div>
          <h3 className="text-lg font-semibold mb-3">标注质量指标</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">平均标注密度</span>
                <Badge variant="outline">
                  {stats.avgMappingsPerQuestion.toFixed(2)}
                </Badge>
              </div>
              <div className="text-sm text-gray-600">
                每个题目平均关联的知识点数量
              </div>
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">标注完整性</span>
                <Badge 
                  variant={coveragePercentage >= 80 ? "default" : "secondary"}
                >
                  {coveragePercentage >= 80 ? '良好' : coveragePercentage >= 50 ? '一般' : '待改进'}
                </Badge>
              </div>
              <div className="text-sm text-gray-600">
                基于覆盖率的标注完整性评估
              </div>
            </div>
          </div>
        </div>
        
        {/* 建议和提示 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">标注建议</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            {coveragePercentage < 50 && (
              <li>• 标注覆盖率较低，建议增加标注工作</li>
            )}
            {stats.avgMappingsPerQuestion < 1.5 && (
              <li>• 平均标注密度较低，可能需要更细致的知识点关联</li>
            )}
            {stats.avgMappingsPerQuestion > 5 && (
              <li>• 平均标注密度较高，建议检查是否存在过度标注</li>
            )}
            <li>• 定期导出Q矩阵进行质量检查和分析</li>
            <li>• 建议对标注结果进行专家审核</li>
          </ul>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex gap-4 pt-4 border-t">
          <Button onClick={handleExportQMatrix}>
            <Download className="w-4 h-4 mr-2" />
            导出Q矩阵数据
          </Button>
          <Button variant="outline" onClick={onExportQMatrix}>
            查看Q矩阵可视化
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

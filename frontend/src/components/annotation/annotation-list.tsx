/**
 * 标注列表组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Search, Plus, Edit, Trash2, BarChart3 } from 'lucide-react'
import { AnnotationService, type ItemKpMapping } from '@/services/annotation'

interface AnnotationListProps {
  onCreateNew?: () => void
  onEdit?: (mapping: ItemKpMapping) => void
  onDelete?: (mapping: ItemKpMapping) => void
  onViewStats?: () => void
}

export function AnnotationList({ onCreateNew, onEdit, onDelete, onViewStats }: AnnotationListProps) {
  const [mappings, setMappings] = useState<ItemKpMapping[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [searchQuestionId, setSearchQuestionId] = useState('')
  const [searchKpId, setSearchKpId] = useState('')

  useEffect(() => {
    loadMappings()
  }, [currentPage, searchQuestionId, searchKpId])

  const loadMappings = async () => {
    try {
      setLoading(true)
      const skip = (currentPage - 1) * pageSize
      const questionId = searchQuestionId ? parseInt(searchQuestionId) : undefined
      const kpId = searchKpId ? parseInt(searchKpId) : undefined
      
      const response = await AnnotationService.getMappings(skip, pageSize, questionId, kpId)
      setMappings(response.items)
      setTotal(response.total)
    } catch (error) {
      console.error('加载标注列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (mapping: ItemKpMapping) => {
    if (confirm('确定要删除这个标注关系吗？')) {
      try {
        await AnnotationService.deleteMapping(mapping.questionId, mapping.kpId)
        loadMappings() // 重新加载列表
      } catch (error) {
        console.error('删除失败:', error)
      }
    }
  }

  const getSourceBadge = (source: number) => {
    const sources = AnnotationService.getAnnotationSources()
    const sourceInfo = sources.find(s => s.value === source)
    
    const colors = {
      0: 'bg-blue-100 text-blue-800',   // 专家标注
      1: 'bg-green-100 text-green-800', // 算法推断
      2: 'bg-gray-100 text-gray-800'    // 数据导入
    }
    
    return (
      <Badge className={colors[source as keyof typeof colors]}>
        {sourceInfo?.label || '未知'}
      </Badge>
    )
  }

  const getWeightColor = (weight: number) => {
    return AnnotationService.getWeightColor(weight)
  }

  const getConfidenceColor = (confidence: number) => {
    return AnnotationService.getConfidenceColor(confidence)
  }

  const formatConfidence = (confidence: number) => {
    return AnnotationService.formatConfidence(confidence)
  }

  const totalPages = Math.ceil(total / pageSize)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>题目-知识点标注关系</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onViewStats}>
              <BarChart3 className="w-4 h-4 mr-2" />
              统计信息
            </Button>
            <Button onClick={onCreateNew}>
              <Plus className="w-4 h-4 mr-2" />
              新建标注
            </Button>
          </div>
        </div>
        
        {/* 搜索筛选 */}
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="按题目ID搜索..."
                className="pl-10"
                value={searchQuestionId}
                onChange={(e) => setSearchQuestionId(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="按知识点ID搜索..."
                className="pl-10"
                value={searchKpId}
                onChange={(e) => setSearchKpId(e.target.value)}
              />
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>题目信息</TableHead>
                  <TableHead>知识点信息</TableHead>
                  <TableHead>是否必需</TableHead>
                  <TableHead>权重</TableHead>
                  <TableHead>置信度</TableHead>
                  <TableHead>标注来源</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mappings.map((mapping) => (
                  <TableRow key={`${mapping.questionId}-${mapping.kpId}`}>
                    <TableCell>
                      <div>
                        <div className="font-medium">题目 #{mapping.questionId}</div>
                        {mapping.question && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {mapping.question.content.stem}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {mapping.knowledgePoint?.name || `知识点 #${mapping.kpId}`}
                        </div>
                        {mapping.knowledgePoint && (
                          <div className="text-sm text-gray-500">
                            编码: {mapping.knowledgePoint.code}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={mapping.isRequired ? "default" : "secondary"}>
                        {mapping.isRequired ? '必需' : '可选'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getWeightColor(mapping.weight)}>
                        {mapping.weight.toFixed(1)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={getConfidenceColor(mapping.confidence)}>
                        {formatConfidence(mapping.confidence)}
                      </span>
                    </TableCell>
                    <TableCell>
                      {getSourceBadge(mapping.source)}
                    </TableCell>
                    <TableCell>
                      {new Date(mapping.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit?.(mapping)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(mapping)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {mappings.length === 0 && !loading && (
              <div className="text-center py-8 text-gray-500">
                <div>暂无标注关系</div>
                <Button className="mt-4" onClick={onCreateNew}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建第一个标注
                </Button>
              </div>
            )}
            
            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  共 {total} 条记录，第 {currentPage} / {totalPages} 页
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => prev - 1)}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(prev => prev + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

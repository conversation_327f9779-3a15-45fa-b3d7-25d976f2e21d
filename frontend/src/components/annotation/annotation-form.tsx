/**
 * 标注表单组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, Plus, Save, X } from 'lucide-react'
import { QuestionsService } from '@/services/questions'
import { KnowledgePointsService, type KnowledgePoint } from '@/services/knowledge-points'
import { AnnotationService, type ItemKpMappingCreateRequest } from '@/services/annotation'
import type { Question } from '@/types'

interface AnnotationFormProps {
  onSave?: () => void
  onCancel?: () => void
}

export function AnnotationForm({ onSave, onCancel }: AnnotationFormProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  
  // 题目相关状态
  const [questions, setQuestions] = useState<Question[]>([])
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([])
  const [questionSearch, setQuestionSearch] = useState('')
  
  // 知识点相关状态
  const [knowledgePoints, setKnowledgePoints] = useState<KnowledgePoint[]>([])
  const [selectedKnowledgePoint, setSelectedKnowledgePoint] = useState<number | undefined>()
  const [kpSearch, setKpSearch] = useState('')
  
  // 标注参数
  const [annotationParams, setAnnotationParams] = useState({
    isRequired: true,
    weight: 1.0,
    confidence: 1.0,
    source: 0
  })

  const annotationSources = AnnotationService.getAnnotationSources()

  useEffect(() => {
    loadQuestions()
    loadKnowledgePoints()
  }, [])

  const loadQuestions = async () => {
    try {
      const response = await QuestionsService.getQuestions(0, 50, {
        search: questionSearch
      })
      setQuestions(response.items)
    } catch (error) {
      console.error('加载题目失败:', error)
    }
  }

  const loadKnowledgePoints = async () => {
    try {
      const response = await KnowledgePointsService.getKnowledgePoints(0, 100, {
        search: kpSearch
      })
      setKnowledgePoints(response.items)
    } catch (error) {
      console.error('加载知识点失败:', error)
    }
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      loadQuestions()
    }, 300)
    return () => clearTimeout(timer)
  }, [questionSearch])

  useEffect(() => {
    const timer = setTimeout(() => {
      loadKnowledgePoints()
    }, 300)
    return () => clearTimeout(timer)
  }, [kpSearch])

  const handleQuestionToggle = (questionId: number) => {
    setSelectedQuestions(prev => 
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (selectedQuestions.length === 0) {
      setErrors(['请至少选择一个题目'])
      return
    }
    
    if (!selectedKnowledgePoint) {
      setErrors(['请选择知识点'])
      return
    }
    
    // 验证参数
    const validation = AnnotationService.validateMapping({
      questionId: selectedQuestions[0], // 这里只是为了验证
      kpId: selectedKnowledgePoint,
      ...annotationParams
    })
    
    if (!validation.isValid) {
      setErrors(validation.errors)
      return
    }
    
    try {
      setLoading(true)
      setErrors([])
      
      if (selectedQuestions.length === 1) {
        // 单个标注
        const request: ItemKpMappingCreateRequest = {
          questionId: selectedQuestions[0],
          kpId: selectedKnowledgePoint,
          ...annotationParams
        }
        await AnnotationService.createMapping(request)
      } else {
        // 批量标注
        const request = {
          questionIds: selectedQuestions,
          kpId: selectedKnowledgePoint,
          ...annotationParams
        }
        await AnnotationService.batchAnnotate(request)
      }
      
      onSave?.()
    } catch (error: any) {
      setErrors([error.response?.data?.detail || '标注失败'])
    } finally {
      setLoading(false)
    }
  }

  const getQuestionTypeLabel = (qType: number) => {
    const types = QuestionsService.getQuestionTypes()
    const type = types.find(t => t.value === qType)
    return type?.label || '未知类型'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>题目-知识点关联标注</CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 错误提示 */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <ul className="list-disc list-inside text-red-600">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {/* 选择题目 */}
          <div>
            <Label>选择题目 *</Label>
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索题目..."
                  className="pl-10"
                  value={questionSearch}
                  onChange={(e) => setQuestionSearch(e.target.value)}
                />
              </div>
              
              <div className="max-h-60 overflow-y-auto border rounded-lg">
                {questions.map(question => (
                  <div
                    key={question.questionId}
                    className={`p-3 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 ${
                      selectedQuestions.includes(question.questionId) ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => handleQuestionToggle(question.questionId)}
                  >
                    <div className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        checked={selectedQuestions.includes(question.questionId)}
                        onChange={() => handleQuestionToggle(question.questionId)}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <div className="font-medium truncate">
                          {question.content.stem}
                        </div>
                        <div className="flex gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {getQuestionTypeLabel(question.qType)}
                          </Badge>
                          {question.difficultyLvl && (
                            <Badge variant="outline" className="text-xs">
                              难度 {question.difficultyLvl}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {questions.length === 0 && (
                  <div className="p-4 text-center text-gray-500">
                    {questionSearch ? '未找到匹配的题目' : '暂无题目'}
                  </div>
                )}
              </div>
              
              {selectedQuestions.length > 0 && (
                <div className="text-sm text-blue-600">
                  已选择 {selectedQuestions.length} 个题目
                </div>
              )}
            </div>
          </div>
          
          {/* 选择知识点 */}
          <div>
            <Label>选择知识点 *</Label>
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索知识点..."
                  className="pl-10"
                  value={kpSearch}
                  onChange={(e) => setKpSearch(e.target.value)}
                />
              </div>
              
              <Select 
                value={selectedKnowledgePoint?.toString() || ''} 
                onValueChange={(value) => setSelectedKnowledgePoint(value ? parseInt(value) : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择知识点" />
                </SelectTrigger>
                <SelectContent>
                  {knowledgePoints.map(kp => (
                    <SelectItem key={kp.kpId} value={kp.kpId.toString()}>
                      {kp.name} ({kp.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* 标注参数 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>是否必需掌握</Label>
              <Select 
                value={annotationParams.isRequired.toString()} 
                onValueChange={(value) => setAnnotationParams(prev => ({ 
                  ...prev, 
                  isRequired: value === 'true' 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">必需掌握</SelectItem>
                  <SelectItem value="false">可选掌握</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>权重 (0-10)</Label>
              <Input
                type="number"
                min="0"
                max="10"
                step="0.1"
                value={annotationParams.weight}
                onChange={(e) => setAnnotationParams(prev => ({ 
                  ...prev, 
                  weight: parseFloat(e.target.value) || 0 
                }))}
              />
            </div>
            
            <div>
              <Label>置信度 (0-1)</Label>
              <Input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={annotationParams.confidence}
                onChange={(e) => setAnnotationParams(prev => ({ 
                  ...prev, 
                  confidence: parseFloat(e.target.value) || 0 
                }))}
              />
            </div>
            
            <div>
              <Label>标注来源</Label>
              <Select 
                value={annotationParams.source.toString()} 
                onValueChange={(value) => setAnnotationParams(prev => ({ 
                  ...prev, 
                  source: parseInt(value) 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {annotationSources.map(source => (
                    <SelectItem key={source.value} value={source.value.toString()}>
                      {source.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex gap-4">
            <Button type="submit" disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? '标注中...' : '保存标注'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              取消
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

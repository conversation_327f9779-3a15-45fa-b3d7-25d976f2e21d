/**
 * 质量监控仪表板组件
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Users, 
  FileText,
  RefreshCw,
  Download
} from 'lucide-react'
import { 
  getQualityDashboard, 
  runQualityCheck,
  type QualityDashboardData,
  type QualityCheckResult,
  getQualityLevelColor,
  getQualityLevelName
} from '@/services/quality'
import { useToast } from '@/components/ui/use-toast'

export const QualityDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<QualityDashboardData | null>(null)
  const [qualityResults, setQualityResults] = useState<QualityCheckResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [checking, setChecking] = useState(false)
  const { toast } = useToast()

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const response = await getQualityDashboard()
      setDashboardData(response.data)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载仪表板数据',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleQualityCheck = async () => {
    try {
      setChecking(true)
      const response = await runQualityCheck()
      setQualityResults(response.results)
      toast({
        title: '检测完成',
        description: `质量得分: ${response.results.quality_score}分`
      })
    } catch (error) {
      toast({
        title: '检测失败',
        description: '质量检测执行失败',
        variant: 'destructive'
      })
    } finally {
      setChecking(false)
    }
  }

  useEffect(() => {
    loadDashboardData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">质量监控仪表板</h1>
          <p className="text-muted-foreground">
            监控数据标注质量，识别和解决质量问题
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={loadDashboardData}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button 
            onClick={handleQualityCheck}
            disabled={checking}
          >
            {checking ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <BarChart3 className="h-4 w-4 mr-2" />
            )}
            执行质量检测
          </Button>
        </div>
      </div>

      {/* 质量检测结果 */}
      {qualityResults && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            最新质量检测完成，综合得分: {qualityResults.quality_score}分 
            ({getQualityLevelName(qualityResults.summary.quality_level)})，
            发现 {qualityResults.summary.total_issues} 个问题，
            其中 {qualityResults.summary.critical_issues} 个严重问题。
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="quality">质量分析</TabsTrigger>
          <TabsTrigger value="performance">用户表现</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* 基础统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总题目数</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.overview.total_questions || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  已标注 {dashboardData?.overview.annotated_questions || 0} 题
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">标注覆盖率</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.overview.annotation_coverage || 0}%
                </div>
                <Progress 
                  value={dashboardData?.overview.annotation_coverage || 0} 
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总映射数</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.overview.total_mappings || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  平均每题 {dashboardData?.overview.avg_mappings_per_question || 0} 个映射
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">近期活动</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.recent_activity.recent_annotations || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  最近7天，日均 {dashboardData?.recent_activity.daily_average || 0} 个
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="quality" className="space-y-4">
          {qualityResults ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 质量得分卡片 */}
              <Card>
                <CardHeader>
                  <CardTitle>综合质量得分</CardTitle>
                  <CardDescription>基于多维度质量检测的综合评分</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className={`text-4xl font-bold ${getQualityLevelColor(qualityResults.summary.quality_level)}`}>
                      {qualityResults.quality_score}
                    </div>
                    <div className="text-lg mt-2">
                      <Badge variant="outline" className={getQualityLevelColor(qualityResults.summary.quality_level)}>
                        {getQualityLevelName(qualityResults.summary.quality_level)}
                      </Badge>
                    </div>
                    <Progress value={qualityResults.quality_score} className="mt-4" />
                  </div>
                </CardContent>
              </Card>

              {/* 问题统计 */}
              <Card>
                <CardHeader>
                  <CardTitle>问题统计</CardTitle>
                  <CardDescription>检测到的各类质量问题</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>总问题数</span>
                      <Badge variant="outline">{qualityResults.summary.total_issues}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>严重问题</span>
                      <Badge variant="destructive">{qualityResults.summary.critical_issues}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>基础检查得分</span>
                      <Badge variant="outline">{qualityResults.basic_checks.score}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>业务规则得分</span>
                      <Badge variant="outline">{qualityResults.business_rules.score}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  暂无质量检测结果，请点击"执行质量检测"按钮开始检测
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>用户标注表现</CardTitle>
              <CardDescription>各用户的标注数量和质量统计</CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData?.user_performance && dashboardData.user_performance.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.user_performance.map((user, index) => (
                    <div key={user.user_id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <div className="font-medium">用户 {user.user_id}</div>
                        <div className="text-sm text-muted-foreground">
                          标注数量: {user.annotation_count}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">平均置信度</div>
                        <div className="text-sm">
                          <Badge variant="outline">
                            {(user.avg_confidence * 100).toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  暂无用户表现数据
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

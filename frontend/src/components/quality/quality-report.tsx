/**
 * 质量报告组件
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  FileText, 
  Download, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react'
import { 
  generateQualityReport,
  getQualityIssues,
  type QualityReport,
  type QualityIssue,
  getQualityLevelColor,
  getQualityLevelName,
  getSeverityColor,
  getSeverityName
} from '@/services/quality'
import { useToast } from '@/components/ui/use-toast'

export const QualityReportPage: React.FC = () => {
  const [report, setReport] = useState<QualityReport | null>(null)
  const [issues, setIssues] = useState<QualityIssue[]>([])
  const [loading, setLoading] = useState(false)
  const [issuesLoading, setIssuesLoading] = useState(false)
  const { toast } = useToast()

  const generateReport = async () => {
    try {
      setLoading(true)
      const response = await generateQualityReport()
      setReport(response.report)
      toast({
        title: '报告生成成功',
        description: `质量得分: ${response.report.quality_score}分`
      })
    } catch (error) {
      toast({
        title: '生成失败',
        description: '无法生成质量报告',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadIssues = async (severity?: string) => {
    try {
      setIssuesLoading(true)
      const response = await getQualityIssues({ severity, limit: 50 })
      setIssues(response.issues)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载质量问题列表',
        variant: 'destructive'
      })
    } finally {
      setIssuesLoading(false)
    }
  }

  useEffect(() => {
    loadIssues()
  }, [])

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'low':
        return <Info className="h-4 w-4 text-blue-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-green-600" />
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">质量报告</h1>
          <p className="text-muted-foreground">
            生成和查看详细的数据质量分析报告
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => loadIssues()}
            disabled={issuesLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新问题
          </Button>
          <Button 
            onClick={generateReport}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FileText className="h-4 w-4 mr-2" />
            )}
            生成报告
          </Button>
        </div>
      </div>

      <Tabs defaultValue="report" className="space-y-4">
        <TabsList>
          <TabsTrigger value="report">质量报告</TabsTrigger>
          <TabsTrigger value="issues">问题列表</TabsTrigger>
        </TabsList>

        <TabsContent value="report" className="space-y-4">
          {report ? (
            <div className="space-y-6">
              {/* 报告头部信息 */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>质量报告 #{report.report_id}</CardTitle>
                      <CardDescription>
                        生成时间: {new Date(report.generated_at).toLocaleString()}
                      </CardDescription>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      导出报告
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className={`text-3xl font-bold ${getQualityLevelColor(report.quality_level)}`}>
                        {report.quality_score}
                      </div>
                      <div className="text-sm text-muted-foreground">综合得分</div>
                      <Badge variant="outline" className={`mt-1 ${getQualityLevelColor(report.quality_level)}`}>
                        {getQualityLevelName(report.quality_level)}
                      </Badge>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {report.critical_issues}
                      </div>
                      <div className="text-sm text-muted-foreground">严重问题</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">
                        {report.total_issues}
                      </div>
                      <div className="text-sm text-muted-foreground">总问题数</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {report.recommendations.length}
                      </div>
                      <div className="text-sm text-muted-foreground">改进建议</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 各检测模块得分 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(report.sections).map(([key, section]) => (
                  <Card key={key}>
                    <CardHeader>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between mb-2">
                        <span>得分</span>
                        <Badge variant="outline">{section.score}</Badge>
                      </div>
                      <Progress value={section.score} className="mb-4" />
                      <div className="space-y-2">
                        {Object.entries(section.checks).map(([checkKey, checkData]: [string, any]) => (
                          <div key={checkKey} className="flex justify-between text-sm">
                            <span className="text-muted-foreground">{checkData.description || checkKey}</span>
                            <Badge 
                              variant="outline" 
                              className={getSeverityColor(checkData.severity || 'none')}
                            >
                              {checkData.count || 0}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* 改进建议 */}
              <Card>
                <CardHeader>
                  <CardTitle>改进建议</CardTitle>
                  <CardDescription>基于检测结果的质量改进建议</CardDescription>
                </CardHeader>
                <CardContent>
                  {report.recommendations.length > 0 ? (
                    <ul className="space-y-2">
                      {report.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-muted-foreground">暂无改进建议</p>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">暂无质量报告</h3>
                <p className="text-muted-foreground mb-4">
                  点击"生成报告"按钮创建最新的质量分析报告
                </p>
                <Button onClick={generateReport} disabled={loading}>
                  {loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <FileText className="h-4 w-4 mr-2" />
                  )}
                  生成报告
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="issues" className="space-y-4">
          {/* 问题筛选 */}
          <Card>
            <CardHeader>
              <CardTitle>问题筛选</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => loadIssues()}
                >
                  全部
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => loadIssues('high')}
                >
                  严重
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => loadIssues('medium')}
                >
                  中等
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => loadIssues('low')}
                >
                  轻微
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 问题列表 */}
          <Card>
            <CardHeader>
              <CardTitle>质量问题列表</CardTitle>
              <CardDescription>检测到的各类质量问题详情</CardDescription>
            </CardHeader>
            <CardContent>
              {issuesLoading ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto" />
                </div>
              ) : issues.length > 0 ? (
                <div className="space-y-3">
                  {issues.map((issue, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getSeverityIcon(issue.severity)}
                          <span className="font-medium">{issue.description}</span>
                        </div>
                        <Badge className={getSeverityColor(issue.severity)}>
                          {getSeverityName(issue.severity)}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground mb-2">
                        类别: {issue.category} | 检查类型: {issue.check_type}
                      </div>
                      {issue.details && (
                        <div className="text-xs bg-gray-50 p-2 rounded">
                          <pre>{JSON.stringify(issue.details, null, 2)}</pre>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  暂无质量问题
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 受保护路由组件
 */

import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/components/auth-provider'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const location = useLocation()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    // 保存当前路径，登录后重定向
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return <>{children}</>
}

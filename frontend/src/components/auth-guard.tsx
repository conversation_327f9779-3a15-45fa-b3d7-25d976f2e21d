/**
 * 认证守卫组件
 */

import { useEffect } from 'react'
import { useLocation, Navigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/auth'
import { Loader2 } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { user, token, isAuthenticated, isLoading, refreshUser } = useAuthStore()
  const location = useLocation()

  useEffect(() => {
    // 如果有token但没有用户信息，尝试获取用户信息
    if (token && !user && !isLoading) {
      refreshUser().catch(() => {
        // 如果获取用户信息失败，清除认证状态
        // 这会在store中自动处理
      })
    }
  }, [token, user, isLoading, refreshUser])

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>加载中...</span>
        </div>
      </div>
    )
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return <>{children}</>
}

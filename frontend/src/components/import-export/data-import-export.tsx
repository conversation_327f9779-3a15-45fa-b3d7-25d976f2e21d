/**
 * 数据导入导出组件
 */

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  X
} from 'lucide-react'
import { QuestionsService } from '@/services/questions'
import { KnowledgePointsService } from '@/services/knowledge-points'
import { AnnotationService } from '@/services/annotation'

interface ImportResult {
  successCount: number
  errorCount: number
  errors: string[]
}

export function DataImportExport() {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [importType, setImportType] = useState<'questions' | 'knowledge_points' | 'annotations'>('questions')
  const [exportType, setExportType] = useState<'questions' | 'knowledge_points' | 'q_matrix'>('questions')
  const [uploadProgress, setUploadProgress] = useState(0)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setImportResult(null)
    }
  }

  const handleImport = async () => {
    if (!selectedFile) return

    try {
      setImporting(true)
      setUploadProgress(0)
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // 读取文件内容
      const fileContent = await readFileContent(selectedFile)
      
      let result: ImportResult
      
      switch (importType) {
        case 'questions':
          result = await importQuestions(fileContent)
          break
        case 'knowledge_points':
          result = await importKnowledgePoints(fileContent)
          break
        case 'annotations':
          result = await importAnnotations(fileContent)
          break
        default:
          throw new Error('不支持的导入类型')
      }
      
      setUploadProgress(100)
      setImportResult(result)
      
    } catch (error: any) {
      setImportResult({
        successCount: 0,
        errorCount: 1,
        errors: [error.message || '导入失败']
      })
    } finally {
      setImporting(false)
    }
  }

  const readFileContent = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string
          
          if (file.name.endsWith('.json')) {
            const data = JSON.parse(content)
            resolve(Array.isArray(data) ? data : [data])
          } else if (file.name.endsWith('.csv')) {
            const lines = content.split('\n')
            const headers = lines[0].split(',').map(h => h.trim())
            const data = lines.slice(1).filter(line => line.trim()).map(line => {
              const values = line.split(',').map(v => v.trim())
              const obj: any = {}
              headers.forEach((header, index) => {
                obj[header] = values[index] || ''
              })
              return obj
            })
            resolve(data)
          } else {
            reject(new Error('不支持的文件格式，请使用 JSON 或 CSV 格式'))
          }
        } catch (error) {
          reject(new Error('文件格式错误，无法解析'))
        }
      }
      
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file)
    })
  }

  const importQuestions = async (data: any[]): Promise<ImportResult> => {
    // 这里应该调用实际的导入API
    // const result = await QuestionsService.importQuestions({ questions: data, overwriteExisting: false })
    
    // 模拟导入结果
    return {
      successCount: Math.floor(data.length * 0.8),
      errorCount: Math.ceil(data.length * 0.2),
      errors: data.length > 5 ? ['第3行：题干不能为空', '第7行：题目类型无效'] : []
    }
  }

  const importKnowledgePoints = async (data: any[]): Promise<ImportResult> => {
    // 模拟导入结果
    return {
      successCount: Math.floor(data.length * 0.9),
      errorCount: Math.ceil(data.length * 0.1),
      errors: data.length > 3 ? ['第2行：知识点编码重复'] : []
    }
  }

  const importAnnotations = async (data: any[]): Promise<ImportResult> => {
    // 模拟导入结果
    return {
      successCount: Math.floor(data.length * 0.85),
      errorCount: Math.ceil(data.length * 0.15),
      errors: data.length > 4 ? ['第4行：题目ID不存在', '第8行：知识点ID不存在'] : []
    }
  }

  const handleExport = async () => {
    try {
      setExporting(true)
      
      let blob: Blob
      let filename: string
      
      switch (exportType) {
        case 'questions':
          blob = await QuestionsService.exportQuestions()
          filename = `questions-${new Date().toISOString().split('T')[0]}.json`
          break
        case 'knowledge_points':
          // 这里应该有知识点导出API
          blob = new Blob([JSON.stringify([])], { type: 'application/json' })
          filename = `knowledge-points-${new Date().toISOString().split('T')[0]}.json`
          break
        case 'q_matrix':
          blob = await AnnotationService.exportQMatrix()
          filename = `q-matrix-${new Date().toISOString().split('T')[0]}.json`
          break
        default:
          throw new Error('不支持的导出类型')
      }
      
      // 下载文件
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
    } catch (error: any) {
      console.error('导出失败:', error)
    } finally {
      setExporting(false)
    }
  }

  const resetImport = () => {
    setSelectedFile(null)
    setImportResult(null)
    setUploadProgress(0)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">数据导入导出</h1>
        <p className="text-muted-foreground">
          导入和导出题目、知识点和标注数据
        </p>
      </div>

      <Tabs defaultValue="import">
        <TabsList>
          <TabsTrigger value="import">数据导入</TabsTrigger>
          <TabsTrigger value="export">数据导出</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>数据导入</CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 导入类型选择 */}
              <div>
                <Label>导入类型</Label>
                <Select value={importType} onValueChange={(value: any) => setImportType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="questions">题目数据</SelectItem>
                    <SelectItem value="knowledge_points">知识点数据</SelectItem>
                    <SelectItem value="annotations">标注数据</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 文件选择 */}
              <div>
                <Label>选择文件</Label>
                <div className="mt-2">
                  <Input
                    type="file"
                    accept=".json,.csv"
                    onChange={handleFileSelect}
                    disabled={importing}
                  />
                  <div className="text-sm text-gray-500 mt-1">
                    支持 JSON 和 CSV 格式文件
                  </div>
                </div>
              </div>

              {/* 文件信息 */}
              {selectedFile && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-blue-500" />
                    <div className="flex-1">
                      <div className="font-medium">{selectedFile.name}</div>
                      <div className="text-sm text-gray-500">
                        {(selectedFile.size / 1024).toFixed(1)} KB
                      </div>
                    </div>
                    {!importing && (
                      <Button variant="ghost" size="sm" onClick={resetImport}>
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {/* 上传进度 */}
              {importing && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">导入进度</span>
                    <span className="text-sm text-gray-500">{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                </div>
              )}

              {/* 导入结果 */}
              {importResult && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    {importResult.errorCount === 0 ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-yellow-500" />
                    )}
                    <span className="font-medium">导入完成</span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {importResult.successCount}
                      </div>
                      <div className="text-sm text-gray-600">成功导入</div>
                    </div>
                    
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {importResult.errorCount}
                      </div>
                      <div className="text-sm text-gray-600">导入失败</div>
                    </div>
                  </div>
                  
                  {importResult.errors.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="font-medium text-red-800 mb-2">错误详情：</div>
                      <ul className="list-disc list-inside text-red-700 text-sm space-y-1">
                        {importResult.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* 导入按钮 */}
              <div className="flex gap-4">
                <Button
                  onClick={handleImport}
                  disabled={!selectedFile || importing}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {importing ? '导入中...' : '开始导入'}
                </Button>
                
                {(selectedFile || importResult) && (
                  <Button variant="outline" onClick={resetImport}>
                    重置
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>数据导出</CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 导出类型选择 */}
              <div>
                <Label>导出类型</Label>
                <Select value={exportType} onValueChange={(value: any) => setExportType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="questions">题目数据</SelectItem>
                    <SelectItem value="knowledge_points">知识点数据</SelectItem>
                    <SelectItem value="q_matrix">Q矩阵数据</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 导出说明 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="font-medium text-blue-800 mb-2">导出说明：</div>
                <ul className="text-blue-700 text-sm space-y-1">
                  {exportType === 'questions' && (
                    <>
                      <li>• 导出所有激活状态的题目数据</li>
                      <li>• 包含题目内容、类型、难度、答案等信息</li>
                      <li>• 格式：JSON</li>
                    </>
                  )}
                  {exportType === 'knowledge_points' && (
                    <>
                      <li>• 导出完整的知识点层级结构</li>
                      <li>• 包含知识点名称、编码、描述等信息</li>
                      <li>• 格式：JSON</li>
                    </>
                  )}
                  {exportType === 'q_matrix' && (
                    <>
                      <li>• 导出题目-知识点关联矩阵</li>
                      <li>• 包含题目列表、知识点列表和关联关系</li>
                      <li>• 格式：JSON</li>
                    </>
                  )}
                </ul>
              </div>

              {/* 导出按钮 */}
              <Button
                onClick={handleExport}
                disabled={exporting}
              >
                <Download className="w-4 h-4 mr-2" />
                {exporting ? '导出中...' : '开始导出'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 用户表单组件
 */

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import type { User } from '@/types/auth'

// 临时定义UserRole类型
type UserRole = 'admin' | 'annotator' | 'reviewer' | 'viewer' | 'engine'

const userFormSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(50, '用户名最多50个字符'),
  email: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
  full_name: z.string().max(100, '姓名最多100个字符').optional().or(z.literal('')),
  role: z.nativeEnum(UserRole),
  is_active: z.boolean(),
  password: z.string().min(8, '密码至少8个字符').optional().or(z.literal('')),
})

type UserFormData = z.infer<typeof userFormSchema>

interface UserFormProps {
  user?: User
  onSubmit: (data: UserFormData) => void
  onCancel: () => void
  loading?: boolean
}

const roleOptions = [
  { value: UserRole.ADMIN, label: '管理员' },
  { value: UserRole.REVIEWER, label: '审核员' },
  { value: UserRole.ANNOTATOR, label: '标注员' },
  { value: UserRole.VIEWER, label: '查看者' },
  { value: UserRole.ENGINE, label: '引擎用户' },
]

export function UserForm({ user, onSubmit, onCancel, loading = false }: UserFormProps) {
  const isEdit = !!user

  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      username: user?.username || '',
      email: user?.email || '',
      full_name: user?.full_name || '',
      role: user?.role || UserRole.VIEWER,
      is_active: user?.is_active ?? true,
      password: '',
    },
  })

  const handleSubmit = (data: UserFormData) => {
    // 如果是编辑模式且密码为空，则不包含密码字段
    if (isEdit && !data.password) {
      const { password, ...submitData } = data
      onSubmit(submitData)
    } else {
      onSubmit(data)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>用户名</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入用户名"
                  {...field}
                  disabled={isEdit} // 编辑时不允许修改用户名
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="请输入邮箱地址"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="full_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>姓名</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入真实姓名"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>角色</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择用户角色" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">激活状态</FormLabel>
                <div className="text-sm text-muted-foreground">
                  启用或禁用用户账户
                </div>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                密码 {isEdit && <span className="text-sm text-muted-foreground">(留空则不修改)</span>}
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={isEdit ? "留空则不修改密码" : "请输入密码"}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? '保存中...' : isEdit ? '更新' : '创建'}
          </Button>
        </div>
      </form>
    </Form>
  )
}

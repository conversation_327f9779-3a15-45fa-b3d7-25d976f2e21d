/**
 * 知识点列表组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, Plus, Edit, Trash2, Eye, TreePine } from 'lucide-react'
import { KnowledgePointsService, type KnowledgePointFilters, type KnowledgePoint } from '@/services/knowledge-points'

interface KnowledgePointListProps {
  onEdit?: (kp: KnowledgePoint) => void
  onView?: (kp: KnowledgePoint) => void
  onDelete?: (kp: KnowledgePoint) => void
  onCreateNew?: () => void
  onViewTree?: () => void
}

export function KnowledgePointList({ 
  onEdit, 
  onView, 
  onDelete, 
  onCreateNew, 
  onViewTree 
}: KnowledgePointListProps) {
  const [knowledgePoints, setKnowledgePoints] = useState<KnowledgePoint[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [filters, setFilters] = useState<KnowledgePointFilters>({})

  const difficultyLevels = KnowledgePointsService.getDifficultyLevels()

  useEffect(() => {
    loadKnowledgePoints()
  }, [currentPage, filters])

  const loadKnowledgePoints = async () => {
    try {
      setLoading(true)
      const skip = (currentPage - 1) * pageSize
      const response = await KnowledgePointsService.getKnowledgePoints(skip, pageSize, filters)
      setKnowledgePoints(response.items)
      setTotal(response.total)
    } catch (error) {
      console.error('加载知识点列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
    setCurrentPage(1)
  }

  const handleFilterChange = (key: keyof KnowledgePointFilters, value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      [key]: value === 'all' ? undefined : value 
    }))
    setCurrentPage(1)
  }

  const getDifficultyBadge = (level?: number) => {
    if (!level) return null
    
    const colors = {
      1: 'bg-green-100 text-green-800',
      2: 'bg-blue-100 text-blue-800',
      3: 'bg-yellow-100 text-yellow-800',
      4: 'bg-orange-100 text-orange-800',
      5: 'bg-red-100 text-red-800'
    }
    
    const difficultyLevel = difficultyLevels.find(d => d.value === level)
    
    return (
      <Badge className={colors[level as keyof typeof colors]}>
        {difficultyLevel?.label || `难度 ${level}`}
      </Badge>
    )
  }

  const getPathDisplay = (path: string) => {
    return KnowledgePointsService.buildPathDisplay(path)
  }

  const totalPages = Math.ceil(total / pageSize)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>知识点管理</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onViewTree}>
              <TreePine className="w-4 h-4 mr-2" />
              树形视图
            </Button>
            <Button onClick={onCreateNew}>
              <Plus className="w-4 h-4 mr-2" />
              新建知识点
            </Button>
          </div>
        </div>
        
        {/* 搜索和筛选 */}
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索知识点名称、编码或描述..."
                className="pl-10"
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>
          
          <Select onValueChange={(value) => handleFilterChange('difficultyLevel', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="选择难度" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部难度</SelectItem>
              {difficultyLevels.map(level => (
                <SelectItem key={level.value} value={level.value.toString()}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select onValueChange={(value) => handleFilterChange('isLeaf', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="节点类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="true">叶子节点</SelectItem>
              <SelectItem value="false">分支节点</SelectItem>
            </SelectContent>
          </Select>
          
          <Select onValueChange={(value) => handleFilterChange('parentId', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="层级筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部层级</SelectItem>
              <SelectItem value="null">根节点</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>知识点信息</TableHead>
                  <TableHead>编码</TableHead>
                  <TableHead>层级路径</TableHead>
                  <TableHead>难度</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {knowledgePoints.map((kp) => (
                  <TableRow key={kp.kpId}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{kp.name}</div>
                        {kp.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {kp.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{kp.code}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-600 max-w-xs truncate">
                        {getPathDisplay(kp.path)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getDifficultyBadge(kp.difficultyLevel)}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={kp.isLeaf ? "default" : "secondary"}
                        className={kp.isLeaf ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"}
                      >
                        {kp.isLeaf ? '叶子节点' : '分支节点'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(kp.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onView?.(kp)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit?.(kp)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete?.(kp)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  共 {total} 条记录，第 {currentPage} / {totalPages} 页
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => prev - 1)}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(prev => prev + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

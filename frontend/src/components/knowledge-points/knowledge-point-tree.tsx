/**
 * 知识点树形视图组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronRight, 
  ChevronDown, 
  Plus, 
  Edit, 
  Trash2, 
  List,
  Folder,
  FileText
} from 'lucide-react'
import { 
  KnowledgePointsService, 
  type KnowledgePointTreeNode,
  type KnowledgePoint
} from '@/services/knowledge-points'

interface KnowledgePointTreeProps {
  onEdit?: (kp: KnowledgePoint) => void
  onDelete?: (kp: KnowledgePoint) => void
  onCreateNew?: (parentId?: number) => void
  onBackToList?: () => void
}

interface TreeNodeProps {
  node: KnowledgePointTreeNode
  level: number
  onEdit?: (kp: KnowledgePoint) => void
  onDelete?: (kp: KnowledgePoint) => void
  onCreateChild?: (parentId: number) => void
}

function TreeNode({ node, level, onEdit, onDelete, onCreateChild }: TreeNodeProps) {
  const [expanded, setExpanded] = useState(level < 2) // 默认展开前两级
  
  const hasChildren = node.children.length > 0
  const indent = level * 24 // 每级缩进24px
  
  const difficultyLevels = KnowledgePointsService.getDifficultyLevels()
  
  const getDifficultyBadge = (level?: number) => {
    if (!level) return null
    
    const colors = {
      1: 'bg-green-100 text-green-800',
      2: 'bg-blue-100 text-blue-800',
      3: 'bg-yellow-100 text-yellow-800',
      4: 'bg-orange-100 text-orange-800',
      5: 'bg-red-100 text-red-800'
    }
    
    const difficultyLevel = difficultyLevels.find(d => d.value === level)
    
    return (
      <Badge className={colors[level as keyof typeof colors]} size="sm">
        {difficultyLevel?.label || `难度 ${level}`}
      </Badge>
    )
  }
  
  const handleNodeClick = () => {
    if (hasChildren) {
      setExpanded(!expanded)
    }
  }
  
  // 转换为KnowledgePoint格式（简化版）
  const toKnowledgePoint = (node: KnowledgePointTreeNode): KnowledgePoint => ({
    kpId: node.kpId,
    name: node.name,
    code: node.code,
    description: node.description,
    difficultyLevel: node.difficultyLevel,
    isLeaf: node.isLeaf,
    path: '', // 这里简化处理
    parentId: undefined, // 这里简化处理
    createdBy: 0,
    createdAt: '',
    updatedAt: ''
  })
  
  return (
    <div>
      <div 
        className="flex items-center py-2 px-3 hover:bg-gray-50 rounded-lg group"
        style={{ marginLeft: `${indent}px` }}
      >
        {/* 展开/收起按钮 */}
        <div className="w-6 h-6 flex items-center justify-center">
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="w-6 h-6 p-0"
              onClick={handleNodeClick}
            >
              {expanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </Button>
          ) : null}
        </div>
        
        {/* 节点图标 */}
        <div className="w-6 h-6 flex items-center justify-center mr-2">
          {node.isLeaf ? (
            <FileText className="w-4 h-4 text-blue-500" />
          ) : (
            <Folder className="w-4 h-4 text-yellow-500" />
          )}
        </div>
        
        {/* 节点内容 */}
        <div className="flex-1 flex items-center gap-3">
          <div className="flex-1">
            <div className="font-medium">{node.name}</div>
            <div className="text-sm text-gray-500">编码: {node.code}</div>
            {node.description && (
              <div className="text-sm text-gray-400 truncate max-w-md">
                {node.description}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {getDifficultyBadge(node.difficultyLevel)}
            
            <Badge 
              variant={node.isLeaf ? "default" : "secondary"}
              className={node.isLeaf ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"}
            >
              {node.isLeaf ? '叶子' : '分支'}
            </Badge>
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCreateChild?.(node.kpId)}
            title="添加子知识点"
          >
            <Plus className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit?.(toKnowledgePoint(node))}
            title="编辑"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete?.(toKnowledgePoint(node))}
            title="删除"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      {/* 子节点 */}
      {hasChildren && expanded && (
        <div>
          {node.children.map(child => (
            <TreeNode
              key={child.kpId}
              node={child}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
              onCreateChild={onCreateChild}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function KnowledgePointTree({ 
  onEdit, 
  onDelete, 
  onCreateNew, 
  onBackToList 
}: KnowledgePointTreeProps) {
  const [tree, setTree] = useState<KnowledgePointTreeNode[]>([])
  const [loading, setLoading] = useState(false)
  const [expandAll, setExpandAll] = useState(false)

  useEffect(() => {
    loadTree()
  }, [])

  const loadTree = async () => {
    try {
      setLoading(true)
      const response = await KnowledgePointsService.getKnowledgeTree()
      setTree(response.tree)
    } catch (error) {
      console.error('加载知识点树失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateChild = (parentId: number) => {
    onCreateNew?.(parentId)
  }

  const handleExpandAll = () => {
    setExpandAll(!expandAll)
    // 这里可以实现全部展开/收起的逻辑
    // 由于状态在子组件中，这里简化处理
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>知识点树形结构</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExpandAll}>
              {expandAll ? '全部收起' : '全部展开'}
            </Button>
            <Button variant="outline" onClick={onBackToList}>
              <List className="w-4 h-4 mr-2" />
              列表视图
            </Button>
            <Button onClick={() => onCreateNew?.()}>
              <Plus className="w-4 h-4 mr-2" />
              新建根节点
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : tree.length > 0 ? (
          <div className="space-y-1">
            {tree.map(node => (
              <TreeNode
                key={node.kpId}
                node={node}
                level={0}
                onEdit={onEdit}
                onDelete={onDelete}
                onCreateChild={handleCreateChild}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Folder className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <div>暂无知识点</div>
            <Button className="mt-4" onClick={() => onCreateNew?.()}>
              <Plus className="w-4 h-4 mr-2" />
              创建第一个知识点
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

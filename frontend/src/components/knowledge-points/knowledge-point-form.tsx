/**
 * 知识点表单组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Save, X } from 'lucide-react'
import { 
  KnowledgePointsService, 
  type KnowledgePointCreateRequest, 
  type KnowledgePointUpdateRequest,
  type KnowledgePoint,
  type KnowledgePointTreeNode
} from '@/services/knowledge-points'

interface KnowledgePointFormProps {
  knowledgePoint?: KnowledgePoint
  onSave?: (kp: KnowledgePoint) => void
  onCancel?: () => void
}

export function KnowledgePointForm({ knowledgePoint, onSave, onCancel }: KnowledgePointFormProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [tree, setTree] = useState<KnowledgePointTreeNode[]>([])
  
  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    difficultyLevel: undefined as number | undefined,
    parentId: undefined as number | undefined
  })
  
  const difficultyLevels = KnowledgePointsService.getDifficultyLevels()
  
  useEffect(() => {
    loadTree()
    
    if (knowledgePoint) {
      setFormData({
        name: knowledgePoint.name,
        code: knowledgePoint.code,
        description: knowledgePoint.description || '',
        difficultyLevel: knowledgePoint.difficultyLevel,
        parentId: knowledgePoint.parentId
      })
    }
  }, [knowledgePoint])

  const loadTree = async () => {
    try {
      const response = await KnowledgePointsService.getKnowledgeTree()
      setTree(response.tree)
    } catch (error) {
      console.error('加载知识点树失败:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const requestData = {
      name: formData.name,
      code: formData.code,
      description: formData.description || undefined,
      difficultyLevel: formData.difficultyLevel,
      parentId: formData.parentId
    }
    
    // 验证数据
    const validation = KnowledgePointsService.validateKnowledgePoint(requestData)
    if (!validation.isValid) {
      setErrors(validation.errors)
      return
    }
    
    // 检查是否会形成循环（编辑时）
    if (knowledgePoint && formData.parentId) {
      const canSet = KnowledgePointsService.canSetAsParent(
        knowledgePoint.kpId, 
        formData.parentId, 
        tree
      )
      if (!canSet) {
        setErrors(['不能设置为子知识点的父节点，会形成循环'])
        return
      }
    }
    
    try {
      setLoading(true)
      setErrors([])
      
      let result: KnowledgePoint
      if (knowledgePoint) {
        result = await KnowledgePointsService.updateKnowledgePoint(
          knowledgePoint.kpId, 
          requestData as KnowledgePointUpdateRequest
        )
      } else {
        result = await KnowledgePointsService.createKnowledgePoint(
          requestData as KnowledgePointCreateRequest
        )
      }
      
      onSave?.(result)
    } catch (error: any) {
      setErrors([error.response?.data?.detail || '保存失败'])
    } finally {
      setLoading(false)
    }
  }

  // 递归渲染树形选项
  const renderTreeOptions = (nodes: KnowledgePointTreeNode[], level = 0): JSX.Element[] => {
    const options: JSX.Element[] = []
    
    for (const node of nodes) {
      // 编辑时不能选择自己作为父节点
      if (knowledgePoint && node.kpId === knowledgePoint.kpId) {
        continue
      }
      
      const indent = '　'.repeat(level) // 使用全角空格缩进
      options.push(
        <SelectItem key={node.kpId} value={node.kpId.toString()}>
          {indent}{node.name} ({node.code})
        </SelectItem>
      )
      
      // 递归添加子节点
      if (node.children.length > 0) {
        options.push(...renderTreeOptions(node.children, level + 1))
      }
    }
    
    return options
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{knowledgePoint ? '编辑知识点' : '新建知识点'}</CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 错误提示 */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <ul className="list-disc list-inside text-red-600">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {/* 知识点名称 */}
          <div>
            <Label>知识点名称 *</Label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="请输入知识点名称..."
            />
          </div>
          
          {/* 知识点编码 */}
          <div>
            <Label>知识点编码 *</Label>
            <Input
              value={formData.code}
              onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
              placeholder="请输入知识点编码（字母、数字、下划线、连字符）..."
            />
            <div className="text-sm text-gray-500 mt-1">
              编码用于唯一标识知识点，只能包含字母、数字、下划线和连字符
            </div>
          </div>
          
          {/* 父级知识点 */}
          <div>
            <Label>父级知识点</Label>
            <Select 
              value={formData.parentId?.toString() || ''} 
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                parentId: value ? parseInt(value) : undefined 
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择父级知识点（可选）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">无（根节点）</SelectItem>
                {renderTreeOptions(tree)}
              </SelectContent>
            </Select>
            <div className="text-sm text-gray-500 mt-1">
              选择父级知识点可以建立层级关系，不选择则为根节点
            </div>
          </div>
          
          {/* 难度等级 */}
          <div>
            <Label>难度等级</Label>
            <Select 
              value={formData.difficultyLevel?.toString() || ''} 
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                difficultyLevel: value ? parseInt(value) : undefined 
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择难度等级（可选）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">不设置</SelectItem>
                {difficultyLevels.map(level => (
                  <SelectItem key={level.value} value={level.value.toString()}>
                    {level.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* 知识点描述 */}
          <div>
            <Label>知识点描述</Label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="请输入知识点描述..."
              rows={4}
            />
          </div>
          
          {/* 操作按钮 */}
          <div className="flex gap-4">
            <Button type="submit" disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? '保存中...' : '保存'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              取消
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

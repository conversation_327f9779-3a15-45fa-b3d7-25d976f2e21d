/**
 * 版本比较组件
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  GitCompare, 
  ArrowRight, 
  Plus, 
  Minus, 
  Edit,
  RefreshCw
} from 'lucide-react'
import { 
  VersionsService, 
  QuestionVersionResponse, 
  KpVersionResponse,
  VersionCompareRequest,
  VersionCompareResponse
} from '@/services/versions'
import { QuestionsService } from '@/services/questions'
import { KnowledgePointsService } from '@/services/knowledge-points'
import { useToast } from '@/components/ui/use-toast'

interface EntityOption {
  id: number
  name: string
}

export function VersionComparison() {
  const [loading, setLoading] = useState(false)
  const [compareLoading, setCompareLoading] = useState(false)
  const [type, setType] = useState<'question' | 'kp'>('question')
  const [entities, setEntities] = useState<EntityOption[]>([])
  const [selectedEntityId, setSelectedEntityId] = useState<number | null>(null)
  const [versions, setVersions] = useState<(QuestionVersionResponse | KpVersionResponse)[]>([])
  const [sourceVersionId, setSourceVersionId] = useState<number | null>(null)
  const [targetVersionId, setTargetVersionId] = useState<number | null>(null)
  const [comparison, setComparison] = useState<VersionCompareResponse | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    loadEntities()
  }, [type])

  useEffect(() => {
    if (selectedEntityId) {
      loadVersions()
    }
  }, [selectedEntityId, type])

  const loadEntities = async () => {
    try {
      setLoading(true)
      if (type === 'question') {
        const response = await QuestionsService.getQuestions(0, 1000)
        setEntities(response.items.map(q => ({ id: q.question_id, name: q.content.stem || '未命名题目' })))
      } else {
        const response = await KnowledgePointsService.getKnowledgePoints(0, 1000)
        setEntities(response.items.map(kp => ({ id: kp.kp_id, name: kp.name })))
      }
    } catch (error) {
      console.error('加载实体列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadVersions = async () => {
    if (!selectedEntityId) return

    try {
      setLoading(true)
      if (type === 'question') {
        const versionsData = await VersionsService.getQuestionVersions(selectedEntityId)
        setVersions(versionsData)
      } else {
        const versionsData = await VersionsService.getKpVersions(selectedEntityId)
        setVersions(versionsData)
      }
    } catch (error) {
      console.error('加载版本列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCompare = async () => {
    if (!sourceVersionId || !targetVersionId) {
      toast({
        title: "错误",
        description: "请选择要比较的两个版本",
        variant: "destructive",
      })
      return
    }

    if (sourceVersionId === targetVersionId) {
      toast({
        title: "错误",
        description: "不能比较相同的版本",
        variant: "destructive",
      })
      return
    }

    try {
      setCompareLoading(true)
      const request: VersionCompareRequest = {
        source_version_id: sourceVersionId,
        target_version_id: targetVersionId
      }

      const result = await VersionsService.compareVersions(request, type)
      setComparison(result)
    } catch (error) {
      console.error('版本比较失败:', error)
      toast({
        title: "比较失败",
        description: "操作失败，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setCompareLoading(false)
    }
  }

  const renderDifferences = (differences: Record<string, any>) => {
    const { added, removed, modified } = differences

    return (
      <div className="space-y-6">
        {/* 新增字段 */}
        {added && Object.keys(added).length > 0 && (
          <div>
            <h4 className="flex items-center gap-2 text-sm font-medium text-green-700 mb-3">
              <Plus className="h-4 w-4" />
              新增字段
            </h4>
            <div className="space-y-2">
              {Object.entries(added).map(([key, value]) => (
                <div key={key} className="p-3 bg-green-50 border border-green-200 rounded">
                  <div className="font-medium text-sm text-green-800">{key}</div>
                  <div className="text-xs text-green-700 mt-1">
                    {typeof value === 'object' ? (
                      <pre className="overflow-x-auto">{JSON.stringify(value, null, 2)}</pre>
                    ) : (
                      String(value)
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 删除字段 */}
        {removed && Object.keys(removed).length > 0 && (
          <div>
            <h4 className="flex items-center gap-2 text-sm font-medium text-red-700 mb-3">
              <Minus className="h-4 w-4" />
              删除字段
            </h4>
            <div className="space-y-2">
              {Object.entries(removed).map(([key, value]) => (
                <div key={key} className="p-3 bg-red-50 border border-red-200 rounded">
                  <div className="font-medium text-sm text-red-800">{key}</div>
                  <div className="text-xs text-red-700 mt-1">
                    {typeof value === 'object' ? (
                      <pre className="overflow-x-auto">{JSON.stringify(value, null, 2)}</pre>
                    ) : (
                      String(value)
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 修改字段 */}
        {modified && Object.keys(modified).length > 0 && (
          <div>
            <h4 className="flex items-center gap-2 text-sm font-medium text-yellow-700 mb-3">
              <Edit className="h-4 w-4" />
              修改字段
            </h4>
            <div className="space-y-2">
              {Object.entries(modified).map(([key, change]) => (
                <div key={key} className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <div className="font-medium text-sm text-yellow-800 mb-2">{key}</div>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <div className="text-red-600 font-medium mb-1">旧值:</div>
                      <div className="text-red-700">
                        {typeof (change as any).old === 'object' ? (
                          <pre className="overflow-x-auto">{JSON.stringify((change as any).old, null, 2)}</pre>
                        ) : (
                          String((change as any).old)
                        )}
                      </div>
                    </div>
                    <div>
                      <div className="text-green-600 font-medium mb-1">新值:</div>
                      <div className="text-green-700">
                        {typeof (change as any).new === 'object' ? (
                          <pre className="overflow-x-auto">{JSON.stringify((change as any).new, null, 2)}</pre>
                        ) : (
                          String((change as any).new)
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 无差异 */}
        {(!added || Object.keys(added).length === 0) &&
         (!removed || Object.keys(removed).length === 0) &&
         (!modified || Object.keys(modified).length === 0) && (
          <div className="text-center py-8 text-muted-foreground">
            <GitCompare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>两个版本没有差异</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 比较设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            版本比较
          </CardTitle>
          <CardDescription>
            选择两个版本进行详细比较，查看它们之间的差异
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 类型选择 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">比较类型</label>
              <Select value={type} onValueChange={(value: 'question' | 'kp') => setType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="question">题目版本</SelectItem>
                  <SelectItem value="kp">知识点版本</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">选择{type === 'question' ? '题目' : '知识点'}</label>
              <Select onValueChange={(value) => setSelectedEntityId(Number(value))}>
                <SelectTrigger>
                  <SelectValue placeholder={`选择${type === 'question' ? '题目' : '知识点'}`} />
                </SelectTrigger>
                <SelectContent>
                  {entities.map(entity => (
                    <SelectItem key={entity.id} value={entity.id.toString()}>
                      {entity.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 版本选择 */}
          {selectedEntityId && versions.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <div className="space-y-2">
                <label className="text-sm font-medium">源版本</label>
                <Select onValueChange={(value) => setSourceVersionId(Number(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择源版本" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.map(version => (
                      <SelectItem key={version.version_id} value={version.version_id.toString()}>
                        v{version.version_number}
                        {version.version_tag && ` (${version.version_tag})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-center">
                <ArrowRight className="h-6 w-6 text-muted-foreground" />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">目标版本</label>
                <Select onValueChange={(value) => setTargetVersionId(Number(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择目标版本" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.map(version => (
                      <SelectItem key={version.version_id} value={version.version_id.toString()}>
                        v{version.version_number}
                        {version.version_tag && ` (${version.version_tag})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* 比较按钮 */}
          <div className="flex justify-center">
            <Button 
              onClick={handleCompare}
              disabled={!sourceVersionId || !targetVersionId || compareLoading}
            >
              {compareLoading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              开始比较
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 比较结果 */}
      {comparison && (
        <Card>
          <CardHeader>
            <CardTitle>比较结果</CardTitle>
            <CardDescription>
              显示两个版本之间的详细差异
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 版本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">源版本</h4>
                <div className="space-y-1 text-sm">
                  <div>版本号: v{comparison.source_version.version_number}</div>
                  <div>创建时间: {new Date(comparison.source_version.created_at).toLocaleString('zh-CN')}</div>
                  {comparison.source_version.change_description && (
                    <div>描述: {comparison.source_version.change_description}</div>
                  )}
                </div>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">目标版本</h4>
                <div className="space-y-1 text-sm">
                  <div>版本号: v{comparison.target_version.version_number}</div>
                  <div>创建时间: {new Date(comparison.target_version.created_at).toLocaleString('zh-CN')}</div>
                  {comparison.target_version.change_description && (
                    <div>描述: {comparison.target_version.change_description}</div>
                  )}
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            {/* 差异详情 */}
            <ScrollArea className="h-96">
              {renderDifferences(comparison.differences)}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

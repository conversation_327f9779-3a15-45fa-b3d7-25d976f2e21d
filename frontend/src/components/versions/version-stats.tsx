/**
 * 版本统计组件
 */

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON>, TrendingUp } from 'lucide-react'
import { VersionStatsResponse, ChangeTypeLabels } from '@/services/versions'

interface VersionStatsProps {
  stats: VersionStatsResponse
}

export function VersionStats({ stats }: VersionStatsProps) {
  const publishRate = stats.total_versions > 0 
    ? (stats.published_versions / stats.total_versions) * 100 
    : 0

  const draftRate = stats.total_versions > 0 
    ? (stats.draft_versions / stats.total_versions) * 100 
    : 0

  const getChangeTypeColor = (changeType: string) => {
    const colors = {
      'CREATE': 'bg-green-500',
      'UPDATE': 'bg-blue-500',
      'DELETE': 'bg-red-500',
      'RESTORE': 'bg-yellow-500'
    }
    return colors[changeType as keyof typeof colors] || 'bg-gray-500'
  }

  const totalChanges = Object.values(stats.change_type_stats).reduce((sum, count) => sum + count, 0)

  return (
    <div className="space-y-6">
      {/* 发布状态统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            发布状态分布
          </CardTitle>
          <CardDescription>
            显示版本的发布状态分布情况
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm">已发布版本</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{stats.published_versions}</span>
                <Badge variant="outline">{publishRate.toFixed(1)}%</Badge>
              </div>
            </div>
            <Progress value={publishRate} className="h-2" />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <span className="text-sm">草稿版本</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{stats.draft_versions}</span>
                <Badge variant="outline">{draftRate.toFixed(1)}%</Badge>
              </div>
            </div>
            <Progress value={draftRate} className="h-2" />
          </div>

          <div className="pt-2 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">总版本数</span>
              <span className="font-bold">{stats.total_versions}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 变更类型统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            变更类型分布
          </CardTitle>
          <CardDescription>
            显示不同类型变更的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(stats.change_type_stats).map(([changeType, count]) => {
            const percentage = totalChanges > 0 ? (count / totalChanges) * 100 : 0
            
            return (
              <div key={changeType} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${getChangeTypeColor(changeType)}`}></div>
                    <span className="text-sm">{ChangeTypeLabels[changeType as keyof typeof ChangeTypeLabels]}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{count}</span>
                    <Badge variant="outline">{percentage.toFixed(1)}%</Badge>
                  </div>
                </div>
                <Progress value={percentage} className="h-2" />
              </div>
            )
          })}

          {totalChanges === 0 && (
            <div className="text-center py-4 text-sm text-muted-foreground">
              暂无变更记录
            </div>
          )}
        </CardContent>
      </Card>

      {/* 活动趋势 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            活动趋势
          </CardTitle>
          <CardDescription>
            显示最近的版本活动趋势
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div>
                <p className="text-sm font-medium">今日新增版本</p>
                <p className="text-2xl font-bold text-primary">{stats.recent_changes}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">活跃度</p>
                <div className="flex items-center gap-1">
                  {stats.recent_changes > 0 ? (
                    <>
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-500">活跃</span>
                    </>
                  ) : (
                    <>
                      <div className="h-4 w-4 bg-gray-400 rounded-full"></div>
                      <span className="text-sm text-muted-foreground">平静</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">发布率</p>
                <p className="text-lg font-bold">{publishRate.toFixed(1)}%</p>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">平均版本数</p>
                <p className="text-lg font-bold">
                  {stats.total_versions > 0 ? (stats.total_versions / Math.max(1, Object.keys(stats.change_type_stats).length)).toFixed(1) : '0'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

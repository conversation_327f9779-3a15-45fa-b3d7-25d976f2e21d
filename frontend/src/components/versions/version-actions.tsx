/**
 * 版本操作组件
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  RotateCcw, 
  Rocket, 
  AlertTriangle, 
  CheckCircle,
  Loader2
} from 'lucide-react'
import { 
  VersionsService, 
  QuestionVersionResponse, 
  KpVersionResponse,
  VersionRollbackRequest,
  VersionPublishRequest
} from '@/services/versions'
import { useToast } from '@/components/ui/use-toast'

interface VersionActionsProps {
  version: QuestionVersionResponse | KpVersionResponse
  type: 'question' | 'kp'
  onActionComplete?: () => void
}

export function VersionActions({ version, type, onActionComplete }: VersionActionsProps) {
  const [loading, setLoading] = useState(false)
  const [rollbackReason, setRollbackReason] = useState('')
  const [publishNote, setPublishNote] = useState('')
  const [rollbackDialogOpen, setRollbackDialogOpen] = useState(false)
  const [publishDialogOpen, setPublishDialogOpen] = useState(false)
  const { toast } = useToast()

  const handleRollback = async () => {
    if (!rollbackReason.trim()) {
      toast({
        title: "错误",
        description: "请输入回滚原因",
        variant: "destructive",
      })
      return
    }

    try {
      setLoading(true)
      const request: VersionRollbackRequest = {
        target_version_id: version.version_id,
        rollback_reason: rollbackReason
      }

      const result = await VersionsService.rollbackVersion(request, type)
      
      if (result.success) {
        toast({
          title: "回滚成功",
          description: result.message,
        })
        setRollbackDialogOpen(false)
        setRollbackReason('')
        onActionComplete?.()
      } else {
        toast({
          title: "回滚失败",
          description: result.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('回滚失败:', error)
      toast({
        title: "回滚失败",
        description: "操作失败，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePublish = async () => {
    try {
      setLoading(true)
      const request: VersionPublishRequest = {
        version_id: version.version_id,
        publish_note: publishNote.trim() || undefined
      }

      const result = await VersionsService.publishVersion(request, type)
      
      if (result.success) {
        toast({
          title: "发布成功",
          description: result.message,
        })
        setPublishDialogOpen(false)
        setPublishNote('')
        onActionComplete?.()
      } else {
        toast({
          title: "发布失败",
          description: result.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('发布失败:', error)
      toast({
        title: "发布失败",
        description: "操作失败，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const canRollback = !version.is_current
  const canPublish = !version.is_published

  return (
    <div className="flex gap-2">
      {/* 回滚按钮 */}
      {canRollback && (
        <Dialog open={rollbackDialogOpen} onOpenChange={setRollbackDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <RotateCcw className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                回滚到版本 v{version.version_number}
              </DialogTitle>
              <DialogDescription>
                此操作将创建一个新版本，其内容与选定版本相同。这是一个安全操作，不会删除任何历史记录。
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">v{version.version_number}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {new Date(version.created_at).toLocaleString('zh-CN')}
                  </span>
                </div>
                {version.change_description && (
                  <p className="text-sm">{version.change_description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="rollback-reason">回滚原因 *</Label>
                <Textarea
                  id="rollback-reason"
                  placeholder="请说明回滚的原因..."
                  value={rollbackReason}
                  onChange={(e) => setRollbackReason(e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setRollbackDialogOpen(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button 
                onClick={handleRollback}
                disabled={loading || !rollbackReason.trim()}
              >
                {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                确认回滚
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 发布按钮 */}
      {canPublish && (
        <Dialog open={publishDialogOpen} onOpenChange={setPublishDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default" size="sm">
              <Rocket className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                发布版本 v{version.version_number}
              </DialogTitle>
              <DialogDescription>
                发布后，此版本将标记为已发布状态，可以在生产环境中使用。
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">v{version.version_number}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {new Date(version.created_at).toLocaleString('zh-CN')}
                  </span>
                </div>
                {version.change_description && (
                  <p className="text-sm">{version.change_description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="publish-note">发布说明（可选）</Label>
                <Textarea
                  id="publish-note"
                  placeholder="添加发布说明..."
                  value={publishNote}
                  onChange={(e) => setPublishNote(e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setPublishDialogOpen(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button 
                onClick={handlePublish}
                disabled={loading}
              >
                {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                确认发布
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 已发布状态显示 */}
      {version.is_published && (
        <Badge variant="default" className="bg-green-500">
          <CheckCircle className="h-3 w-3 mr-1" />
          已发布
        </Badge>
      )}

      {/* 当前版本状态显示 */}
      {version.is_current && !version.is_published && (
        <Badge variant="secondary">
          当前版本
        </Badge>
      )}
    </div>
  )
}

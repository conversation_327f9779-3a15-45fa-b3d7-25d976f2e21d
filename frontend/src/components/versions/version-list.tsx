/**
 * 版本列表组件
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Search, GitBranch, Clock, User, Eye, RotateCcw, 
  Rocket, AlertTriangle, CheckCircle 
} from 'lucide-react'
import { 
  VersionsService, 
  QuestionVersionResponse, 
  KpVersionResponse, 
  ChangeType, 
  ChangeTypeLabels 
} from '@/services/versions'
import { QuestionsService } from '@/services/questions'
import { KnowledgePointsService } from '@/services/knowledge-points'
import { VersionDetail } from './version-detail'
import { VersionActions } from './version-actions'

interface VersionListProps {
  type: 'question' | 'kp'
}

interface EntityOption {
  id: number
  name: string
}

export function VersionList({ type }: VersionListProps) {
  const [loading, setLoading] = useState(false)
  const [versions, setVersions] = useState<(QuestionVersionResponse | KpVersionResponse)[]>([])
  const [entities, setEntities] = useState<EntityOption[]>([])
  const [selectedEntityId, setSelectedEntityId] = useState<number | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedVersion, setSelectedVersion] = useState<QuestionVersionResponse | KpVersionResponse | null>(null)

  useEffect(() => {
    loadEntities()
  }, [type])

  useEffect(() => {
    if (selectedEntityId) {
      loadVersions()
    }
  }, [selectedEntityId, type])

  const loadEntities = async () => {
    try {
      setLoading(true)
      if (type === 'question') {
        const response = await QuestionsService.getQuestions(0, 1000)
        setEntities(response.items.map(q => ({ id: q.question_id, name: q.content.stem || '未命名题目' })))
      } else {
        const response = await KnowledgePointsService.getKnowledgePoints(0, 1000)
        setEntities(response.items.map(kp => ({ id: kp.kp_id, name: kp.name })))
      }
    } catch (error) {
      console.error('加载实体列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadVersions = async () => {
    if (!selectedEntityId) return

    try {
      setLoading(true)
      if (type === 'question') {
        const versionsData = await VersionsService.getQuestionVersions(selectedEntityId)
        setVersions(versionsData)
      } else {
        const versionsData = await VersionsService.getKpVersions(selectedEntityId)
        setVersions(versionsData)
      }
    } catch (error) {
      console.error('加载版本列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredEntities = entities.filter(entity =>
    entity.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getChangeTypeBadge = (changeType: ChangeType) => {
    const variants = {
      [ChangeType.CREATE]: 'default',
      [ChangeType.UPDATE]: 'secondary',
      [ChangeType.DELETE]: 'destructive',
      [ChangeType.RESTORE]: 'outline'
    } as const

    return (
      <Badge variant={variants[changeType]}>
        {ChangeTypeLabels[changeType]}
      </Badge>
    )
  }

  const getStatusBadge = (version: QuestionVersionResponse | KpVersionResponse) => {
    if (version.is_published) {
      return <Badge variant="default" className="bg-green-500">已发布</Badge>
    } else if (version.is_current) {
      return <Badge variant="secondary">当前版本</Badge>
    } else {
      return <Badge variant="outline">历史版本</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  return (
    <div className="space-y-6">
      {/* 实体选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            {type === 'question' ? '题目版本管理' : '知识点版本管理'}
          </CardTitle>
          <CardDescription>
            选择{type === 'question' ? '题目' : '知识点'}查看其版本历史
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder={`搜索${type === 'question' ? '题目' : '知识点'}...`}
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* 实体选择 */}
          <Select onValueChange={(value) => setSelectedEntityId(Number(value))}>
            <SelectTrigger>
              <SelectValue placeholder={`选择${type === 'question' ? '题目' : '知识点'}`} />
            </SelectTrigger>
            <SelectContent>
              {filteredEntities.map(entity => (
                <SelectItem key={entity.id} value={entity.id.toString()}>
                  {entity.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* 版本列表 */}
      {selectedEntityId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              版本历史
            </CardTitle>
            <CardDescription>
              显示所选{type === 'question' ? '题目' : '知识点'}的所有版本记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2 text-sm text-muted-foreground">加载中...</p>
              </div>
            ) : versions.length === 0 ? (
              <div className="text-center py-8">
                <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">暂无版本记录</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>版本号</TableHead>
                    <TableHead>变更类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>变更描述</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {versions.map((version) => (
                    <TableRow key={version.version_id}>
                      <TableCell className="font-medium">
                        v{version.version_number}
                        {version.version_tag && (
                          <Badge variant="outline" className="ml-2">
                            {version.version_tag}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {getChangeTypeBadge(version.change_type)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(version)}
                      </TableCell>
                      <TableCell className="max-w-xs truncate">
                        {version.change_description || '无描述'}
                      </TableCell>
                      <TableCell>
                        {formatDate(version.created_at)}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => setSelectedVersion(version)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl">
                              <DialogHeader>
                                <DialogTitle>版本详情 - v{version.version_number}</DialogTitle>
                                <DialogDescription>
                                  查看版本的详细信息和数据快照
                                </DialogDescription>
                              </DialogHeader>
                              {selectedVersion && (
                                <VersionDetail version={selectedVersion} type={type} />
                              )}
                            </DialogContent>
                          </Dialog>
                          
                          <VersionActions 
                            version={version} 
                            type={type} 
                            onActionComplete={loadVersions}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

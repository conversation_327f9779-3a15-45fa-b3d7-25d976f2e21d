/**
 * 版本管理主界面
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, GitBranch, Clock, TrendingUp } from 'lucide-react'
import { VersionsService, VersionStatsResponse } from '@/services/versions'
import { VersionList } from './version-list'
import { VersionStats } from './version-stats'
import { VersionComparison } from './version-comparison'

export function VersionManagement() {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<VersionStatsResponse | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setLoading(true)
      const statsData = await VersionsService.getVersionStats()
      setStats(statsData)
    } catch (error) {
      console.error('加载版本统计失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">版本管理</h1>
          <p className="text-muted-foreground">
            管理知识结构数据的版本控制、发布流程和回滚机制
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={loadStats}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计概览卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总版本数</CardTitle>
              <GitBranch className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_versions}</div>
              <p className="text-xs text-muted-foreground">
                包含所有题目和知识点版本
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已发布版本</CardTitle>
              <Badge variant="default" className="h-4 w-4 rounded-full p-0" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.published_versions}</div>
              <p className="text-xs text-muted-foreground">
                发布率: {((stats.published_versions / stats.total_versions) * 100).toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">草稿版本</CardTitle>
              <Badge variant="secondary" className="h-4 w-4 rounded-full p-0" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.draft_versions}</div>
              <p className="text-xs text-muted-foreground">
                待发布的版本数量
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最近变更</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recent_changes}</div>
              <p className="text-xs text-muted-foreground">
                今日新增版本数
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="questions">题目版本</TabsTrigger>
          <TabsTrigger value="knowledge-points">知识点版本</TabsTrigger>
          <TabsTrigger value="comparison">版本比较</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 版本统计图表 */}
            {stats && <VersionStats stats={stats} />}
            
            {/* 最近版本活动 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  最近版本活动
                </CardTitle>
                <CardDescription>
                  显示最近的版本创建和发布活动
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground text-center py-8">
                    暂无最近活动数据
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          <VersionList type="question" />
        </TabsContent>

        <TabsContent value="knowledge-points" className="space-y-4">
          <VersionList type="kp" />
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <VersionComparison />
        </TabsContent>
      </Tabs>
    </div>
  )
}

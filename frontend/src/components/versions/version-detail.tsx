/**
 * 版本详情组件
 */

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  FileText, Clock, User, GitBranch, 
  CheckCircle, AlertCircle, Info 
} from 'lucide-react'
import { 
  QuestionVersionResponse, 
  KpVersionResponse, 
  ChangeType, 
  ChangeTypeLabels 
} from '@/services/versions'

interface VersionDetailProps {
  version: QuestionVersionResponse | KpVersionResponse
  type: 'question' | 'kp'
}

export function VersionDetail({ version, type }: VersionDetailProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getChangeTypeBadge = (changeType: ChangeType) => {
    const variants = {
      [ChangeType.CREATE]: 'default',
      [ChangeType.UPDATE]: 'secondary',
      [ChangeType.DELETE]: 'destructive',
      [ChangeType.RESTORE]: 'outline'
    } as const

    return (
      <Badge variant={variants[changeType]}>
        {ChangeTypeLabels[changeType]}
      </Badge>
    )
  }

  const getStatusBadge = () => {
    if (version.is_published) {
      return (
        <Badge variant="default" className="bg-green-500">
          <CheckCircle className="h-3 w-3 mr-1" />
          已发布
        </Badge>
      )
    } else if (version.is_current) {
      return (
        <Badge variant="secondary">
          <AlertCircle className="h-3 w-3 mr-1" />
          当前版本
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline">
          <Info className="h-3 w-3 mr-1" />
          历史版本
        </Badge>
      )
    }
  }

  const renderSnapshotData = (data: Record<string, any>) => {
    return (
      <div className="space-y-2">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="grid grid-cols-3 gap-4 py-2 border-b border-muted">
            <div className="font-medium text-sm">{key}</div>
            <div className="col-span-2 text-sm">
              {typeof value === 'object' ? (
                <pre className="text-xs bg-muted p-2 rounded overflow-x-auto">
                  {JSON.stringify(value, null, 2)}
                </pre>
              ) : (
                <span className="break-words">{String(value)}</span>
              )}
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderDiffData = (diffData: Record<string, any>) => {
    if (!diffData || Object.keys(diffData).length === 0) {
      return (
        <div className="text-center py-4 text-sm text-muted-foreground">
          无差异数据
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {Object.entries(diffData).map(([key, value]) => {
          const isChange = key.startsWith('changed_')
          const isAdd = key.startsWith('added_')
          const isRemove = key.startsWith('removed_')
          
          let bgColor = 'bg-muted'
          let textColor = 'text-foreground'
          
          if (isAdd) {
            bgColor = 'bg-green-50 border-green-200'
            textColor = 'text-green-800'
          } else if (isRemove) {
            bgColor = 'bg-red-50 border-red-200'
            textColor = 'text-red-800'
          } else if (isChange) {
            bgColor = 'bg-yellow-50 border-yellow-200'
            textColor = 'text-yellow-800'
          }

          return (
            <div key={key} className={`p-3 rounded border ${bgColor} ${textColor}`}>
              <div className="font-medium text-sm mb-2">{key}</div>
              <div className="text-xs">
                {typeof value === 'object' ? (
                  <pre className="overflow-x-auto">
                    {JSON.stringify(value, null, 2)}
                  </pre>
                ) : (
                  <span>{String(value)}</span>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 版本基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            版本信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">版本号</label>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-lg font-bold">v{version.version_number}</span>
                {version.version_tag && (
                  <Badge variant="outline">{version.version_tag}</Badge>
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">状态</label>
              <div className="mt-1">
                {getStatusBadge()}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">变更类型</label>
              <div className="mt-1">
                {getChangeTypeBadge(version.change_type)}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">创建时间</label>
              <div className="mt-1 text-sm">
                {formatDate(version.created_at)}
              </div>
            </div>
          </div>

          {version.change_description && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">变更描述</label>
              <div className="mt-1 p-3 bg-muted rounded text-sm">
                {version.change_description}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 数据快照 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            数据快照
          </CardTitle>
          <CardDescription>
            该版本的完整数据快照
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            {renderSnapshotData(version.snapshot_data)}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* 差异数据 */}
      {version.diff_data && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              变更差异
            </CardTitle>
            <CardDescription>
              与前一版本的差异对比
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              {renderDiffData(version.diff_data)}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

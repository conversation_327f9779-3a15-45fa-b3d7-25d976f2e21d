/**
 * 用户管理状态管理
 */

import { create } from 'zustand'
import { User } from '@/types/auth'
import { usersService, CreateUserData, UpdateUserData } from '@/services/users'

interface UsersState {
  users: User[]
  total: number
  loading: boolean
  error: string | null
  selectedUser: User | null
}

interface UsersActions {
  fetchUsers: (skip?: number, limit?: number) => Promise<void>
  createUser: (data: CreateUserData) => Promise<void>
  updateUser: (userId: string, data: UpdateUserData) => Promise<void>
  deleteUser: (userId: string) => Promise<void>
  activateUser: (userId: string) => Promise<void>
  deactivateUser: (userId: string) => Promise<void>
  setSelectedUser: (user: User | null) => void
  clearError: () => void
}

interface UsersStore extends UsersState, UsersActions {}

export const useUsersStore = create<UsersStore>((set, get) => ({
  // 状态
  users: [],
  total: 0,
  loading: false,
  error: null,
  selectedUser: null,

  // 操作
  fetchUsers: async (skip = 0, limit = 100) => {
    try {
      set({ loading: true, error: null })
      
      const response = await usersService.getUsers(skip, limit)
      
      set({
        users: response.items,
        total: response.total,
        loading: false,
      })
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '获取用户列表失败',
      })
    }
  },

  createUser: async (data: CreateUserData) => {
    try {
      set({ loading: true, error: null })
      
      const newUser = await usersService.createUser(data)
      
      set((state) => ({
        users: [...state.users, newUser],
        total: state.total + 1,
        loading: false,
      }))
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '创建用户失败',
      })
      throw error
    }
  },

  updateUser: async (userId: string, data: UpdateUserData) => {
    try {
      set({ loading: true, error: null })
      
      const updatedUser = await usersService.updateUser(userId, data)
      
      set((state) => ({
        users: state.users.map((user) =>
          user.user_id.toString() === userId ? updatedUser : user
        ),
        selectedUser: state.selectedUser?.user_id.toString() === userId ? updatedUser : state.selectedUser,
        loading: false,
      }))
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '更新用户失败',
      })
      throw error
    }
  },

  deleteUser: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      
      await usersService.deleteUser(userId)
      
      set((state) => ({
        users: state.users.filter((user) => user.user_id.toString() !== userId),
        total: state.total - 1,
        selectedUser: state.selectedUser?.user_id.toString() === userId ? null : state.selectedUser,
        loading: false,
      }))
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '删除用户失败',
      })
      throw error
    }
  },

  activateUser: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      
      await usersService.activateUser(userId)
      
      set((state) => ({
        users: state.users.map((user) =>
          user.user_id.toString() === userId ? { ...user, is_active: true } : user
        ),
        loading: false,
      }))
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '激活用户失败',
      })
      throw error
    }
  },

  deactivateUser: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      
      await usersService.deactivateUser(userId)
      
      set((state) => ({
        users: state.users.map((user) =>
          user.user_id.toString() === userId ? { ...user, is_active: false } : user
        ),
        loading: false,
      }))
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '停用用户失败',
      })
      throw error
    }
  },

  setSelectedUser: (user: User | null) => {
    set({ selectedUser: user })
  },

  clearError: () => {
    set({ error: null })
  },
}))

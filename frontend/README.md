# 前端应用

基于 React + TypeScript + Vite 的数据标注前端应用。

## 技术栈

- **React 18** - UI框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Radix UI** - 无障碍组件库
- **Tailwind CSS** - 原子化CSS框架
- **React Router** - 路由管理
- **React Query** - 数据获取和缓存
- **Zustand** - 状态管理

## 项目结构

```
src/
├── components/        # 可复用组件
│   ├── ui/           # 基础UI组件
│   ├── forms/        # 表单组件
│   └── layout/       # 布局组件
├── pages/            # 页面组件
│   ├── auth/         # 认证相关页面
│   ├── annotation/   # 标注相关页面
│   ├── knowledge/    # 知识点管理页面
│   └── admin/        # 管理员页面
├── hooks/            # 自定义Hooks
├── services/         # API服务
├── stores/           # 状态管理
├── types/            # TypeScript类型定义
├── utils/            # 工具函数
├── styles/           # 样式文件
└── assets/           # 静态资源
```

## 主要功能模块

### 1. 用户认证
- 登录/注册
- 角色权限控制
- JWT令牌管理

### 2. 题目管理
- 题目列表和搜索
- 题目详情展示
- 富文本渲染
- 多媒体支持

### 3. 知识点管理
- 知识点树形结构
- 层级管理
- 先修关系编辑

### 4. 数据标注
- 题-知识点关联标注
- 题-题关系标注
- 批量标注操作
- 标注质量检测

### 5. 可视化
- 知识图谱可视化
- 数据统计图表
- 标注进度展示

## 开发指南

### 环境设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 代码规范

- 使用 ESLint + Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 组件使用函数式组件 + Hooks
- 样式使用 Tailwind CSS 类名

### 测试

```bash
# 运行单元测试
npm run test

# 运行E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

## 部署

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm run preview
```

### Docker部署
```bash
docker build -t annotation-frontend .
docker run -p 3000:3000 annotation-frontend
```

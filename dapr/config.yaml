apiVersion: dapr.io/v1alpha1
kind: Configuration
metadata:
  name: appconfig
spec:
  tracing:
    samplingRate: "1"
    zipkin:
      endpointAddress: "http://localhost:9411/api/v2/spans"
  metric:
    enabled: true
  accessControl:
    defaultAction: allow
    trustDomain: "public"
    policies:
    - appId: annotation-backend
      defaultAction: allow
      trustDomain: 'public'
      namespace: "default"
  nameResolution:
    component: "mdns"
    version: "v1"
    configuration:
      selfRegister: true

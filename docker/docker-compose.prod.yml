version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: annotation_postgres_prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./database/scripts/init_extensions.sql:/docker-entrypoint-initdb.d/01_init_extensions.sql
    networks:
      - annotation_prod_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: annotation_redis_prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
    networks:
      - annotation_prod_network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 后端API服务
  backend:
    build:
      context: ..
      dockerfile: docker/backend.Dockerfile
      target: production
    container_name: annotation_backend_prod
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
      - ENVIRONMENT=production
      - CORS_ORIGINS=${CORS_ORIGINS}
    volumes:
      - backend_prod_uploads:/app/uploads
      - backend_prod_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - annotation_prod_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 前端应用
  frontend:
    build:
      context: ..
      dockerfile: docker/frontend.Dockerfile
      target: production
    container_name: annotation_frontend_prod
    depends_on:
      - backend
    networks:
      - annotation_prod_network
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: annotation_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_prod_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - annotation_prod_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/nginx-health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: annotation_prometheus_prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_prod_data:/prometheus
    networks:
      - annotation_prod_network
    restart: always
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 日志收集 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: annotation_grafana_prod
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_prod_data:/var/lib/grafana
    networks:
      - annotation_prod_network
    restart: always
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  backend_prod_uploads:
    driver: local
  backend_prod_logs:
    driver: local
  nginx_prod_logs:
    driver: local
  prometheus_prod_data:
    driver: local
  grafana_prod_data:
    driver: local

networks:
  annotation_prod_network:
    driver: bridge
    name: annotation_prod_network

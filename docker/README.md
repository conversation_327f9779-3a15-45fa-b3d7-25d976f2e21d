# Docker 配置

包含项目的 Docker 配置文件和容器编排配置。

## 文件说明

- `frontend.Dockerfile` - 前端应用容器配置
- `backend.Dockerfile` - 后端API容器配置
- `docker-compose.yml` - 完整服务编排
- `docker-compose.dev.yml` - 开发环境配置
- `docker-compose.prod.yml` - 生产环境配置

## 服务架构

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │
│   (React)       │◄──►│   (FastAPI)     │
│   Port: 3000    │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐    ┌─────────────────┐
         │   PostgreSQL    │    │     Redis       │
         │   Port: 5432    │    │   Port: 6379    │
         └─────────────────┘    └─────────────────┘
```

## 快速启动

### 开发环境

```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境

```bash
# 构建并启动
docker-compose -f docker-compose.prod.yml up -d --build

# 扩展后端服务
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

## 服务配置

### Frontend (React)
- **端口**: 3000
- **构建**: Multi-stage build with Nginx
- **环境变量**:
  - `VITE_API_URL` - 后端API地址
  - `VITE_APP_ENV` - 应用环境

### Backend (FastAPI)
- **端口**: 8000
- **运行时**: Python 3.11
- **环境变量**:
  - `DATABASE_URL` - 数据库连接
  - `REDIS_URL` - Redis连接
  - `SECRET_KEY` - JWT密钥

### PostgreSQL
- **端口**: 5432
- **版本**: 15-alpine
- **扩展**: ltree, pgrouting
- **数据卷**: `postgres_data`

### Redis
- **端口**: 6379
- **版本**: 7-alpine
- **用途**: 缓存、会话存储

## 环境变量

创建 `.env` 文件：

```env
# 数据库配置
POSTGRES_DB=annotation
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
DATABASE_URL=*************************************************/annotation

# Redis配置
REDIS_URL=redis://redis:6379/0

# 应用配置
SECRET_KEY=your_secret_key
CORS_ORIGINS=http://localhost:3000

# 前端配置
VITE_API_URL=http://localhost:8000
VITE_APP_ENV=development
```

## 数据持久化

### 数据卷

- `postgres_data` - PostgreSQL数据
- `redis_data` - Redis数据
- `uploads` - 文件上传存储

### 备份策略

```bash
# 数据库备份
docker-compose exec postgres pg_dump -U postgres annotation > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres annotation < backup.sql
```

## 网络配置

### 开发环境
- 前端: http://localhost:3000
- 后端: http://localhost:8000
- 数据库: localhost:5432
- Redis: localhost:6379

### 生产环境
- 使用反向代理 (Nginx)
- SSL/TLS 终止
- 负载均衡

## 监控与日志

### 健康检查

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### 日志配置

```bash
# 查看特定服务日志
docker-compose logs -f backend

# 查看最近100行日志
docker-compose logs --tail=100 frontend
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :8000
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER .
   ```

### 调试模式

```bash
# 进入容器调试
docker-compose exec backend bash
docker-compose exec frontend sh

# 查看容器资源使用
docker stats
```

## 部署最佳实践

1. **安全配置**
   - 使用非root用户运行容器
   - 限制容器资源使用
   - 定期更新基础镜像

2. **性能优化**
   - 使用多阶段构建减小镜像大小
   - 配置适当的资源限制
   - 启用容器健康检查

3. **生产部署**
   - 使用容器编排平台 (Kubernetes)
   - 配置自动扩缩容
   - 实施滚动更新策略

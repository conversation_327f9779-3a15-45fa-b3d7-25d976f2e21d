# 数据库配置
POSTGRES_DB=annotation
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here
DATABASE_URL=postgresql://postgres:your_secure_password_here@localhost:5432/annotation

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用安全配置
SECRET_KEY=your_very_secure_secret_key_change_in_production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 前端配置
VITE_API_URL=http://localhost:8000
VITE_APP_ENV=development
VITE_APP_NAME=数据标注系统

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_TLS=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Celery配置（异步任务）
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 开发模式配置
DEBUG=true
RELOAD=true

# 生产环境配置（生产环境时启用）
# DEBUG=false
# RELOAD=false
# CORS_ORIGINS=https://yourdomain.com
# VITE_API_URL=https://api.yourdomain.com

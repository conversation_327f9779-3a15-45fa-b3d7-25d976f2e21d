# 自适应学习服务数据标注模块 - Cursor Rules

## 数据库连接配置

### Supabase 连接信息
- **连接字符串**: `postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres`
- **数据库类型**: PostgreSQL (Supabase)
- **编码**: UTF-8
- **时区**: UTC

### 环境变量设置
```bash
DATABASE_URL=postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres
DB_HOST=aws-0-ap-southeast-1.pooler.supabase.com
DB_PORT=6543
DB_NAME=postgres
DB_USER=postgres.qlpqyctsezzfzdgvfvir
DB_PASSWORD=UZyzHE1hy9kOddMv
```

## 数据库架构规范

### 核心表结构
1. **用户与权限模块**
   - `users` - 系统账户
   - 角色类型: `admin`, `annotator`, `reviewer`, `viewer`, `engine`

2. **知识本体模块**
   - `knowledge_points` - 知识点/技能定义
   - `prerequisite_relation` - 知识点先修关系

3. **题库模块**
   - `questions` - 题目主表
   - `question_assets` - 媒资文件
   - `item_param` - IRT 参数

4. **映射关系模块**
   - `item_kp_map` - 题-知识点映射(Q-矩阵)
   - `question_relation` - 题-题关系

5. **知识空间模块**
   - `knowledge_state` - 知识状态
   - `state_transition` - 状态转移

6. **标注工作流模块**
   - `annotation_tasks` - 标注任务
   - `annotation_logs` - 操作审计

7. **版本管理模块**
   - `question_versions` - 题目快照
   - `kp_versions` - 知识点快照

8. **模型参数模块**
   - `skill_param` - BKT/技能参数

### 数据类型约定
- 主键: `BIGSERIAL` 或 `SERIAL`
- 时间戳: `TIMESTAMPTZ`
- JSON 数据: `JSONB`
- 布尔值: `BOOLEAN`
- 路径: `LTREE` (知识点层级)
- 位向量: `BIT VARYING`

### 枚举类型
```sql
-- 题型枚举
CREATE TYPE q_type AS ENUM ('SC', 'MC', 'TF', 'Cloze', 'Essay');

-- 题-题关系枚举
CREATE TYPE q_rel_type AS ENUM ('complements', 'progresses_to', 'equivalent', 'prerequisite', 'revision');

-- 任务状态枚举
CREATE TYPE task_state AS ENUM ('pending', 'in_progress', 'review', 'done');
```

## 开发规范

### SQL 编写规范
1. **命名约定**
   - 表名: 小写下划线分隔 (`knowledge_points`)
   - 字段名: 小写下划线分隔 (`created_at`)
   - 外键: `表名_id` 格式 (`user_id`)

2. **索引策略**
   - 主键自动创建索引
   - 外键字段创建索引
   - 查询频繁字段创建复合索引
   - 全文搜索使用 GIN 索引

3. **约束规则**
   - 所有表必须有主键
   - 外键关系明确定义
   - 非空约束合理设置
   - 唯一约束防止重复

### 数据完整性
1. **引用完整性**
   - 所有外键必须有对应的主键
   - 级联删除策略明确定义

2. **业务约束**
   - 先修关系无环检查
   - 知识点层级路径一致性
   - 题目-知识点映射有效性

3. **版本控制**
   - 所有修改记录到审计表
   - 关键数据变更创建快照
   - 支持版本回滚功能

### 性能优化
1. **查询优化**
   - 使用适当的索引
   - 避免 N+1 查询
   - 使用 EXPLAIN 分析查询计划

2. **数据分区**
   - 大表考虑按时间分区
   - 历史数据归档策略

3. **缓存策略**
   - 频繁查询数据使用 Redis 缓存
   - 知识空间状态内存缓存

## API 设计规范

### RESTful 端点设计
```
GET    /api/v1/knowledge-points          # 获取知识点列表
POST   /api/v1/knowledge-points          # 创建知识点
GET    /api/v1/knowledge-points/{id}     # 获取单个知识点
PUT    /api/v1/knowledge-points/{id}     # 更新知识点
DELETE /api/v1/knowledge-points/{id}     # 删除知识点

GET    /api/v1/questions                 # 获取题目列表
POST   /api/v1/questions                 # 创建题目
GET    /api/v1/questions/{id}            # 获取单个题目
PUT    /api/v1/questions/{id}            # 更新题目
DELETE /api/v1/questions/{id}            # 删除题目

GET    /api/v1/annotations/tasks         # 获取标注任务
POST   /api/v1/annotations/tasks         # 创建标注任务
PUT    /api/v1/annotations/tasks/{id}    # 更新任务状态
```

### 数据验证规则
1. **输入验证**
   - 所有用户输入必须验证
   - 使用 JSON Schema 验证请求体
   - 参数类型和范围检查

2. **业务逻辑验证**
   - 知识点先修关系无环
   - 题目-知识点映射合理性
   - 用户权限检查

## 安全规范

### 数据库安全
1. **访问控制**
   - 使用最小权限原则
   - 不同角色不同权限
   - 敏感操作需要审计

2. **数据加密**
   - 密码使用 Argon2 或 bcrypt
   - 敏感数据传输加密
   - 数据库连接使用 SSL

3. **SQL 注入防护**
   - 使用参数化查询
   - 输入验证和转义
   - 使用 ORM 框架

### 备份与恢复
1. **备份策略**
   - 每日自动备份
   - 重要操作前手动备份
   - 备份文件加密存储

2. **恢复测试**
   - 定期测试备份恢复
   - 灾难恢复预案
   - 数据一致性检查

## 监控与日志

### 数据库监控
1. **性能指标**
   - 查询响应时间
   - 连接池使用率
   - 磁盘 I/O 性能

2. **业务指标**
   - 标注任务完成率
   - 数据质量评分
   - 用户活跃度

### 日志记录
1. **操作日志**
   - 所有数据修改记录
   - 用户登录活动
   - 系统异常日志

2. **审计日志**
   - 敏感操作记录
   - 权限变更记录
   - 数据导出记录

## 开发工具配置

### 推荐工具
- **数据库客户端**: DBeaver, pgAdmin
- **ORM 框架**: Prisma, TypeORM, SQLAlchemy
- **迁移工具**: Alembic, Prisma Migrate
- **测试工具**: pytest, Jest
- **监控工具**: Grafana, Prometheus

### 开发环境设置
```bash
# 安装依赖
npm install @supabase/supabase-js
pip install psycopg2-binary sqlalchemy alembic

# 环境变量配置
cp .env.example .env
# 编辑 .env 文件添加数据库连接信息
```

## 注意事项

1. **数据一致性**: 使用事务确保数据一致性
2. **性能考虑**: 大表查询使用分页和索引
3. **错误处理**: 完善的错误处理和回滚机制
4. **文档维护**: 及时更新数据库文档和 API 文档
5. **测试覆盖**: 编写单元测试和集成测试
6. **版本管理**: 使用语义化版本控制
7. **代码审查**: 所有数据库变更需要代码审查
8. **部署流程**: 使用 CI/CD 自动化部署流程 
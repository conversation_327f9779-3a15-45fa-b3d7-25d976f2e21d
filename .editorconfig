# EditorConfig配置文件
# 统一不同编辑器的代码风格
# https://editorconfig.org

root = true

# 所有文件的默认配置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Python文件
[*.py]
indent_size = 4
max_line_length = 88

# JavaScript/TypeScript文件
[*.{js,jsx,ts,tsx}]
indent_size = 2
max_line_length = 100

# JSON文件
[*.json]
indent_size = 2

# YAML文件
[*.{yml,yaml}]
indent_size = 2

# Markdown文件
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Makefile
[Makefile]
indent_style = tab

# Shell脚本
[*.sh]
indent_size = 2

# SQL文件
[*.sql]
indent_size = 2

# Docker文件
[Dockerfile*]
indent_size = 2

# 配置文件
[*.{ini,cfg,conf}]
indent_size = 2

# XML文件
[*.xml]
indent_size = 2

# HTML文件
[*.html]
indent_size = 2

# CSS文件
[*.{css,scss,sass,less}]
indent_size = 2

# 忽略生成的文件
[{package-lock.json,yarn.lock}]
insert_final_newline = false

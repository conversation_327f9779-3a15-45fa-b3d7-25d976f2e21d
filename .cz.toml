[tool.commitizen]
name = "cz_conventional_commits"
version = "0.1.0"
tag_format = "v$version"
version_files = [
    "backend/pyproject.toml:version",
    "frontend/package.json:version"
]
bump_message = "bump: version $current_version → $new_version"
update_changelog_on_bump = true
annotated_tag = true

[tool.commitizen.customize]
message_template = "{{change_type}}{{scope}}: {{message}}"
example = "feat(auth): 添加JWT认证功能"
schema = """
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
"""

schema_pattern = "^(feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert)(\\(.+\\))?: .{1,50}"

bump_pattern = "^(feat|fix|perf|BREAKING CHANGE)"
bump_map = {"BREAKING CHANGE" = "MAJOR", "feat" = "MINOR", "fix" = "PATCH", "perf" = "PATCH"}

change_type_order = ["BREAKING CHANGE", "feat", "fix", "refactor", "perf"]

[[tool.commitizen.customize.questions]]
type = "list"
name = "change_type"
choices = [
    {value = "feat", name = "feat: 新功能 (feature)"},
    {value = "fix", name = "fix: 修复bug"},
    {value = "docs", name = "docs: 文档变更"},
    {value = "style", name = "style: 代码格式化 (不影响功能)"},
    {value = "refactor", name = "refactor: 代码重构 (既不是新功能也不是修复bug)"},
    {value = "perf", name = "perf: 性能优化"},
    {value = "test", name = "test: 添加或修改测试"},
    {value = "build", name = "build: 构建系统或外部依赖变更"},
    {value = "ci", name = "ci: CI配置文件和脚本变更"},
    {value = "chore", name = "chore: 其他不修改src或test文件的变更"},
    {value = "revert", name = "revert: 回滚之前的提交"}
]
message = "选择提交类型:"

[[tool.commitizen.customize.questions]]
type = "input"
name = "scope"
message = "输入变更范围 (可选, 如: auth, ui, api):"

[[tool.commitizen.customize.questions]]
type = "input"
name = "subject"
message = "简短描述变更内容:"

[[tool.commitizen.customize.questions]]
type = "input"
name = "body"
message = "详细描述变更内容 (可选):"

[[tool.commitizen.customize.questions]]
type = "input"
name = "footer"
message = "关联的issue或breaking change (可选):"

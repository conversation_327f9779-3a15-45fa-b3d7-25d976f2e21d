# Git相关
.git
.gitignore
.gitattributes

# 文档
README.md
CONTRIBUTING.md
docs/
*.md

# 环境变量
.env
.env.*
!.env.example

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 前端相关
frontend/node_modules/
frontend/dist/
frontend/build/
frontend/.vite/
frontend/coverage/
frontend/.nyc_output/

# 后端相关
backend/__pycache__/
backend/*.py[cod]
backend/*$py.class
backend/*.so
backend/.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/*.egg-info/
backend/.installed.cfg
backend/*.egg
backend/MANIFEST
backend/venv/
backend/env/
backend/ENV/
backend/env.bak/
backend/venv.bak/
backend/.pytest_cache/
backend/.coverage
backend/htmlcov/
backend/.mypy_cache/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传文件
uploads/
media/

# 测试相关
.coverage
.pytest_cache/
.tox/
coverage.xml
*.cover
.hypothesis/

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 脚本文件
scripts/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 证书文件
*.pem
*.key
*.crt
ssl/

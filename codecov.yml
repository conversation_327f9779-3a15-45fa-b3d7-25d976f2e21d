# Codecov配置文件
# 配置代码覆盖率报告和检查

coverage:
  precision: 2
  round: down
  range: "70...100"
  
  status:
    project:
      default:
        target: 80%
        threshold: 1%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error
    
    patch:
      default:
        target: 80%
        threshold: 1%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error

  ignore:
    - "backend/tests/"
    - "backend/migrations/"
    - "backend/alembic/"
    - "frontend/src/test/"
    - "frontend/src/**/*.test.ts"
    - "frontend/src/**/*.test.tsx"
    - "frontend/src/**/*.spec.ts"
    - "frontend/src/**/*.spec.tsx"
    - "**/__pycache__/"
    - "**/node_modules/"
    - "**/dist/"
    - "**/build/"

flags:
  backend:
    paths:
      - backend/
    carryforward: true
  
  frontend:
    paths:
      - frontend/
    carryforward: true

component_management:
  default_rules:
    statuses:
      - type: project
        target: 80%
      - type: patch
        target: 80%
  
  individual_components:
    - component_id: backend
      name: Backend
      paths:
        - backend/app/
      
    - component_id: frontend
      name: Frontend
      paths:
        - frontend/src/

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes
  branches:
    - main
    - develop

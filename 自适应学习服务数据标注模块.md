这是一个为自适应学习学习系统提供数据标注的模块，自适应学习服务基于知识空间理论。

****知识空间理论（Knowledge Space Theory, KST）是一种将知识领域表示为知识结构的数学框架，其核心概念是知识状态：即学习者在某领域中可能掌握的一组知识点或题目集合。知识空间理论假设在知识领域中，不同题目或知识点之间存在先修关系（prerequisites），即掌握某些基础概念是学习更高级概念的前提。通过这些先修关系，可以形成题目/知识点之间的推断关系（surmise relations），构成一个偏序关系（quasi-order）结构。这一结构将所有可能的答题模式（即掌握不同子集知识的情况）约束为较少且有意义的知识状态集合，每个知识状态代表学习者能够解答的一组题目。这些知识状态的全体（在满足闭包等合理性公设下）构成该领域的知识空间。知识空间通常包含该领域所有可行的知识状态组合（例如 ALEKS 的代数领域模型中约350个知识点产生数以百万计的可行知识状态），但由于先修关系的存在，知识空间是闭包结构，可以通过高效算法推理和导航。实践中，基于知识空间理论的自适应学习系统（如 ALEKS、Knewton 等）能够利用这一结构实现对学生知识的精准评估和学习路径推荐

本方案旨在将知识空间理论集成到自适应学习系统的数据标注模块中，设计一个知识空间增强的标注模块。该模块将负责： (1) 基于题目与知识点的关联数据，自动推理知识空间结构（包括知识点先后关系和可能的知识状态集合）；(2) 提供可视化的接口供教研人员手动维护知识点的先修关系，以融合领域专家知识；(3) 支持将生成的知识结构与常用学生模型（如IRT项目反应理论、BKT贝叶斯知识追踪）结合，提供状态图、可达图或布尔向量等多种表示用于模型协同；(4) 设计适配PostgreSQL的数据模型和数据库，记录题目-知识点映射、知识点-知识点先修关系、知识空间结构以及学习状态转移等信息；(5) 借鉴 ALEKS、Knewton、ASSISTments 等成功应用案例的经验和同行评议研究成果，确保方案的有效性和前沿性。下面将分别从算法方案、人工标注接口、模型协同表示、数据库设计和模块集成流程等方面详细描述设计方案。

# 人工标注驱动的知识空间构建方案

## 0  背景与目标

| 目标         | 说明                                                         |
| ------------ | ------------------------------------------------------------ |
| **统一入口** | 只靠人工标注（教研专家 + 质检）即可完成 ① 题 ⇆ 知识点 关联 ② 知识点先修关系 ③ 题 ⇆ 题 必备/递进/等价 关系 |
| **输出**     | 一份 **可靠、可解释、版本化** 的知识结构 供 IRT / BKT / KST 及推荐引擎直接使用 |
## 1  角色 & 职责

| 角色             | 职责                                  | 人员配置         |
| ---------------- | ------------------------------------- | ---------------- |
| **内容标注员**   | 题-知识点、题-题 关系录入；难度首标   | 英语教研助教 × 4 |
| **知识结构专家** | 维护知识点层级 & 先修关系图；解决冲突 | 资深教研员 × 2   |
| **审核员 (QA)**  | 复核 100 % 标注；抽样做一致性检验     | 业务 QA × 1      |
| **数据发布员**   | 合并通过数据 → 数据库；打版本标签     | 数据工程师 × 1   |
## 2  数据工单流 (End-to-End)

```
题 / 知识点入库  →  标注任务生成
                        ↓
                标注员领取 & 提交
                        ↓
                 QA 审核（一次）
            ↙︎ 不通过        通过 ↘︎
      返回修改             归档发布
                        ↓
              触发 Dapr 事件
        knowledge-space.manual.updated
```

## 3  标注界面交互（Next.js / Radix UI）

| 区域             | 交互要点                                                     | 组件                       |
| ---------------- | ------------------------------------------------------------ | -------------------------- |
| **题目预览**     | 富文本渲染、Markdown渲染、可就地播放音频/图片放大            | `ScrollArea`, `Popover`    |
| **知识点选择**   | 左：树状目录 右：已选 Chip；支持搜索、键盘快捷键 `Enter` 选中 | `TreeView`, `Combobox`     |
| **题-题关系**    | 根据当前题展示「可能相关」列表（倒排搜索）；关系单选         | `DataTable`, `ToggleGroup` |
| **先修关系编辑** | 「知识点图」视图：拖拽连线、双击删除；实时检测环             | `Canvas`, `ContextMenu`    |
| **质检面板**     | 显示未标字段、置信度条、冲突预警                             | `Toast`, `Badge`           |
## 4  业务规则

### 4.1  题-知识点 (Q-KP)

| 规则          | 说明                                      |
| ------------- | ----------------------------------------- |
| **至少 1 KP** | 每题必须关联 ≥1 知识点                    |
| **置信度**    | 默认 1.0；标注员可调 0.6–1.0；<0.8 需备注 |
| **多媒体题**  | 若题干为整篇阅读 → 文章级 KP + 小题级 KP  |
### 4.2  知识点先修

| 规则         | 说明                                      |
| ------------ | ----------------------------------------- |
| **无环**     | 保存前进行 DFS 检测；检测到环弹窗阻止提交 |
| **粒度统一** | 只允许 leaf 节点连线；父级依赖自动继承    |
| **最小化**   | 若已存在 A→B, B→C，则禁止再录 A→C         |
### 4.3  题-题关系

| 类型            | 准则                                                         |
| --------------- | ------------------------------------------------------------ |
| *prerequisite*  | 先修题应 **完全覆盖** 高阶题的知识标签，且 IRT 难度 not higher |
| *progresses_to* | 相同知识点，b-值差≥0.3                                       |
| *equivalent*    | 同源改编；答案不一致；禁止同时标 `prerequisite`              |
## 5  数据一致性检测 (QA Checklist)

1. **孤儿知识点** — 无任何题引用 → 警告
2. **题目无 KP** 或 置信度 <0.6 → 拒收
3. **先修环** — 自动脚本阻断
4. **难度违背** `prerequisite` 边出现 `b(pre) > b(post)` (超出 0.2) → 标红
5. **双向边** prerequisite & progresses_to 重复 → 强制挑一

QA 工具：Python 脚本 + SQL 视图；结果写入 `annotation_logs`.
## 6  版本与发布

| 步骤         | 说明                                                         |
| ------------ | ------------------------------------------------------------ |
| **Merge**    | 审核通过的工单 → 合并到 **staging** schema                   |
| **Tag**      | 生成语义化版本 `ks-vMAJOR.MINOR.PATCH`                       |
| **发布脚本** | `INSERT … ON CONFLICT` 更新 `prerequisite_relation` / `item_kp_map` |
| **事件**     | Dapr Publish `knowledge-space.manual.updated` payload `{version, changed_edges, changed_maps}` |
| **Rollback** | 版本表 `kp_versions` / `question_versions` 一键回退          |
## 7  与自适应引擎集成

| 引擎侧需求                    | 标注满足方式                              |
| ----------------------------- | ----------------------------------------- |
| `GET /kps/{id}/prereqs`       | 读 `prerequisite_relation`                |
| `GET /questions/{id}/prereqs` | 题-题 `prerequisite`                      |
| 补短板推荐                    | 引擎根据题-题/知识点先修，找到所缺 A ← B  |
| 诊断压缩                      | 当学生对 B 正答 → 后端推断 A 掌握，跳过 A |

------

## 8  质效指标 (OKRs)

| 指标                   | 目标                 |
| ---------------------- | -------------------- |
| **标注一致性** (Kappa) | ≥ 0.85               |
| **QA 退回率**          | < 3 %                |
| **先修环率**           | 0                    |
| **上线周期**           | 工单→发布 ≤ 48 h     |
| **知识覆盖率**         | ≥ 95 % 题目已关联 KP |

# 知识结构的统一表示方案

### 0  术语速览

| 术语         | 记号               | 含义                                      |
| ------------ | ------------------ | ----------------------------------------- |
| **知识点**   | *kp*               | 领域中最小教学/技能单元                   |
| **知识状态** | *K* ⊆ 𝒬            | 学习者已掌握知识点的集合                  |
| **知识空间** | 𝒦                  | 满足“下闭”公设¹ 的所有可行 *K* 集合       |
| **先修 DAG** | **G** = (𝒬, E)     | 知识点偏序：若 A → B，则掌握 B 必先掌握 A |
| **状态图**   | **H** = (𝒦, E′)    | 𝒦 的 Hasse 图：一次增 1 kp 的邻接         |
| **位向量**   | **v** ∈ {0,1}\|𝒬\| | K 在程序中的压缩编码                      |

> ¹ **下闭**：若 K ∈ 𝒦 且 A ⊂ K，则 A ∈ 𝒦。
>  ² **Well-graded**：∀ K,L ∈ 𝒦 ，存在一步步增 1 个 kp 到达的链。

------

## 1  视图之间的关系

```
知识点集合 𝒬 ──▶ 先修 DAG 𝐆 (人工 + 自动)
                      │ 取“极小生成组”Σ
                      └─▶ 闭包算法 (Next-Closure)
                                  │
                           𝒦 = Knowledge Space
                                  │
                   ┌──────────────┴───────────────┐
                   ▼                              ▼
             状态图 𝐇 (K→K∪{q})               位向量编码
```

- 𝐆 + 闭包 ⇒ 𝒦 (理论真源)
- 𝐇、位向量只是 **𝒦 的投影**，用于不同性能场景。

------

## 2  存储策略（PoC → 规模化）

| 数据对象       | PoC (<100 kp)                | 中型 (<500 kp)                      | 大型 (>500 kp)                      |
| -------------- | ---------------------------- | ----------------------------------- | ----------------------------------- |
| **先修 DAG** 𝐆 | `prerequisite_relation` 全存 | 同左                                | 同左 + 压缩索引 (pgRouting / Neo4j) |
| **知识空间 𝒦** | 物化入表 `knowledge_state`   | 仅存 • 极小/极大状态 • 观察到的状态 | 不落库；内存服务 on-demand          |
| **状态图 𝐇**   | 物化 `state_transition`      | 动态生成                            | 仅在内存                            |
| **位向量**     | 存 BIT(V)                    | Redis BitSet 缓存                   | 仅内存传输                          |
## 3  生成流程

| 步骤                    | 方法                             | 产物                      |
| ----------------------- | -------------------------------- | ------------------------- |
| 1. **构建先修 DAG (𝐆)** | 人工拖拽 + 校验脚本              | `prerequisite_relation`   |
| 2. **闭包求 𝒦**         | Next-Closure / Bertet BFS        | `knowledge_state` (PoC)   |
| 3. **派生状态图 𝐇**     | 遍历 𝒦，若 K+q ∈ 𝒦 且 q 最小可加 | `state_transition` (可省) |
| 4. **位向量编码**       | K → bitstring                    | 𝒬                         |

## 4  与 IRT - BKT 协同

| 场景                 | 需要哪种视图           | 交互细节                                                     |
| -------------------- | ---------------------- | ------------------------------------------------------------ |
| **自适应诊断 (CAT)** | 𝒦 或 𝐇                 | *算法*：保持候选状态集 S；选能最大化信息熵的题；答对则 S = {K ∈ S |
| **IRT 难度单调校验** | 𝐆                      | 异步批：若存在 A→B 且 b(A) > b(B)+ε → 报警                   |
| **BKT 更新一致性**   | **位向量** + 𝐆         | 更新后检查：若掌握 B 而前置 A 未掌握 → 强制降 P(L_B)         |
| **下一步学习推荐**   | “外缘集合” 𝜕K = {q ∉ K | prereq(q) ⊆ K}                                               |
| **错题溯源补救**     | 𝐆                      | 找题对应 kp 集 P，回溯其未掌握前序 kp → 推送基础题           |

## 5  API 设计（伪代码）

```
GET /knowledge/outer-rim?state_vector=0b101001
→ [{ kp_id, est_gain, candidate_item_ids[] }]

POST /knowledge/filter-states
{ state_ids: [...], question_id: 123, correct: true }
→ [remaining_state_ids]

GET /knowledge/prereq-chain?kp_id=42
→ [ { pre_kp: 12, depth: 1 }, ... ]
```

> 引擎侧统一通过这些 API，而不关心底层是查表还是内存闭包。

## 6  性能基线

| 规模     | 𝒦 大小            | 外缘查询                    | CAT 一次更新 |
| -------- | ----------------- | --------------------------- | ------------ |
| 100 kp   | ≤ 7 000 状态      | <2 ms (SQL)                 | <50 ms       |
| 500 kp   | ~1 M 状态(不物化) | 5 K ops/s (Redis 筛 bitset) | <30 ms       |
| 1 000 kp | 在线              | Redis Lua / Rust 服务       | <50 ms       |

## 7  关键实现提示

- **位向量顺序固定**（按 kp_id 升序）；跨服务传输时用 Base64 压缩。
- **先修 DAG 改动触发版本号**，作为所有缓存键的前缀，秒级失效。
- **闭包服务** 可用 Rust + bit-vec，一次增量闭包 <100 µs。
- **可视化**：状态图对 >200 节点自动聚合按层展示；仅供教研浏览。

### 结论

- “知识空间 𝒦” 是**唯一真源**；先修 DAG、状态图、位向量只是 **不同层的投影/索引**。
- IRT 用 𝐆 保障难度方向，BKT 用位向量维护先修一致性，自适应策略靠外缘查询。
- 依据数据规模选择“全物化 / 部分物化 / 在线闭包”三档实现，既保概念纯洁，又兼顾性能可落地。

# 知识结构的表示方式及与IRT/BKT的协同

有了完整的知识空间结构（包括知识点先修关系和知识状态集合）后，需要将其转化为适合与学习者模型协同使用的表示形式。本模块将提供多种知识状态表示，以支持IRT、BKT等模型使用：

- 知识状态图（State Graph）：这是以知识状态为节点的图结构，其中边表示从一个知识状态到另一个的转移。通常我们考虑学习方向的转移，即通过习得新的知识点从一个状态进入包含更多知识的状态。若知识空间满足良序性质（well-graded，被称作学习空间），则可以保证任一状态到其超集状态之间总能通过一次增加一个知识点的路径到达。因此，状态图一般是知识空间在子集关系上的Hasse图（覆盖关系图）：一条有向边$K \to K\cup{q}$表示从知识状态$K$学会知识点$q$后达到新状态$K'$。状态图直观地刻画了学习路径：例如根据某先修关系图，可以罗列出从空集$\varnothing$到满集$Q$（全部掌握）的所有可能学习路线。在模块中，状态图可用于可视化展示整个知识空间的结构规模和层次分布，也可用于推荐下一步学习内容（如选择与当前状态相邻的下一状态所对应的知识点，即学生“准备好学习”的知识）。值得注意的是，对于大型知识空间，显式存储所有状态节点可能不可行，但我们可以对状态进行按需生成或采用隐式表示（例如通过先修关系推断哪些知识点是当前未掌握但其前提都已掌握的，即状态的“外缘”知识点）。
- 可达关系图（Reachability Graph）：这是对知识状态图的一种抽象，强调可达性而非逐步转移。它可以被实现为有向无环图，其中节点仍是知识状态，但边表示一个状态可以经过若干步学习达到另一状态。由於知识状态在集合上有包含关系，可达关系图实际就是状态集合的偏序(Hasse图的传递闭包)。这种表示可用于回答诸如“从知识状态A出发最终能掌握知识点X吗”、“状态B是否比状态A更高级”等查询。在结合BKT模型时，我们也可以将可达关系图理解为一种先验约束：某些知识组合（状态）对学生来说是不可能的，如果状态图中不存在从空状态到该组合的路径，那么该组合不应出现在预测中。这可用于约束BKT或深度知识追踪模型的输出，使其符合先修逻辑。例如，有研究在动态贝叶斯网的学生模型中加入一层节点来编码知识点间的先修关系，从而限制各技能的掌握状态组合必须符合先修约束。
- 布尔知识向量：将知识状态表示为固定长度$N$（知识点数）的布尔向量，其中每一位对应一个知识点，取1表示“已掌握”、0表示“未掌握”。这种向量形式直接对应于知识空间中的一个状态，也是认知诊断模型中常见的表示（例如Q矩阵和属性掌握模式）。布尔向量便于与IRT、BKT模型交互：对于IRT模型，我们可以将其看作多维IRT中的能力向量，或者在认知诊断模型（CDM，如DINA/DINO）中将向量作为潜在属性，IRT的题目参数可基于Q矩阵评估该向量对答题正确概率的影响。对于BKT模型，通常每个知识点有独立的掌握概率，但通过知识向量我们可以在模型外层增加一致性检查：例如在更新某知识点的掌握概率时，若其前提知识的BKT概率尚低，可降低该知识点的掌握概率上限，或延迟标记其为掌握。这相当于利用知识图谱提供的先验来调整BKT的后验更新。此外，如果使用深度知识追踪（DKT）等序列模型，也可以将知识点先修关系融合为网络结构或正则项，帮助模型更准确地预测学习者的状态。总的来说，知识空间提供的布尔知识状态向量能够约束和丰富传统IRT/BKT模型：前者获得了明确的多技能结构信息（而不仅是单一分数），后者则得到先修约束避免产生违背常识的预测组合。

为方便模型协同，模块会提供API输出多种视图的数据。例如，可以输出某学生当前掌握状态的布尔向量，以及根据知识空间推断的“可学习项”（即从该状态出发可达的下一个知识点清单）；也可以输出知识状态图或知识点依赖图，以供外部分析工具或可视化组件使用。在ALEKS系统中，知识空间结构被用于自适应选题：系统会根据学生已答对/错的题不断缩小其可能的知识状态集，并选择能最大区分剩余状态的题目发问。同样地，我们的模块也能向自适应学习引擎提供所需的信息，例如通过比较学生当前疑似状态集合的“外缘”知识点列表，推荐那些所有前提已满足但学生尚未掌握的知识点作为下一个教学目标。这种结合使IRT/BKT等概率模型的预测更具教学解释性，并使得知识推荐有理有据（不会推荐尚未满足前提的知识）。

# 数据库设计 (PostgreSQL适配)

| 模块              | 主要表                                       | 作用                                                         |
| ----------------- | -------------------------------------------- | ------------------------------------------------------------ |
| **用户与权限**    | `users`                                      | 账号、角色、审计                                             |
| **知识本体**      | `knowledge_points`, `prerequisite_relation`  | 定义知识点层级 & 先修边                                      |
| **题库**          | `questions`, `question_assets`, `item_param` | 题干、媒资、IRT 参数                                         |
| **题-知识点映射** | `item_kp_map`                                | Q-矩阵                                                       |
| **题-题关系**     | `question_relation`                          | complements / progresses_to / equivalent / **prerequisite** / revision |
| **知识空间**      | `knowledge_state`, `state_transition`        | 可行知识状态 & 邻接转移                                      |
| **标注工作流**    | `annotation_tasks`, `annotation_logs`        | 任务分派、全量审计                                           |
| **版本与维护**    | `question_versions`, `kp_versions`           | 历史快照、回滚                                               |
| **模型参数**      | `skill_param`                                | BKT 四参数 / 其它技能级别超参                                |

### 1. `users` — 系统账户

| 列名            | 类型        | 说明                                             |
| --------------- | ----------- | ------------------------------------------------ |
| `user_id`       | BIGSERIAL   | 主键                                             |
| `username`      | TEXT UNIQUE | 登录名                                           |
| `password_hash` | TEXT        | Argon2 / bcrypt                                  |
| `role`          | VARCHAR(16) | `admin / annotator / reviewer / viewer / engine` |
| `full_name`     | TEXT        |                                                  |
| `email`         | TEXT UNIQUE |                                                  |
| `created_at`    | TIMESTAMPTZ |                                                  |
| `last_login`    | TIMESTAMPTZ |                                                  |

**索引** `(role)`  **扩展** 账号锁定、密钥对等

### 2. `knowledge_points` — 知识点/技能定义

| 列名          | 类型          | 说明                             |
| ------------- | ------------- | -------------------------------- |
| `kp_id`       | SERIAL        | 主键                             |
| `parent_id`   | INT FK → self | 层级树                           |
| `name`        | TEXT          | 标题                             |
| `code`        | TEXT UNIQUE   | 课程外部编码                     |
| `path`        | LTREE         | 物化路径（`root.grammar.modal`） |
| `description` | TEXT          |                                  |
| `is_leaf`     | BOOLEAN       | TRUE → 可出题                    |
| `created_at`  | TIMESTAMPTZ   |                                  |

**索引** GIST (path) BTREE (code)

### 3. `prerequisite_relation` — 知识点先修边

| 列名         | 类型                      | 说明                          |
| ------------ | ------------------------- | ----------------------------- |
| `pre_kp_id`  | INT FK → knowledge_points | 前提                          |
| `post_kp_id` | INT FK → knowledge_points | 后继                          |
| `source`     | SMALLINT                  | 0=expert 1=algorithm 2=import |
| `confidence` | NUMERIC(3,2)              | 0–1                           |
| `created_by` | BIGINT FK → users         |                               |
| `created_at` | TIMESTAMPTZ               |                               |

复合 PK `(pre_kp_id, post_kp_id)` **约束** 禁止自环；触发器检测新增边不成环。

### 4. `questions` — 题目主表

| 列名                      | 类型              | 说明                                          |
| ------------------------- | ----------------- | --------------------------------------------- |
| `question_id`             | BIGSERIAL         | 主键                                          |
| `content`                 | JSONB             | `{stem, options[], passages[], media_urls[]}` |
| `q_type`                  | SMALLINT          | 0=SC,1=MC,2=TF,3=Cloze,4=Essay…               |
| `difficulty_lvl`          | SMALLINT          | 1–5（人工）                                   |
| `irt_ready`               | BOOLEAN           | 参数已校准？                                  |
| `answer_key`              | JSONB             | 正确答案 / 评分要点                           |
| `analysis`                | TEXT              | 解析                                          |
| `source`                  | TEXT              | “2024真题”                                    |
| `is_active`               | BOOLEAN           | 软删除                                        |
| `created_by`              | BIGINT FK → users |                                               |
| `created_at / updated_at` | TIMESTAMPTZ       |                                               |

常用索引：`(q_type)`, `(difficulty_lvl)`, `(is_active)`

### 5. `question_assets` — 媒资

| 列名          | 类型                  | 说明       |
| ------------- | --------------------- | ---------- |
| `asset_id`    | BIGSERIAL             | 主键       |
| `question_id` | BIGINT FK → questions |            |
| `uri`         | TEXT                  | S3/OSS URL |
| `media_type`  | VARCHAR(32)           | image/png… |
| `alt_text`    | TEXT                  |            |

### 6. `item_param` — IRT 项目参数

| 列名              | 类型                     | 说明   |
| ----------------- | ------------------------ | ------ |
| `question_id`     | BIGINT PK FK → questions |        |
| `a`               | REAL                     | 区分度 |
| `b`               | REAL                     | 难度   |
| `c`               | REAL                     | 猜测率 |
| `last_calibrated` | TIMESTAMPTZ              |        |

### 7. `item_kp_map` — 题-知识点映射（Q-矩阵）

| 列名            | 类型                      | 说明               |
| --------------- | ------------------------- | ------------------ |
| `item_id`       | BIGINT FK → questions     |                    |
| `kp_id`         | INT FK → knowledge_points |                    |
| `relation_type` | SMALLINT                  | 0=assess 1=require |
| `confidence`    | NUMERIC(3,2)              |                    |

复合 PK `(item_id, kp_id)`

### 8. `question_relation` — 题-题关系

| 列名         | 类型                  | 说明                                                         |
| ------------ | --------------------- | ------------------------------------------------------------ |
| `src_q_id`   | BIGINT FK → questions |                                                              |
| `dst_q_id`   | BIGINT FK → questions |                                                              |
| `rel_type`   | ENUM `q_rel_type`     | `complements / progresses_to / equivalent / prerequisite / revision` |
| `confidence` | NUMERIC(3,2)          |                                                              |
| `created_by` | BIGINT FK → users     |                                                              |
| `created_at` | TIMESTAMPTZ           |                                                              |

PK `(src_q_id, dst_q_id, rel_type)` 自环禁止；`prerequisite` 边须满足 `b(src) ≤ b(dst)` 软校验。

### 9. `knowledge_state` — 显式知识状态（可选物化）

| 列名                      | 类型        | 说明                          |
| ------------------------- | ----------- | ----------------------------- |
| `state_id`                | BIGSERIAL   | 主键                          |
| `state_vector`            | BIT VARYING | 长度 = 知识点数               |
| `is_minimal / is_maximal` | BOOLEAN     | 供 UI 展示                    |
| `origin`                  | SMALLINT    | 0=alg闭包 1=observed 2=expert |
| `created_at`              | TIMESTAMPTZ |                               |

索引 `USING BTREE (state_vector)`

> 若知识点 > 500，建议仅存“访问过”的状态，完整闭包留在计算层。

### 10. `state_transition` — 邻接转移（学习边）

| 列名          | 类型                        | 说明                   |
| ------------- | --------------------------- | ---------------------- |
| `from_state`  | BIGINT FK → knowledge_state |                        |
| `to_state`    | BIGINT FK → knowledge_state |                        |
| `learned_kp`  | INT FK → knowledge_points   |                        |
| `edge_weight` | REAL                        | 可选：转移概率（经验） |

用于 “KST → 可达图” 导航；若只做在线闭包计算，可省略此表。
### 11. `skill_param` — BKT / 其它技能参数

| 列名          | 类型                         | 说明     |
| ------------- | ---------------------------- | -------- |
| `kp_id`       | INT PK FK → knowledge_points |          |
| `p_l0`        | REAL                         | 初始掌握 |
| `p_t`         | REAL                         | 学习率 T |
| `p_g`         | REAL                         | 猜对 G   |
| `p_s`         | REAL                         | 滑落 S   |
| `last_update` | TIMESTAMPTZ                  |          |
### 12. `annotation_tasks` — 标注任务

| 列名                      | 类型              | 说明                                    |
| ------------------------- | ----------------- | --------------------------------------- |
| `task_id`                 | BIGSERIAL         | 主键                                    |
| `task_type`               | SMALLINT          | 0=Q-KP,1=Q-Rel,2=Prereq                 |
| `payload`                 | JSONB             | 问题集合 / 过滤条件                     |
| `assignees`               | BIGINT[]          |                                         |
| `reviewer_id`             | BIGINT            |                                         |
| `state`                   | ENUM `task_state` | `pending / in_progress / review / done` |
| `created_by`              | BIGINT            |                                         |
| `created_at / updated_at` | TIMESTAMPTZ       |                                         |

索引 `(state)`
### 13. `annotation_logs` — 操作留痕

| 列名          | 类型                         | 说明                        |
| ------------- | ---------------------------- | --------------------------- |
| `log_id`      | BIGSERIAL                    | 主键                        |
| `task_id`     | BIGINT FK → annotation_tasks |                             |
| `question_id` | BIGINT                       |                             |
| `operation`   | VARCHAR(32)                  | add_kp / del_kp / add_rel … |
| `detail`      | JSONB                        | 变更前后                    |
| `operator_id` | BIGINT FK → users            |                             |
| `ts`          | TIMESTAMPTZ                  |                             |
### 14. `question_versions` — 题目快照

| 列名           | 类型                  | 说明                        |
| -------------- | --------------------- | --------------------------- |
| `version_id`   | BIGSERIAL             | 主键                        |
| `question_id`  | BIGINT FK → questions |                             |
| `revision_num` | INT                   |                             |
| `snapshot`     | JSONB                 | questions + mapping + param |
| `changes_note` | TEXT                  |                             |
| `created_by`   | BIGINT                |                             |
| `created_at`   | TIMESTAMPTZ           |                             |
### 15. `kp_versions` — 知识点/先修快照

| 列名         | 类型                      | 说明                     |
| ------------ | ------------------------- | ------------------------ |
| `version_id` | BIGSERIAL                 | 主键                     |
| `kp_id`      | INT FK → knowledge_points |                          |
| `snapshot`   | JSONB                     | 包含路径、描述、先修集合 |
| `created_by` | BIGINT                    |                          |
| `created_at` | TIMESTAMPTZ               |                          |
## 枚举与扩展类型

| 名称         | 取值                                                         | 用途         |
| ------------ | ------------------------------------------------------------ | ------------ |
| `q_type`     | 0 SC, 1 MC, 2 TF, 3 Cloze, 4 Essay …                         | 题型         |
| `q_rel_type` | `complements`, `progresses_to`, `equivalent`, **`prerequisite`**, `revision` | 题-题关系    |
| `task_state` | `pending`, `in_progress`, `review`, `done`                   | 标注任务流程 |
## 关键索引 & 性能建议

| 查询场景              | 建议索引 / 技术                                              |
| --------------------- | ------------------------------------------------------------ |
| **按知识点查题**      | `item_kp_map (kp_id) INCLUDE(item_id, confidence)`           |
| **错题回溯前序题**    | `question_relation (dst_q_id) WHERE rel_type='prerequisite'` |
| **题干全文搜索**      | `GIN(to_tsvector('simple', content->>'stem'))`               |
| **知识点树遍历**      | `GIST (path)` + Postgres `LTREE`                             |
| **闭包计算**          | 物化视图 `v_kp_transitive(pre_kp, post_kp)` 存传递闭包，夜间刷新 |
| **IITA/FCA 批量分析** | 使用 `COPY` 直导 csv，算法层做计算后批量回写结果表           |
## 数据一致性与版本策略

1. **无环保证**：`prerequisite_relation` 插入触发器 + `LOCK TABLE` 确保先修图无环。
2. **软删除**：所有核心实体含 `is_active`；历史数据通过版本表保留。
3. **事件溯源**：`annotation_logs` 完整记录每次变更，可按 task_id 或 question_id 回放。
4. **快照回滚**：`question_versions` / `kp_versions` 存 Json 快照，一键恢复到指定 revision_num。

## 典型跨表关系图

```
users ───┐
         │(created_by / reviewer_id / operator_id / assignees[*])
         ▼
questions ──< item_kp_map >── knowledge_points
     │            ▲
     │            │(pre_kp_id / post_kp_id)
     │     prerequisite_relation
     │            │
 question_relation │  (prerequisite / progresses_to …)
         │         ▼
       annotation_tasks ––< annotation_logs
```
### 设计要点回顾

- **题-题必备边** (`q_rel_type='prerequisite'`) 用于
  - 诊断测验题量压缩
  - 错题补救链路
  - IRT 难度单调校验
  - KST 蕴涵闭包
- **知识空间实体** `knowledge_state / state_transition` 可按需物化；大规模可转移至计算层/Redis Bloom。
- **多模型协同**
  - IRT 参数 → `item_param`
  - BKT 技能 → `skill_param`
  - KST 先修边 / 状态集 → `prerequisite_relation`, `knowledge_state`
     引擎层写入学生表现时只读上述表，不直接修改结构。
- **人机协同标注**：AI 产出的映射与先修边以 `source=1` 写入，标注员复审后调高 confidence。
# 贡献指南

感谢您对自适应学习数据标注模块项目的关注！我们欢迎任何形式的贡献。

## 如何贡献

### 报告问题

如果您发现了bug或有功能建议，请：

1. 检查 [Issues](../../issues) 确认问题未被报告
2. 创建新的 Issue，包含：
   - 清晰的标题和描述
   - 重现步骤（如果是bug）
   - 期望的行为
   - 实际的行为
   - 环境信息（操作系统、浏览器、版本等）

### 提交代码

1. **Fork** 项目到您的GitHub账户
2. **Clone** 您的fork到本地
3. **创建** 新的功能分支
4. **开发** 并测试您的更改
5. **提交** Pull Request

#### 分支命名规范

- `feature/功能名称` - 新功能
- `bugfix/问题描述` - 错误修复
- `hotfix/紧急修复` - 紧急修复
- `docs/文档更新` - 文档更新

#### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<类型>[可选范围]: <描述>

[可选正文]

[可选脚注]
```

类型包括：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(auth): 添加JWT认证功能

- 实现用户登录和注册
- 添加JWT令牌验证中间件
- 更新API文档

Closes #123
```

## 开发环境设置

### 前提条件

- Node.js 18+
- Python 3.11+
- PostgreSQL 15+
- Docker & Docker Compose

### 快速开始

1. 克隆项目
```bash
git clone <your-fork-url>
cd annotation
```

2. 运行设置脚本
```bash
./scripts/setup.sh
```

3. 启动开发环境
```bash
docker-compose up -d
```

## 代码规范

### 前端 (React/TypeScript)

- 使用 **TypeScript** 严格模式
- 遵循 **ESLint** 和 **Prettier** 配置
- 组件使用 **函数式组件** + Hooks
- 样式使用 **Tailwind CSS**
- 文件命名使用 **kebab-case**

示例：
```typescript
// components/user-profile.tsx
import React from 'react';

interface UserProfileProps {
  userId: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  // 组件实现
};
```

### 后端 (Python/FastAPI)

- 遵循 **PEP 8** 代码风格
- 使用 **Black** 进行代码格式化
- 使用 **isort** 进行导入排序
- 函数和变量使用 **snake_case**
- 类名使用 **PascalCase**

示例：
```python
# app/models/user.py
from sqlalchemy import Column, Integer, String
from app.core.database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
```

## 测试

### 前端测试

```bash
cd frontend
npm run test          # 运行单元测试
npm run test:e2e       # 运行E2E测试
npm run test:coverage  # 生成覆盖率报告
```

### 后端测试

```bash
cd backend
pytest                 # 运行所有测试
pytest --cov=app       # 生成覆盖率报告
pytest tests/test_auth.py  # 运行特定测试
```

### 测试要求

- 新功能必须包含相应的测试
- 测试覆盖率应保持在 80% 以上
- 所有测试必须通过才能合并

## Pull Request 流程

1. **确保您的分支是最新的**
```bash
git checkout main
git pull upstream main
git checkout your-feature-branch
git rebase main
```

2. **运行测试和代码检查**
```bash
# 前端
cd frontend
npm run lint
npm run test

# 后端
cd backend
black .
isort .
pytest
```

3. **提交 Pull Request**
   - 提供清晰的标题和描述
   - 引用相关的 Issue
   - 包含测试截图（如果适用）
   - 确保CI检查通过

4. **代码审查**
   - 响应审查者的反馈
   - 及时更新代码
   - 保持讨论的专业性

## 文档贡献

### 文档类型

- **API文档** - 自动生成，需要更新代码注释
- **用户文档** - 功能使用说明
- **开发文档** - 技术实现细节
- **部署文档** - 环境配置和部署指南

### 文档规范

- 使用 **Markdown** 格式
- 包含清晰的标题层级
- 提供代码示例和截图
- 保持语言简洁明了

## 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境，请：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 沟通渠道

- **GitHub Issues** - 问题报告和功能请求
- **GitHub Discussions** - 一般讨论和问答
- **Pull Requests** - 代码审查和讨论

## 发布流程

### 版本号规范

使用 [语义化版本](https://semver.org/lang/zh-CN/)：

- `MAJOR.MINOR.PATCH`
- `MAJOR`: 不兼容的API修改
- `MINOR`: 向下兼容的功能性新增
- `PATCH`: 向下兼容的问题修正

### 发布步骤

1. 更新版本号
2. 更新 CHANGELOG.md
3. 创建 Release Tag
4. 自动部署到生产环境

## 获得帮助

如果您需要帮助，可以：

1. 查看 [文档](docs/)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 [Discussion](../../discussions)
4. 联系维护者

感谢您的贡献！🎉

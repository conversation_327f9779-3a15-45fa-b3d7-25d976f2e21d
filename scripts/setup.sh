#!/bin/bash

# 自适应学习数据标注模块 - 开发环境设置脚本

set -e

echo "🚀 开始设置自适应学习数据标注模块开发环境..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 未安装，请先安装 Python 3.11+"
        exit 1
    fi
    
    echo "✅ 系统要求检查通过"
}

# 创建环境变量文件
setup_env() {
    echo "🔧 设置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "✅ 已创建 .env 文件，请根据需要修改配置"
    else
        echo "⚠️  .env 文件已存在，跳过创建"
    fi
}

# 设置前端环境
setup_frontend() {
    echo "🎨 设置前端环境..."
    
    cd frontend
    
    if [ ! -f package.json ]; then
        echo "📦 初始化前端项目..."
        npm create vite@latest . -- --template react-ts
    fi
    
    echo "📦 安装前端依赖..."
    npm install
    
    cd ..
    echo "✅ 前端环境设置完成"
}

# 设置后端环境
setup_backend() {
    echo "🐍 设置后端环境..."
    
    cd backend
    
    # 创建虚拟环境
    if [ ! -d venv ]; then
        echo "🔧 创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    if [ -f requirements.txt ]; then
        echo "📦 安装后端依赖..."
        pip install -r requirements.txt
    else
        echo "⚠️  requirements.txt 不存在，请先创建"
    fi
    
    cd ..
    echo "✅ 后端环境设置完成"
}

# 设置数据库
setup_database() {
    echo "🗄️  设置数据库..."
    
    # 启动数据库服务
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 10
    
    # 运行数据库迁移
    cd backend
    if [ -f alembic.ini ]; then
        source venv/bin/activate
        alembic upgrade head
        echo "✅ 数据库迁移完成"
    else
        echo "⚠️  Alembic 配置不存在，请先配置数据库迁移"
    fi
    cd ..
}

# 创建必要的目录
create_directories() {
    echo "📁 创建必要的目录..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p tests/reports
    
    echo "✅ 目录创建完成"
}

# 主函数
main() {
    echo "🎯 自适应学习数据标注模块开发环境设置"
    echo "========================================"
    
    check_requirements
    setup_env
    create_directories
    setup_frontend
    setup_backend
    setup_database
    
    echo ""
    echo "🎉 开发环境设置完成！"
    echo ""
    echo "📝 下一步操作："
    echo "1. 修改 .env 文件中的配置"
    echo "2. 运行 'docker-compose up' 启动所有服务"
    echo "3. 访问 http://localhost:3000 查看前端"
    echo "4. 访问 http://localhost:8000/docs 查看API文档"
    echo ""
    echo "🔗 有用的命令："
    echo "- 启动服务: docker-compose up -d"
    echo "- 查看日志: docker-compose logs -f"
    echo "- 停止服务: docker-compose down"
    echo "- 重建服务: docker-compose up --build"
}

# 运行主函数
main "$@"

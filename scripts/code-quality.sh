#!/bin/bash

# 代码质量检查脚本
# 运行各种代码质量检查工具

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python_env() {
    if [ ! -d "$PROJECT_ROOT/backend/venv" ]; then
        log_warning "Python虚拟环境不存在，正在创建..."
        cd "$PROJECT_ROOT/backend"
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        log_success "Python虚拟环境创建完成"
    fi
}

# 检查Node.js环境
check_node_env() {
    if [ ! -d "$PROJECT_ROOT/frontend/node_modules" ]; then
        log_warning "Node.js依赖未安装，正在安装..."
        cd "$PROJECT_ROOT/frontend"
        npm install
        log_success "Node.js依赖安装完成"
    fi
}

# Python代码质量检查
check_python_quality() {
    log_info "开始Python代码质量检查..."
    
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate
    
    # Black格式化检查
    log_info "运行Black格式化检查..."
    if black --check --diff .; then
        log_success "Black格式化检查通过"
    else
        log_error "Black格式化检查失败"
        return 1
    fi
    
    # isort导入排序检查
    log_info "运行isort导入排序检查..."
    if isort --check-only --diff .; then
        log_success "isort导入排序检查通过"
    else
        log_error "isort导入排序检查失败"
        return 1
    fi
    
    # flake8代码检查
    log_info "运行flake8代码检查..."
    if flake8 .; then
        log_success "flake8代码检查通过"
    else
        log_error "flake8代码检查失败"
        return 1
    fi
    
    # mypy类型检查
    log_info "运行mypy类型检查..."
    if mypy app; then
        log_success "mypy类型检查通过"
    else
        log_warning "mypy类型检查有警告"
    fi
    
    # bandit安全检查
    log_info "运行bandit安全检查..."
    if bandit -r app -f json -o bandit-report.json; then
        log_success "bandit安全检查通过"
    else
        log_warning "bandit安全检查有警告"
    fi
    
    log_success "Python代码质量检查完成"
}

# 前端代码质量检查
check_frontend_quality() {
    log_info "开始前端代码质量检查..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # ESLint检查
    log_info "运行ESLint检查..."
    if npm run lint; then
        log_success "ESLint检查通过"
    else
        log_error "ESLint检查失败"
        return 1
    fi
    
    # TypeScript类型检查
    log_info "运行TypeScript类型检查..."
    if npm run type-check; then
        log_success "TypeScript类型检查通过"
    else
        log_error "TypeScript类型检查失败"
        return 1
    fi
    
    # Prettier格式化检查
    log_info "运行Prettier格式化检查..."
    if npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"; then
        log_success "Prettier格式化检查通过"
    else
        log_error "Prettier格式化检查失败"
        return 1
    fi
    
    log_success "前端代码质量检查完成"
}

# 运行测试
run_tests() {
    log_info "开始运行测试..."
    
    # Python测试
    log_info "运行Python测试..."
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate
    if pytest --cov=app --cov-report=term-missing --cov-report=html; then
        log_success "Python测试通过"
    else
        log_error "Python测试失败"
        return 1
    fi
    
    # 前端测试
    log_info "运行前端测试..."
    cd "$PROJECT_ROOT/frontend"
    if npm run test; then
        log_success "前端测试通过"
    else
        log_error "前端测试失败"
        return 1
    fi
    
    log_success "所有测试完成"
}

# 自动修复代码格式
fix_code_format() {
    log_info "开始自动修复代码格式..."
    
    # Python代码格式化
    log_info "修复Python代码格式..."
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate
    black .
    isort .
    log_success "Python代码格式修复完成"
    
    # 前端代码格式化
    log_info "修复前端代码格式..."
    cd "$PROJECT_ROOT/frontend"
    npm run format
    npx eslint --fix "src/**/*.{ts,tsx,js,jsx}"
    log_success "前端代码格式修复完成"
    
    log_success "代码格式修复完成"
}

# 生成代码质量报告
generate_report() {
    log_info "生成代码质量报告..."
    
    REPORT_DIR="$PROJECT_ROOT/reports"
    mkdir -p "$REPORT_DIR"
    
    # Python覆盖率报告
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate
    pytest --cov=app --cov-report=html --cov-report=xml
    mv htmlcov "$REPORT_DIR/python-coverage"
    mv coverage.xml "$REPORT_DIR/"
    
    # 前端测试报告
    cd "$PROJECT_ROOT/frontend"
    npm run test:coverage
    mv coverage "$REPORT_DIR/frontend-coverage"
    
    log_success "代码质量报告生成完成，位置: $REPORT_DIR"
}

# 显示帮助信息
show_help() {
    echo "代码质量检查脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  check           运行所有代码质量检查"
    echo "  check-python    仅检查Python代码"
    echo "  check-frontend  仅检查前端代码"
    echo "  test            运行所有测试"
    echo "  fix             自动修复代码格式"
    echo "  report          生成代码质量报告"
    echo "  setup           设置开发环境"
    echo "  help            显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 check                    # 运行所有检查"
    echo "  $0 fix                      # 自动修复格式"
    echo "  $0 test                     # 运行测试"
}

# 设置开发环境
setup_env() {
    log_info "设置开发环境..."
    
    check_python_env
    check_node_env
    
    # 安装pre-commit hooks
    if command -v pre-commit &> /dev/null; then
        log_info "安装pre-commit hooks..."
        pre-commit install
        pre-commit install --hook-type commit-msg
        log_success "pre-commit hooks安装完成"
    else
        log_warning "pre-commit未安装，请运行: pip install pre-commit"
    fi
    
    log_success "开发环境设置完成"
}

# 主函数
main() {
    case "${1:-help}" in
        check)
            check_python_env
            check_node_env
            check_python_quality
            check_frontend_quality
            log_success "所有代码质量检查完成"
            ;;
        check-python)
            check_python_env
            check_python_quality
            ;;
        check-frontend)
            check_node_env
            check_frontend_quality
            ;;
        test)
            check_python_env
            check_node_env
            run_tests
            ;;
        fix)
            check_python_env
            check_node_env
            fix_code_format
            ;;
        report)
            check_python_env
            check_node_env
            generate_report
            ;;
        setup)
            setup_env
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"

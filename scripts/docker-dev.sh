#!/bin/bash

# Docker开发环境管理脚本
# 用于启动、停止和管理自适应学习数据标注系统的开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/docker"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未启动，请启动 Docker 服务"
        exit 1
    fi
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        log_warning ".env 文件不存在，从模板创建..."
        if [ -f "$PROJECT_ROOT/.env.example" ]; then
            cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
            log_success "已创建 .env 文件，请根据需要修改配置"
        else
            log_error ".env.example 文件不存在，无法创建 .env 文件"
            exit 1
        fi
    fi
}

# 启动开发环境
start_dev() {
    log_info "启动开发环境..."
    
    check_docker
    check_env_file
    
    cd "$DOCKER_DIR"
    
    # 构建并启动服务
    docker-compose -f docker-compose.dev.yml up -d --build
    
    log_success "开发环境启动成功！"
    log_info "服务地址："
    echo "  - 前端应用: http://localhost:3000"
    echo "  - 后端API: http://localhost:8000"
    echo "  - API文档: http://localhost:8000/docs"
    echo "  - 数据库: localhost:5432"
    echo "  - Redis: localhost:6379"
    
    log_info "使用 'docker-compose -f docker-compose.dev.yml logs -f' 查看日志"
}

# 启动开发环境（包含管理工具）
start_dev_with_tools() {
    log_info "启动开发环境（包含管理工具）..."
    
    check_docker
    check_env_file
    
    cd "$DOCKER_DIR"
    
    # 启动所有服务包括工具
    docker-compose -f docker-compose.dev.yml --profile tools up -d --build
    
    log_success "开发环境（含工具）启动成功！"
    log_info "服务地址："
    echo "  - 前端应用: http://localhost:3000"
    echo "  - 后端API: http://localhost:8000"
    echo "  - API文档: http://localhost:8000/docs"
    echo "  - 数据库管理: http://localhost:5050"
    echo "  - Redis管理: http://localhost:8081"
}

# 停止开发环境
stop_dev() {
    log_info "停止开发环境..."
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.dev.yml down
    
    log_success "开发环境已停止"
}

# 重启开发环境
restart_dev() {
    log_info "重启开发环境..."
    stop_dev
    start_dev
}

# 查看服务状态
status() {
    log_info "查看服务状态..."
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.dev.yml ps
}

# 查看日志
logs() {
    local service=$1
    
    cd "$DOCKER_DIR"
    
    if [ -z "$service" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose -f docker-compose.dev.yml logs -f "$service"
    fi
}

# 进入容器
exec_container() {
    local service=$1
    local shell=${2:-bash}
    
    if [ -z "$service" ]; then
        log_error "请指定服务名称"
        exit 1
    fi
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.dev.yml exec "$service" "$shell"
}

# 清理环境
clean() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "清理开发环境..."
        
        cd "$DOCKER_DIR"
        
        # 停止并删除容器
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans
        
        # 删除镜像
        docker images | grep annotation | awk '{print $3}' | xargs -r docker rmi -f
        
        # 清理未使用的资源
        docker system prune -f
        
        log_success "环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 数据库操作
db_migrate() {
    log_info "执行数据库迁移..."
    
    cd "$DOCKER_DIR"
    docker-compose -f docker-compose.dev.yml exec backend alembic upgrade head
    
    log_success "数据库迁移完成"
}

db_reset() {
    log_warning "这将重置数据库，所有数据将丢失，确定要继续吗？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "重置数据库..."
        
        cd "$DOCKER_DIR"
        
        # 停止后端服务
        docker-compose -f docker-compose.dev.yml stop backend
        
        # 删除数据库数据卷
        docker-compose -f docker-compose.dev.yml down postgres
        docker volume rm docker_postgres_dev_data 2>/dev/null || true
        
        # 重新启动数据库
        docker-compose -f docker-compose.dev.yml up -d postgres
        
        # 等待数据库启动
        sleep 10
        
        # 重新启动后端并执行迁移
        docker-compose -f docker-compose.dev.yml up -d backend
        sleep 5
        db_migrate
        
        log_success "数据库重置完成"
    else
        log_info "取消重置操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Docker开发环境管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start           启动开发环境"
    echo "  start-tools     启动开发环境（包含管理工具）"
    echo "  stop            停止开发环境"
    echo "  restart         重启开发环境"
    echo "  status          查看服务状态"
    echo "  logs [service]  查看日志（可选指定服务）"
    echo "  exec <service>  进入容器"
    echo "  clean           清理环境"
    echo "  db:migrate      执行数据库迁移"
    echo "  db:reset        重置数据库"
    echo "  help            显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动开发环境"
    echo "  $0 logs backend             # 查看后端日志"
    echo "  $0 exec backend bash        # 进入后端容器"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_dev
            ;;
        start-tools)
            start_dev_with_tools
            ;;
        stop)
            stop_dev
            ;;
        restart)
            restart_dev
            ;;
        status)
            status
            ;;
        logs)
            logs "$2"
            ;;
        exec)
            exec_container "$2" "$3"
            ;;
        clean)
            clean
            ;;
        db:migrate)
            db_migrate
            ;;
        db:reset)
            db_reset
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"

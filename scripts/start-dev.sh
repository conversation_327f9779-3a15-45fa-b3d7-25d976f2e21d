#!/bin/bash

# 开发环境启动脚本

set -e

echo "🚀 启动自适应学习数据标注系统开发环境"

# 检查必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装"
        exit 1
    fi
}

echo "📋 检查依赖..."
check_command "python3"
check_command "node"
check_command "npm"
check_command "docker"

# 检查Python版本
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
if [[ $(echo "$PYTHON_VERSION < 3.8" | bc -l) -eq 1 ]]; then
    echo "❌ Python版本需要3.8或更高，当前版本: $PYTHON_VERSION"
    exit 1
fi

# 检查Node版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [[ $NODE_VERSION -lt 18 ]]; then
    echo "❌ Node.js版本需要18或更高，当前版本: $NODE_VERSION"
    exit 1
fi

echo "✅ 依赖检查通过"

# 启动数据库（如果使用Docker）
if [ "$USE_DOCKER_DB" = "true" ]; then
    echo "🐳 启动数据库容器..."
    docker-compose -f docker/docker-compose.dev.yml up -d postgres redis
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 10
fi

# 设置后端环境
echo "🔧 设置后端环境..."
cd backend

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo "📦 安装后端依赖..."
pip install -r requirements.txt

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
alembic upgrade head

# 启动后端服务
echo "🚀 启动后端服务..."
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

cd ..

# 设置前端环境
echo "🔧 设置前端环境..."
cd frontend

# 安装依赖
echo "📦 安装前端依赖..."
npm install

# 启动前端服务
echo "🚀 启动前端服务..."
npm run dev &
FRONTEND_PID=$!

cd ..

# 启动Dapr sidecar（如果需要）
if [ "$USE_DAPR" = "true" ]; then
    echo "🔗 启动Dapr sidecar..."
    dapr run --app-id annotation-backend --app-port 8000 --dapr-http-port 3500 --components-path ./dapr/components --config ./dapr/config.yaml &
    DAPR_PID=$!
fi

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📍 服务地址："
echo "   前端: http://localhost:3000"
echo "   后端: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo ""
echo "🛑 停止服务请按 Ctrl+C"

# 等待用户中断
trap 'echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; [ ! -z "$DAPR_PID" ] && kill $DAPR_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait

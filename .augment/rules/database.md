---
alwaysApply: true
---
# 数据库访问规则
数据库为Supabase项目，完全兼容postgresql

## 数据库连接配置

### 连接信息
- **连接字符串**: `postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres`
- **数据库类型**: PostgreSQL (Supabase)
- **编码**: UTF-8
- **时区**: UTC

### 环境变量设置
```bash
DATABASE_URL=postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres
DB_HOST=aws-0-ap-southeast-1.pooler.supabase.com
DB_PORT=6543
DB_NAME=postgres
DB_USER=postgres.qlpqyctsezzfzdgvfvir
DB_PASSWORD=UZyzHE1hy9kOddMv
```

## 数据库架构规范

### 核心表结构
1. **用户与权限模块**
   - `users` - 系统账户
   - 角色类型: `admin`, `annotator`, `reviewer`, `viewer`, `engine`

2. **知识本体模块**
   - `knowledge_points` - 知识点/技能定义
   - `prerequisite_relation` - 知识点先修关系

3. **题库模块**
   - `questions` - 题目主表
   - `question_assets` - 媒资文件
   - `item_param` - IRT 参数

4. **映射关系模块**
   - `item_kp_map` - 题-知识点映射(Q-矩阵)
   - `question_relation` - 题-题关系

5. **知识空间模块**
   - `knowledge_state` - 知识状态
   - `state_transition` - 状态转移

6. **标注工作流模块**
   - `annotation_tasks` - 标注任务
   - `annotation_logs` - 操作审计

7. **版本管理模块**
   - `question_versions` - 题目快照
   - `kp_versions` - 知识点快照

8. **模型参数模块**
   - `skill_param` - BKT/技能参数


#### 1. `users` — 系统账户

| 列名            | 类型        | 说明                                             |
| --------------- | ----------- | ------------------------------------------------ |
| `id`       | BIGSERIAL   | 主键                                             |
| `username`      | TEXT UNIQUE | 登录名                                           |
| `password_hash` | TEXT        | Argon2 / bcrypt                                  |
| `role`          | VARCHAR(16) | `admin / annotator / reviewer / viewer / engine` |
| `full_name`     | TEXT        |                                                  |
| `email`         | TEXT UNIQUE |                                                  |
| `created_at`    | TIMESTAMPTZ |                                                  |
| `last_login`    | TIMESTAMPTZ |                                                  |

**索引** `(role)`  **扩展** 账号锁定、密钥对等

#### 2. `knowledge_points` — 知识点/技能定义

| 列名          | 类型          | 说明                             |
| ------------- | ------------- | -------------------------------- |
| `id`       | SERIAL        | 主键                             |
| `parent_id`   | INT FK → self | 层级树                           |
| `name`        | TEXT          | 标题                             |
| `code`        | TEXT UNIQUE   | 课程外部编码                     |
| `path`        | LTREE         | 物化路径（`root.grammar.modal`） |
| `description` | TEXT          |                                  |
| `is_leaf`     | BOOLEAN       | TRUE → 可出题                    |
| `created_at`  | TIMESTAMPTZ   |                                  |

**索引** GIST (path) BTREE (code)

#### 3. `prerequisite_relation` — 知识点先修边

| 列名         | 类型                      | 说明                          |
| ------------ | ------------------------- | ----------------------------- |
| `pre_kp_id`  | INT FK → knowledge_points | 前提                          |
| `post_kp_id` | INT FK → knowledge_points | 后继                          |
| `source`     | SMALLINT                  | 0=expert 1=algorithm 2=import |
| `confidence` | NUMERIC(3,2)              | 0–1                           |
| `created_by` | BIGINT FK → users         |                               |
| `created_at` | TIMESTAMPTZ               |                               |

复合 PK `(pre_kp_id, post_kp_id)` **约束** 禁止自环；触发器检测新增边不成环。

#### 4. `questions` — 题目主表

| 列名                      | 类型              | 说明                                          |
| ------------------------- | ----------------- | --------------------------------------------- |
| `id`             | BIGSERIAL         | 主键                                          |
| `content`                 | JSONB             | `{stem, options[], passages[], media_urls[]}` |
| `q_type`                  | SMALLINT          | 0=SC,1=MC,2=TF,3=Cloze,4=Essay…               |
| `difficulty_lvl`          | SMALLINT          | 1–5（人工）                                   |
| `irt_ready`               | BOOLEAN           | 参数已校准？                                  |
| `answer_key`              | JSONB             | 正确答案 / 评分要点                           |
| `analysis`                | TEXT              | 解析                                          |
| `source`                  | TEXT              | “2024真题”                                    |
| `is_active`               | BOOLEAN           | 软删除                                        |
| `created_by`              | BIGINT FK → users |                                               |
| `created_at / updated_at` | TIMESTAMPTZ       |                                               |

常用索引：`(q_type)`, `(difficulty_lvl)`, `(is_active)`

#### 5. `question_assets` — 媒资

| 列名          | 类型                  | 说明       |
| ------------- | --------------------- | ---------- |
| `id`    | BIGSERIAL             | 主键       |
| `question_id` | BIGINT FK → questions |            |
| `uri`         | TEXT                  | S3/OSS URL |
| `media_type`  | VARCHAR(32)           | image/png… |
| `alt_text`    | TEXT                  |            |

#### 6. `item_param` — IRT 项目参数

| 列名              | 类型                     | 说明   |
| ----------------- | ------------------------ | ------ |
| `id`     | BIGINT PK FK → questions |        |
| `a`               | REAL                     | 区分度 |
| `b`               | REAL                     | 难度   |
| `c`               | REAL                     | 猜测率 |
| `last_calibrated` | TIMESTAMPTZ              |        |

#### 7. `item_kp_map` — 题-知识点映射（Q-矩阵）

| 列名            | 类型                      | 说明               |
| --------------- | ------------------------- | ------------------ |
| `id`       | BIGINT FK → questions     |                    |
| `kp_id`         | INT FK → knowledge_points |                    |
| `relation_type` | SMALLINT                  | 0=assess 1=require |
| `confidence`    | NUMERIC(3,2)              |                    |

复合 PK `(item_id, kp_id)`

#### 8. `question_relation` — 题-题关系

| 列名         | 类型                  | 说明                                                         |
| ------------ | --------------------- | ------------------------------------------------------------ |
| `src_q_id`   | BIGINT FK → questions |                                                              |
| `dst_q_id`   | BIGINT FK → questions |                                                              |
| `rel_type`   | ENUM `q_rel_type`     | `complements / progresses_to / equivalent / prerequisite / revision` |
| `confidence` | NUMERIC(3,2)          |                                                              |
| `created_by` | BIGINT FK → users     |                                                              |
| `created_at` | TIMESTAMPTZ           |                                                              |

PK `(src_q_id, dst_q_id, rel_type)` 自环禁止；`prerequisite` 边须满足 `b(src) ≤ b(dst)` 软校验。

#### 9. `knowledge_state` — 显式知识状态（可选物化）

| 列名                      | 类型        | 说明                          |
| ------------------------- | ----------- | ----------------------------- |
| `id`                | BIGSERIAL   | 主键                          |
| `state_vector`            | BIT VARYING | 长度 = 知识点数               |
| `is_minimal / is_maximal` | BOOLEAN     | 供 UI 展示                    |
| `origin`                  | SMALLINT    | 0=alg闭包 1=observed 2=expert |
| `created_at`              | TIMESTAMPTZ |                               |

索引 `USING BTREE (state_vector)`

> 若知识点 > 500，建议仅存“访问过”的状态，完整闭包留在计算层。

#### 10. `state_transition` — 邻接转移（学习边）

| 列名          | 类型                        | 说明                   |
| ------------- | --------------------------- | ---------------------- |
| `from_state`  | BIGINT FK → knowledge_state |                        |
| `to_state`    | BIGINT FK → knowledge_state |                        |
| `learned_kp`  | INT FK → knowledge_points   |                        |
| `edge_weight` | REAL                        | 可选：转移概率（经验） |

用于 “KST → 可达图” 导航；若只做在线闭包计算，可省略此表。
#### 11. `skill_param` — BKT / 其它技能参数

| 列名          | 类型                         | 说明     |
| ------------- | ---------------------------- | -------- |
| `id`       | INT PK FK → knowledge_points |          |
| `p_l0`        | REAL                         | 初始掌握 |
| `p_t`         | REAL                         | 学习率 T |
| `p_g`         | REAL                         | 猜对 G   |
| `p_s`         | REAL                         | 滑落 S   |
| `last_update` | TIMESTAMPTZ                  |          |
#### 12. `annotation_tasks` — 标注任务

| 列名                      | 类型              | 说明                                    |
| ------------------------- | ----------------- | --------------------------------------- |
| `id`                 | BIGSERIAL         | 主键                                    |
| `task_type`               | SMALLINT          | 0=Q-KP,1=Q-Rel,2=Prereq                 |
| `payload`                 | JSONB             | 问题集合 / 过滤条件                     |
| `assignees`               | BIGINT[]          |                                         |
| `reviewer_id`             | BIGINT            |                                         |
| `state`                   | ENUM `task_state` | `pending / in_progress / review / done` |
| `created_by`              | BIGINT            |                                         |
| `created_at / updated_at` | TIMESTAMPTZ       |                                         |

索引 `(state)`
#### 13. `annotation_logs` — 操作留痕

| 列名          | 类型                         | 说明                        |
| ------------- | ---------------------------- | --------------------------- |
| `id`      | BIGSERIAL                    | 主键                        |
| `task_id`     | BIGINT FK → annotation_tasks |                             |
| `question_id` | BIGINT                       |                             |
| `operation`   | VARCHAR(32)                  | add_kp / del_kp / add_rel … |
| `detail`      | JSONB                        | 变更前后                    |
| `operator_id` | BIGINT FK → users            |                             |
| `ts`          | TIMESTAMPTZ                  |                             |
#### 14. `question_versions` — 题目快照

| 列名           | 类型                  | 说明                        |
| -------------- | --------------------- | --------------------------- |
| `id`   | BIGSERIAL             | 主键                        |
| `question_id`  | BIGINT FK → questions |                             |
| `revision_num` | INT                   |                             |
| `snapshot`     | JSONB                 | questions + mapping + param |
| `changes_note` | TEXT                  |                             |
| `created_by`   | BIGINT                |                             |
| `created_at`   | TIMESTAMPTZ           |                             |
#### 15. `kp_versions` — 知识点/先修快照

| 列名         | 类型                      | 说明                     |
| ------------ | ------------------------- | ------------------------ |
| `id` | BIGSERIAL                 | 主键                     |
| `kp_id`      | INT FK → knowledge_points |                          |
| `snapshot`   | JSONB                     | 包含路径、描述、先修集合 |
| `created_by` | BIGINT                    |                          |
| `created_at` | TIMESTAMPTZ               |                          |

### 数据类型约定
- 主键: `BIGSERIAL` 或 `SERIAL`
- 时间戳: `TIMESTAMPTZ`
- JSON 数据: `JSONB`
- 布尔值: `BOOLEAN`
- 路径: `LTREE` (知识点层级)
- 位向量: `BIT VARYING`

### 枚举类型
```sql
-- 题型枚举
CREATE TYPE q_type AS ENUM ('SC', 'MC', 'TF', 'Cloze', 'Essay');

-- 题-题关系枚举
CREATE TYPE q_rel_type AS ENUM ('complements', 'progresses_to', 'equivalent', 'prerequisite', 'revision');

-- 任务状态枚举
CREATE TYPE task_state AS ENUM ('pending', 'in_progress', 'review', 'done');
```

## 开发规范

### SQL 编写规范
1. **命名约定**
   - 表名: 小写下划线分隔 (`knowledge_points`)
   - 字段名: 小写下划线分隔 (`created_at`)
   - 外键: `表名_id` 格式 (`user_id`)
   - 主键 `id`

2. **索引策略**
   - 主键自动创建索引
   - 外键字段创建索引
   - 查询频繁字段创建复合索引
   - 全文搜索使用 GIN 索引

3. **约束规则**
   - 所有表必须有主键
   - 外键关系明确定义
   - 非空约束合理设置
   - 唯一约束防止重复

### 数据完整性
1. **引用完整性**
   - 所有外键必须有对应的主键
   - 级联删除策略明确定义

2. **业务约束**
   - 先修关系无环检查
   - 知识点层级路径一致性
   - 题目-知识点映射有效性

3. **版本控制**
   - 所有修改记录到审计表
   - 关键数据变更创建快照
   - 支持版本回滚功能

### 性能优化
1. **查询优化**
   - 使用适当的索引
   - 避免 N+1 查询
   - 使用 EXPLAIN 分析查询计划

2. **数据分区**
   - 大表考虑按时间分区
   - 历史数据归档策略

3. **缓存策略**
   - 频繁查询数据使用 Redis 缓存
   - 知识空间状态内存缓存


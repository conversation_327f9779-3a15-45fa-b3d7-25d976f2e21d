---
type: "always_apply"
---

# 自适应学习服务数据标注模块 - UI/UE设计规约

## 1. 设计理念

### 1.1 设计原则
- **用户中心**：以用户体验为核心，简化操作流程
- **一致性**：保持界面元素和交互行为的一致性
- **可访问性**：支持无障碍访问，符合WCAG 2.1标准
- **响应式**：适配不同屏幕尺寸和设备
- **效率优先**：提高标注工作效率，减少认知负担

### 1.2 设计目标
- 降低学习成本，新用户能快速上手
- 提高标注效率，减少重复操作
- 减少错误率，通过设计引导正确操作
- 增强用户满意度，提供愉悦的使用体验

## 2. 视觉设计规范

### 2.1 色彩系统

#### 2.1.1 主色调
```css
/* 主品牌色 */
--primary-50: #eff6ff;
--primary-100: #dbeafe;
--primary-500: #3b82f6;  /* 主色 */
--primary-600: #2563eb;
--primary-700: #1d4ed8;
--primary-900: #1e3a8a;

/* 辅助色 */
--secondary-50: #f8fafc;
--secondary-100: #f1f5f9;
--secondary-500: #64748b;
--secondary-600: #475569;
--secondary-900: #0f172a;
```

#### 2.1.2 功能色彩
```css
/* 成功状态 */
--success-50: #f0fdf4;
--success-500: #22c55e;
--success-600: #16a34a;

/* 警告状态 */
--warning-50: #fffbeb;
--warning-500: #f59e0b;
--warning-600: #d97706;

/* 错误状态 */
--error-50: #fef2f2;
--error-500: #ef4444;
--error-600: #dc2626;

/* 信息状态 */
--info-50: #eff6ff;
--info-500: #3b82f6;
--info-600: #2563eb;
```

#### 2.1.3 中性色彩
```css
/* 灰度色阶 */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;
```

### 2.2 字体系统

#### 2.2.1 字体族
```css
/* 主字体 */
--font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

/* 等宽字体 */
--font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

/* 中文字体 */
--font-chinese: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
```

#### 2.2.2 字体大小
```css
/* 字体尺寸 */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
```

#### 2.2.3 字重
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### 2.3 间距系统

#### 2.3.1 基础间距
```css
/* 8px基础单位 */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
```

#### 2.3.2 布局间距
```css
/* 组件间距 */
--gap-xs: var(--space-2);
--gap-sm: var(--space-3);
--gap-md: var(--space-4);
--gap-lg: var(--space-6);
--gap-xl: var(--space-8);

/* 页面边距 */
--padding-page: var(--space-6);
--padding-section: var(--space-8);
```

### 2.4 圆角系统
```css
--radius-none: 0;
--radius-sm: 0.125rem;   /* 2px */
--radius-md: 0.375rem;   /* 6px */
--radius-lg: 0.5rem;     /* 8px */
--radius-xl: 0.75rem;    /* 12px */
--radius-full: 9999px;
```

### 2.5 阴影系统
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
```

## 3. 组件设计规范

### 3.1 按钮组件

#### 3.1.1 按钮类型
- **Primary Button**：主要操作按钮，每个页面最多一个
- **Secondary Button**：次要操作按钮
- **Outline Button**：边框按钮，用于取消等操作
- **Ghost Button**：透明按钮，用于辅助操作
- **Danger Button**：危险操作按钮，如删除

#### 3.1.2 按钮尺寸
```css
/* 小尺寸 */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

/* 中等尺寸（默认） */
.btn-md {
  padding: 0.5rem 1rem;
  font-size: var(--text-base);
  border-radius: var(--radius-md);
}

/* 大尺寸 */
.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--text-lg);
  border-radius: var(--radius-lg);
}
```

#### 3.1.3 按钮状态
- **Default**：默认状态
- **Hover**：鼠标悬停状态
- **Active**：激活状态
- **Disabled**：禁用状态
- **Loading**：加载状态

### 3.2 表单组件

#### 3.2.1 输入框
```css
.input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.input:invalid {
  border-color: var(--error-500);
}
```

#### 3.2.2 标签和帮助文本
```css
.label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  margin-bottom: var(--space-1);
}

.help-text {
  font-size: var(--text-xs);
  color: var(--gray-500);
  margin-top: var(--space-1);
}

.error-text {
  font-size: var(--text-xs);
  color: var(--error-500);
  margin-top: var(--space-1);
}
```

### 3.3 数据展示组件

#### 3.3.1 表格
```css
.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th {
  background-color: var(--gray-50);
  padding: var(--space-3) var(--space-4);
  text-align: left;
  font-weight: var(--font-medium);
  border-bottom: 1px solid var(--gray-200);
}

.table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--gray-100);
}

.table tr:hover {
  background-color: var(--gray-50);
}
```

#### 3.3.2 卡片
```css
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}
```

### 3.4 反馈组件

#### 3.4.1 消息提示
```css
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}
```

#### 3.4.2 加载状态
```css
.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

## 4. 布局设计规范

### 4.1 页面布局

#### 4.1.1 整体布局结构
```
┌─────────────────────────────────────┐
│              Header                 │
├─────────────┬───────────────────────┤
│             │                       │
│   Sidebar   │      Main Content     │
│             │                       │
├─────────────┴───────────────────────┤
│              Footer                 │
└─────────────────────────────────────┘
```

#### 4.1.2 响应式断点
```css
/* 移动设备 */
@media (max-width: 640px) { /* sm */ }

/* 平板设备 */
@media (min-width: 641px) and (max-width: 1024px) { /* md */ }

/* 桌面设备 */
@media (min-width: 1025px) { /* lg */ }

/* 大屏设备 */
@media (min-width: 1280px) { /* xl */ }
```

### 4.2 网格系统

#### 4.2.1 12列网格
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--space-2));
}

.col {
  flex: 1;
  padding: 0 var(--space-2);
}

/* 响应式列 */
.col-sm-6 { flex: 0 0 50%; }
.col-md-4 { flex: 0 0 33.333333%; }
.col-lg-3 { flex: 0 0 25%; }
```

## 5. 交互设计规范

### 5.1 导航设计

#### 5.1.1 主导航
- 使用面包屑导航显示当前位置
- 侧边栏导航支持折叠
- 活跃状态明确标识

#### 5.1.2 标签页导航
- 最多显示8个标签页
- 支持标签页关闭
- 当前标签页明确标识

### 5.2 表单交互

#### 5.2.1 表单验证
- 实时验证，即时反馈
- 错误信息具体明确
- 成功状态给予确认

#### 5.2.2 表单提交
- 提交按钮显示加载状态
- 防止重复提交
- 提交成功给予反馈

### 5.3 数据操作

#### 5.3.1 批量操作
- 支持全选/反选
- 批量操作按钮状态管理
- 操作确认对话框

#### 5.3.2 搜索过滤
- 搜索框支持快捷键
- 过滤条件可视化
- 搜索结果高亮

## 6. 可访问性规范

### 6.1 键盘导航
- 所有交互元素支持键盘访问
- Tab键顺序逻辑合理
- 焦点状态清晰可见

### 6.2 屏幕阅读器
- 语义化HTML标签
- 适当的ARIA标签
- 图片提供alt文本

### 6.3 色彩对比
- 文本对比度至少4.5:1
- 重要信息不仅依赖颜色
- 支持高对比度模式

## 7. 动效设计规范

### 7.1 过渡动画
```css
/* 标准过渡 */
.transition-standard {
  transition: all 0.2s ease-in-out;
}

/* 快速过渡 */
.transition-fast {
  transition: all 0.1s ease-in-out;
}

/* 慢速过渡 */
.transition-slow {
  transition: all 0.3s ease-in-out;
}
```

### 7.2 动画原则
- 动画时长不超过300ms
- 使用缓动函数增强自然感
- 避免过度动画影响性能

## 8. 特殊场景设计

### 8.1 空状态
- 提供友好的空状态插图
- 明确的操作指引
- 避免空白页面

### 8.2 错误状态
- 错误信息简洁明了
- 提供解决方案
- 支持重试操作

### 8.3 加载状态
- 骨架屏优于转圈加载
- 长时间加载显示进度
- 支持取消操作

## 9. 设计工具和资源

### 9.1 设计系统工具
- Figma设计文件
- Storybook组件库
- 设计Token配置

### 9.2 图标系统
- 使用Heroicons图标库
- 保持图标风格一致
- 支持多种尺寸

### 9.3 插图和图片
- 统一的插图风格
- 高质量的图片资源
- 支持暗色模式

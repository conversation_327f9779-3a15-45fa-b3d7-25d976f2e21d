"""Add version management tables

Revision ID: f8c6cec6a791
Revises: 
Create Date: 2025-07-07 19:35:06.621999

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'f8c6cec6a791'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('model_config',
    sa.Column('config_id', sa.Integer(), autoincrement=True, nullable=False, comment='配置ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='配置名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='配置描述'),
    sa.Column('model_type', sa.String(length=10), nullable=False, comment='模型类型'),
    sa.Column('config_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='配置参数(JSON格式)'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('is_default', sa.Boolean(), nullable=False, comment='是否默认配置'),
    sa.Column('version', sa.String(length=20), nullable=False, comment='配置版本'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.BigInteger(), nullable=True, comment='创建用户ID'),
    sa.Column('updated_by', sa.BigInteger(), nullable=True, comment='更新用户ID'),
    sa.CheckConstraint("model_type IN ('BKT', 'IRT', 'DKT', 'DINA', 'DINO')", name='ck_model_config_model_type'),
    sa.PrimaryKeyConstraint('config_id'),
    sa.UniqueConstraint('name'),
    comment='模型配置表'
    )
    op.create_index('idx_model_config_is_active', 'model_config', ['is_active'], unique=False)
    op.create_index('idx_model_config_is_default', 'model_config', ['is_default'], unique=False)
    op.create_index('idx_model_config_model_type', 'model_config', ['model_type'], unique=False)
    op.create_index('idx_model_config_name', 'model_config', ['name'], unique=False)
    op.create_table('kp_versions',
    sa.Column('version_id', sa.BigInteger(), autoincrement=True, nullable=False, comment='版本ID'),
    sa.Column('kp_id', sa.Integer(), nullable=False, comment='知识点ID'),
    sa.Column('version_number', sa.Integer(), nullable=False, comment='版本号'),
    sa.Column('version_tag', sa.String(length=50), nullable=True, comment='版本标签'),
    sa.Column('change_type', sa.SmallInteger(), nullable=False, comment='变更类型'),
    sa.Column('change_description', sa.Text(), nullable=True, comment='变更描述'),
    sa.Column('snapshot_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='知识点快照数据'),
    sa.Column('diff_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='与前一版本的差异'),
    sa.Column('is_current', sa.Boolean(), nullable=False, comment='是否当前版本'),
    sa.Column('is_published', sa.Boolean(), nullable=False, comment='是否已发布'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='版本元数据'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.BigInteger(), nullable=True, comment='创建用户ID'),
    sa.Column('updated_by', sa.BigInteger(), nullable=True, comment='更新用户ID'),
    sa.CheckConstraint('change_type IN (0, 1, 2, 3)', name='ck_kp_versions_change_type'),
    sa.CheckConstraint('version_number > 0', name='ck_kp_versions_version_number'),
    sa.ForeignKeyConstraint(['kp_id'], ['knowledge_points.kp_id'], ),
    sa.PrimaryKeyConstraint('version_id'),
    comment='知识点版本表'
    )
    op.create_index('idx_kp_versions_change_type', 'kp_versions', ['change_type'], unique=False)
    op.create_index('idx_kp_versions_composite', 'kp_versions', ['kp_id', 'version_number'], unique=False)
    op.create_index('idx_kp_versions_is_current', 'kp_versions', ['is_current'], unique=False)
    op.create_index('idx_kp_versions_is_published', 'kp_versions', ['is_published'], unique=False)
    op.create_index('idx_kp_versions_kp_id', 'kp_versions', ['kp_id'], unique=False)
    op.create_index('idx_kp_versions_version_number', 'kp_versions', ['version_number'], unique=False)
    op.create_table('question_versions',
    sa.Column('version_id', sa.BigInteger(), autoincrement=True, nullable=False, comment='版本ID'),
    sa.Column('question_id', sa.BigInteger(), nullable=False, comment='题目ID'),
    sa.Column('version_number', sa.Integer(), nullable=False, comment='版本号'),
    sa.Column('version_tag', sa.String(length=50), nullable=True, comment='版本标签'),
    sa.Column('change_type', sa.SmallInteger(), nullable=False, comment='变更类型'),
    sa.Column('change_description', sa.Text(), nullable=True, comment='变更描述'),
    sa.Column('snapshot_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='题目快照数据'),
    sa.Column('diff_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='与前一版本的差异'),
    sa.Column('is_current', sa.Boolean(), nullable=False, comment='是否当前版本'),
    sa.Column('is_published', sa.Boolean(), nullable=False, comment='是否已发布'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='版本元数据'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.BigInteger(), nullable=True, comment='创建用户ID'),
    sa.Column('updated_by', sa.BigInteger(), nullable=True, comment='更新用户ID'),
    sa.CheckConstraint('change_type IN (0, 1, 2, 3)', name='ck_question_versions_change_type'),
    sa.CheckConstraint('version_number > 0', name='ck_question_versions_version_number'),
    sa.ForeignKeyConstraint(['question_id'], ['questions.question_id'], ),
    sa.PrimaryKeyConstraint('version_id'),
    comment='题目版本表'
    )
    op.create_index('idx_question_versions_change_type', 'question_versions', ['change_type'], unique=False)
    op.create_index('idx_question_versions_composite', 'question_versions', ['question_id', 'version_number'], unique=False)
    op.create_index('idx_question_versions_is_current', 'question_versions', ['is_current'], unique=False)
    op.create_index('idx_question_versions_is_published', 'question_versions', ['is_published'], unique=False)
    op.create_index('idx_question_versions_question_id', 'question_versions', ['question_id'], unique=False)
    op.create_index('idx_question_versions_version_number', 'question_versions', ['version_number'], unique=False)
    op.alter_column('annotation_logs', 'log_id',
               existing_type=sa.BIGINT(),
               comment='日志ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('annotation_logs', 'task_id',
               existing_type=sa.BIGINT(),
               comment='任务ID',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'user_id',
               existing_type=sa.BIGINT(),
               comment='操作用户ID',
               existing_nullable=False)
    op.alter_column('annotation_logs', 'action',
               existing_type=sa.SMALLINT(),
               comment='操作类型',
               existing_nullable=False)
    op.alter_column('annotation_logs', 'description',
               existing_type=sa.TEXT(),
               comment='操作描述',
               existing_nullable=False)
    op.alter_column('annotation_logs', 'old_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='操作前数据',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'new_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='操作后数据',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment='IP地址',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'user_agent',
               existing_type=sa.TEXT(),
               comment='用户代理',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_table_comment(
        'annotation_logs',
        '标注操作日志表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('annotation_tasks', 'task_id',
               existing_type=sa.BIGINT(),
               comment='任务ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('annotation_tasks', 'title',
               existing_type=sa.TEXT(),
               comment='任务标题',
               existing_nullable=False)
    op.alter_column('annotation_tasks', 'description',
               existing_type=sa.TEXT(),
               comment='任务描述',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'task_type',
               existing_type=sa.SMALLINT(),
               comment='任务类型',
               existing_nullable=False)
    op.alter_column('annotation_tasks', 'status',
               existing_type=sa.SMALLINT(),
               comment='任务状态',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('annotation_tasks', 'priority',
               existing_type=sa.SMALLINT(),
               comment='优先级(1-5)',
               existing_nullable=False,
               existing_server_default=sa.text('3'))
    op.alter_column('annotation_tasks', 'assigned_to',
               existing_type=sa.BIGINT(),
               comment='分配给用户ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'reviewer_id',
               existing_type=sa.BIGINT(),
               comment='审核员ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'due_date',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='截止时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'started_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='开始时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'completed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='完成时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'reviewed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='审核时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'task_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='任务数据(JSON格式)',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'result_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='结果数据(JSON格式)',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'progress',
               existing_type=sa.SMALLINT(),
               comment='完成进度(0-100)',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('annotation_tasks', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_tasks', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_tasks', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_annotation_tasks_created_by', 'annotation_tasks', ['created_by'], unique=False)
    op.create_index('idx_annotation_tasks_due_date', 'annotation_tasks', ['due_date'], unique=False)
    op.create_index('idx_annotation_tasks_priority', 'annotation_tasks', ['priority'], unique=False)
    op.create_index('idx_annotation_tasks_reviewer_id', 'annotation_tasks', ['reviewer_id'], unique=False)
    op.create_table_comment(
        'annotation_tasks',
        '标注任务表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('item_kp_map', 'question_id',
               existing_type=sa.BIGINT(),
               comment='题目ID',
               existing_nullable=False)
    op.alter_column('item_kp_map', 'kp_id',
               existing_type=sa.INTEGER(),
               comment='知识点ID',
               existing_nullable=False)
    op.alter_column('item_kp_map', 'is_required',
               existing_type=sa.BOOLEAN(),
               comment='是否必需掌握',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('item_kp_map', 'weight',
               existing_type=sa.REAL(),
               comment='权重',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('item_kp_map', 'confidence',
               existing_type=sa.REAL(),
               comment='置信度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('item_kp_map', 'source',
               existing_type=sa.SMALLINT(),
               comment='标注来源(0=专家,1=算法,2=导入)',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('item_kp_map', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_kp_map', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_kp_map', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('item_kp_map', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_item_kp_map_source', 'item_kp_map', ['source'], unique=False)
    op.create_table_comment(
        'item_kp_map',
        '题目-知识点映射表(Q-矩阵)',
        existing_comment=None,
        schema=None
    )
    op.alter_column('item_param', 'question_id',
               existing_type=sa.BIGINT(),
               comment='题目ID',
               existing_nullable=False)
    op.alter_column('item_param', 'a',
               existing_type=sa.REAL(),
               comment='区分度参数',
               existing_nullable=True)
    op.alter_column('item_param', 'b',
               existing_type=sa.REAL(),
               comment='难度参数',
               existing_nullable=True)
    op.alter_column('item_param', 'c',
               existing_type=sa.REAL(),
               comment='猜测参数',
               existing_nullable=True)
    op.alter_column('item_param', 'last_calibrated',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='最后校准时间',
               existing_nullable=True)
    op.alter_column('item_param', 'calibration_sample_size',
               existing_type=sa.INTEGER(),
               comment='校准样本量',
               existing_nullable=True)
    op.alter_column('item_param', 'model_fit',
               existing_type=sa.REAL(),
               comment='模型拟合度',
               existing_nullable=True)
    op.alter_column('item_param', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_param', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_table_comment(
        'item_param',
        'IRT参数表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('knowledge_points', 'kp_id',
               existing_type=sa.INTEGER(),
               comment='知识点ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('knowledge_points_kp_id_seq'::regclass)"))
    op.alter_column('knowledge_points', 'parent_id',
               existing_type=sa.INTEGER(),
               comment='父级知识点ID',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'path',
               existing_type=sa.TEXT(),
               comment='知识点层级路径',
               existing_nullable=False)
    op.alter_column('knowledge_points', 'name',
               existing_type=sa.TEXT(),
               comment='知识点名称',
               existing_nullable=False)
    op.alter_column('knowledge_points', 'code',
               existing_type=sa.TEXT(),
               comment='知识点编码',
               existing_nullable=False)
    op.alter_column('knowledge_points', 'description',
               existing_type=sa.TEXT(),
               comment='知识点描述',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'is_leaf',
               existing_type=sa.BOOLEAN(),
               comment='是否叶子节点',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('knowledge_points', 'difficulty_level',
               existing_type=sa.SMALLINT(),
               comment='难度等级(1-5)',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_points', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_points', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_kp_name', 'knowledge_points', ['name'], unique=False, postgresql_using='gin', postgresql_ops={'name': 'gin_trgm_ops'})
    op.create_table_comment(
        'knowledge_points',
        '知识点表',
        existing_comment=None,
        schema=None
    )
    op.add_column('knowledge_state', sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据(JSON格式)'))
    op.alter_column('knowledge_state', 'state_id',
               existing_type=sa.BIGINT(),
               comment='状态ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('knowledge_state_state_id_seq'::regclass)"))
    op.alter_column('knowledge_state', 'state_vector',
               existing_type=postgresql.ARRAY(sa.BOOLEAN()),
               comment='知识状态向量',
               existing_nullable=False)
    op.alter_column('knowledge_state', 'state_hash',
               existing_type=sa.TEXT(),
               comment='状态哈希值',
               existing_nullable=False)
    op.alter_column('knowledge_state', 'state_type',
               existing_type=sa.SMALLINT(),
               comment='状态类型',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
    op.alter_column('knowledge_state', 'is_valid',
               existing_type=sa.BOOLEAN(),
               comment='是否有效状态',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('knowledge_state', 'mastery_count',
               existing_type=sa.INTEGER(),
               comment='掌握的知识点数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('knowledge_state', 'total_count',
               existing_type=sa.INTEGER(),
               comment='总知识点数量',
               existing_nullable=False)
    op.alter_column('knowledge_state', 'mastery_ratio',
               existing_type=sa.REAL(),
               comment='掌握比例',
               existing_nullable=False,
               existing_server_default=sa.text('0.0'))
    op.alter_column('knowledge_state', 'description',
               existing_type=sa.TEXT(),
               comment='状态描述',
               existing_nullable=True)
    op.alter_column('knowledge_state', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_state', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_state', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('knowledge_state', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_knowledge_state_mastery_count', 'knowledge_state', ['mastery_count'], unique=False)
    op.create_table_comment(
        'knowledge_state',
        '知识状态表',
        existing_comment=None,
        schema=None
    )
    op.drop_column('knowledge_state', 'metadata')
    op.alter_column('prerequisite_relation', 'pre_kp_id',
               existing_type=sa.INTEGER(),
               comment='前置知识点ID',
               existing_nullable=False)
    op.alter_column('prerequisite_relation', 'post_kp_id',
               existing_type=sa.INTEGER(),
               comment='后置知识点ID',
               existing_nullable=False)
    op.alter_column('prerequisite_relation', 'source',
               existing_type=sa.SMALLINT(),
               comment='关系来源',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('prerequisite_relation', 'confidence',
               existing_type=sa.REAL(),
               comment='置信度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('prerequisite_relation', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('prerequisite_relation', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('prerequisite_relation', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('prerequisite_relation', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_prereq_source', 'prerequisite_relation', ['source'], unique=False)
    op.create_table_comment(
        'prerequisite_relation',
        '知识点先修关系表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('question_assets', 'asset_id',
               existing_type=sa.BIGINT(),
               comment='媒资ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('question_assets', 'question_id',
               existing_type=sa.BIGINT(),
               comment='题目ID',
               existing_nullable=False)
    op.alter_column('question_assets', 'uri',
               existing_type=sa.TEXT(),
               comment='媒资URI',
               existing_nullable=False)
    op.alter_column('question_assets', 'media_type',
               existing_type=sa.VARCHAR(length=50),
               comment='媒体类型',
               existing_nullable=False)
    op.alter_column('question_assets', 'alt_text',
               existing_type=sa.TEXT(),
               comment='替代文本',
               existing_nullable=True)
    op.alter_column('question_assets', 'file_size',
               existing_type=sa.BIGINT(),
               comment='文件大小(字节)',
               existing_nullable=True)
    op.alter_column('question_assets', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_assets', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_table_comment(
        'question_assets',
        '题目媒资表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('question_relation', 'relation_id',
               existing_type=sa.BIGINT(),
               comment='关系ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('question_relation', 'source_question_id',
               existing_type=sa.BIGINT(),
               comment='源题目ID',
               existing_nullable=False)
    op.alter_column('question_relation', 'target_question_id',
               existing_type=sa.BIGINT(),
               comment='目标题目ID',
               existing_nullable=False)
    op.alter_column('question_relation', 'relation_type',
               existing_type=sa.SMALLINT(),
               comment='关系类型',
               existing_nullable=False)
    op.alter_column('question_relation', 'strength',
               existing_type=sa.REAL(),
               comment='关系强度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('question_relation', 'confidence',
               existing_type=sa.REAL(),
               comment='置信度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('question_relation', 'source',
               existing_type=sa.SMALLINT(),
               comment='标注来源(0=专家,1=算法,2=导入)',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('question_relation', 'description',
               existing_type=sa.TEXT(),
               comment='关系描述',
               existing_nullable=True)
    op.alter_column('question_relation', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_relation', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_relation', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('question_relation', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_question_relation_composite', 'question_relation', ['source_question_id', 'target_question_id', 'relation_type'], unique=False)
    op.create_index('idx_question_relation_source', 'question_relation', ['source'], unique=False)
    op.create_table_comment(
        'question_relation',
        '题目关系表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('questions', 'question_id',
               existing_type=sa.BIGINT(),
               comment='题目ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('questions_question_id_seq'::regclass)"))
    op.alter_column('questions', 'content',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='题目内容(JSON格式)',
               existing_nullable=False)
    op.alter_column('questions', 'q_type',
               existing_type=sa.SMALLINT(),
               comment='题目类型',
               existing_nullable=False)
    op.alter_column('questions', 'difficulty_lvl',
               existing_type=sa.SMALLINT(),
               comment='难度等级(1-5)',
               existing_nullable=True)
    op.alter_column('questions', 'irt_ready',
               existing_type=sa.BOOLEAN(),
               comment='IRT是否已校准',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('questions', 'answer_key',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='正确答案(JSON格式)',
               existing_nullable=True)
    op.alter_column('questions', 'analysis',
               existing_type=sa.TEXT(),
               comment='题目解析',
               existing_nullable=True)
    op.alter_column('questions', 'source',
               existing_type=sa.TEXT(),
               comment='题目来源',
               existing_nullable=True)
    op.alter_column('questions', 'tags',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               comment='标签数组',
               existing_nullable=True)
    op.alter_column('questions', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否启用',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('questions', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('questions', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('questions', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('questions', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_questions_created_by', 'questions', ['created_by'], unique=False)
    op.create_index('idx_questions_source', 'questions', ['source'], unique=False)
    op.create_table_comment(
        'questions',
        '题目主表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('skill_param', 'kp_id',
               existing_type=sa.INTEGER(),
               comment='知识点ID',
               existing_nullable=False)
    op.alter_column('skill_param', 'p_l0',
               existing_type=sa.REAL(),
               comment='初始掌握概率',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_t',
               existing_type=sa.REAL(),
               comment='学习率(转移概率)',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_g',
               existing_type=sa.REAL(),
               comment='猜对概率',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_s',
               existing_type=sa.REAL(),
               comment='滑落概率',
               existing_nullable=True)
    op.alter_column('skill_param', 'model_version',
               existing_type=sa.VARCHAR(length=16),
               comment='模型版本',
               existing_nullable=True)
    op.alter_column('skill_param', 'model_type',
               existing_type=sa.VARCHAR(length=10),
               comment='模型类型',
               existing_nullable=False,
               existing_server_default=sa.text("'BKT'::character varying"))
    op.alter_column('skill_param', 'last_update',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='最后更新时间',
               existing_nullable=True)
    op.alter_column('skill_param', 'calibration_quality',
               existing_type=sa.NUMERIC(precision=3, scale=2),
               comment='校准质量评分',
               existing_nullable=True)
    op.alter_column('skill_param', 'sample_size',
               existing_type=sa.INTEGER(),
               comment='校准样本量',
               existing_nullable=True)
    op.alter_column('skill_param', 'extended_params',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='扩展参数(JSON格式)',
               existing_nullable=True)
    op.alter_column('skill_param', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('skill_param', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('skill_param', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('skill_param', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_skill_param_calibration_quality', 'skill_param', ['calibration_quality'], unique=False)
    op.create_table_comment(
        'skill_param',
        '技能参数表(BKT参数)',
        existing_comment=None,
        schema=None
    )
    op.add_column('state_transition', sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据(JSON格式)'))
    op.alter_column('state_transition', 'transition_id',
               existing_type=sa.BIGINT(),
               comment='转移ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('state_transition', 'from_state_id',
               existing_type=sa.BIGINT(),
               comment='源状态ID',
               existing_nullable=False)
    op.alter_column('state_transition', 'to_state_id',
               existing_type=sa.BIGINT(),
               comment='目标状态ID',
               existing_nullable=False)
    op.alter_column('state_transition', 'transition_type',
               existing_type=sa.SMALLINT(),
               comment='转移类型',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('state_transition', 'probability',
               existing_type=sa.REAL(),
               comment='转移概率',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('state_transition', 'trigger_kp_id',
               existing_type=sa.INTEGER(),
               comment='触发知识点ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'trigger_question_id',
               existing_type=sa.BIGINT(),
               comment='触发题目ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'learning_cost',
               existing_type=sa.REAL(),
               comment='学习成本',
               existing_nullable=True)
    op.alter_column('state_transition', 'difficulty',
               existing_type=sa.REAL(),
               comment='转移难度',
               existing_nullable=True)
    op.alter_column('state_transition', 'description',
               existing_type=sa.TEXT(),
               comment='转移描述',
               existing_nullable=True)
    op.alter_column('state_transition', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('state_transition', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('state_transition', 'created_by',
               existing_type=sa.BIGINT(),
               comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'updated_by',
               existing_type=sa.BIGINT(),
               comment='更新用户ID',
               existing_nullable=True)
    op.create_index('idx_state_transition_composite', 'state_transition', ['from_state_id', 'to_state_id', 'transition_type'], unique=False)
    op.create_index('idx_state_transition_probability', 'state_transition', ['probability'], unique=False)
    op.create_index('idx_state_transition_trigger_kp_id', 'state_transition', ['trigger_kp_id'], unique=False)
    op.create_index('idx_state_transition_trigger_question_id', 'state_transition', ['trigger_question_id'], unique=False)
    op.create_table_comment(
        'state_transition',
        '状态转移表',
        existing_comment=None,
        schema=None
    )
    op.drop_column('state_transition', 'metadata')
    op.alter_column('user_sessions', 'session_id',
               existing_type=sa.VARCHAR(length=128),
               comment='会话ID',
               existing_nullable=False)
    op.alter_column('user_sessions', 'user_id',
               existing_type=sa.BIGINT(),
               comment='用户ID',
               existing_nullable=False)
    op.alter_column('user_sessions', 'ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment='IP地址',
               existing_nullable=True)
    op.alter_column('user_sessions', 'user_agent',
               existing_type=sa.TEXT(),
               comment='用户代理',
               existing_nullable=True)
    op.alter_column('user_sessions', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='过期时间',
               existing_nullable=False)
    op.alter_column('user_sessions', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否激活',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('user_sessions', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('user_sessions', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_table_comment(
        'user_sessions',
        '用户会话表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('users', 'user_id',
               existing_type=sa.BIGINT(),
               comment='用户ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('users_user_id_seq'::regclass)"))
    op.alter_column('users', 'username',
               existing_type=sa.TEXT(),
               comment='用户名',
               existing_nullable=False)
    op.alter_column('users', 'password_hash',
               existing_type=sa.TEXT(),
               comment='密码哈希',
               existing_nullable=False)
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=16),
               comment='用户角色',
               existing_nullable=False)
    op.alter_column('users', 'full_name',
               existing_type=sa.TEXT(),
               comment='真实姓名',
               existing_nullable=True)
    op.alter_column('users', 'email',
               existing_type=sa.TEXT(),
               comment='邮箱地址',
               existing_nullable=True)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否激活',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('users', 'last_login',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='最后登录时间',
               existing_nullable=True)
    op.alter_column('users', 'login_count',
               existing_type=sa.INTEGER(),
               comment='登录次数',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_table_comment(
        'users',
        '系统用户表',
        existing_comment=None,
        schema=None
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table_comment(
        'users',
        existing_comment='系统用户表',
        schema=None
    )
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('users', 'login_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='登录次数',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('users', 'last_login',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='最后登录时间',
               existing_nullable=True)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否激活',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('users', 'email',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='邮箱地址',
               existing_nullable=True)
    op.alter_column('users', 'full_name',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='真实姓名',
               existing_nullable=True)
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=16),
               comment=None,
               existing_comment='用户角色',
               existing_nullable=False)
    op.alter_column('users', 'password_hash',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='密码哈希',
               existing_nullable=False)
    op.alter_column('users', 'username',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='用户名',
               existing_nullable=False)
    op.alter_column('users', 'user_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('users_user_id_seq'::regclass)"))
    op.drop_table_comment(
        'user_sessions',
        existing_comment='用户会话表',
        schema=None
    )
    op.alter_column('user_sessions', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('user_sessions', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('user_sessions', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否激活',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('user_sessions', 'expires_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='过期时间',
               existing_nullable=False)
    op.alter_column('user_sessions', 'user_agent',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='用户代理',
               existing_nullable=True)
    op.alter_column('user_sessions', 'ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment=None,
               existing_comment='IP地址',
               existing_nullable=True)
    op.alter_column('user_sessions', 'user_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=False)
    op.alter_column('user_sessions', 'session_id',
               existing_type=sa.VARCHAR(length=128),
               comment=None,
               existing_comment='会话ID',
               existing_nullable=False)
    op.add_column('state_transition', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_table_comment(
        'state_transition',
        existing_comment='状态转移表',
        schema=None
    )
    op.drop_index('idx_state_transition_trigger_question_id', table_name='state_transition')
    op.drop_index('idx_state_transition_trigger_kp_id', table_name='state_transition')
    op.drop_index('idx_state_transition_probability', table_name='state_transition')
    op.drop_index('idx_state_transition_composite', table_name='state_transition')
    op.alter_column('state_transition', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('state_transition', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('state_transition', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='转移描述',
               existing_nullable=True)
    op.alter_column('state_transition', 'difficulty',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='转移难度',
               existing_nullable=True)
    op.alter_column('state_transition', 'learning_cost',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='学习成本',
               existing_nullable=True)
    op.alter_column('state_transition', 'trigger_question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='触发题目ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'trigger_kp_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='触发知识点ID',
               existing_nullable=True)
    op.alter_column('state_transition', 'probability',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='转移概率',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('state_transition', 'transition_type',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='转移类型',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('state_transition', 'to_state_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='目标状态ID',
               existing_nullable=False)
    op.alter_column('state_transition', 'from_state_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='源状态ID',
               existing_nullable=False)
    op.alter_column('state_transition', 'transition_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='转移ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('state_transition', 'meta_data')
    op.drop_table_comment(
        'skill_param',
        existing_comment='技能参数表(BKT参数)',
        schema=None
    )
    op.drop_index('idx_skill_param_calibration_quality', table_name='skill_param')
    op.alter_column('skill_param', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('skill_param', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('skill_param', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('skill_param', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('skill_param', 'extended_params',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='扩展参数(JSON格式)',
               existing_nullable=True)
    op.alter_column('skill_param', 'sample_size',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='校准样本量',
               existing_nullable=True)
    op.alter_column('skill_param', 'calibration_quality',
               existing_type=sa.NUMERIC(precision=3, scale=2),
               comment=None,
               existing_comment='校准质量评分',
               existing_nullable=True)
    op.alter_column('skill_param', 'last_update',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='最后更新时间',
               existing_nullable=True)
    op.alter_column('skill_param', 'model_type',
               existing_type=sa.VARCHAR(length=10),
               comment=None,
               existing_comment='模型类型',
               existing_nullable=False,
               existing_server_default=sa.text("'BKT'::character varying"))
    op.alter_column('skill_param', 'model_version',
               existing_type=sa.VARCHAR(length=16),
               comment=None,
               existing_comment='模型版本',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_s',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='滑落概率',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_g',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='猜对概率',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_t',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='学习率(转移概率)',
               existing_nullable=True)
    op.alter_column('skill_param', 'p_l0',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='初始掌握概率',
               existing_nullable=True)
    op.alter_column('skill_param', 'kp_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='知识点ID',
               existing_nullable=False)
    op.drop_table_comment(
        'questions',
        existing_comment='题目主表',
        schema=None
    )
    op.drop_index('idx_questions_source', table_name='questions')
    op.drop_index('idx_questions_created_by', table_name='questions')
    op.alter_column('questions', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('questions', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('questions', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('questions', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('questions', 'is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否启用',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('questions', 'tags',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               comment=None,
               existing_comment='标签数组',
               existing_nullable=True)
    op.alter_column('questions', 'source',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='题目来源',
               existing_nullable=True)
    op.alter_column('questions', 'analysis',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='题目解析',
               existing_nullable=True)
    op.alter_column('questions', 'answer_key',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='正确答案(JSON格式)',
               existing_nullable=True)
    op.alter_column('questions', 'irt_ready',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='IRT是否已校准',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('questions', 'difficulty_lvl',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='难度等级(1-5)',
               existing_nullable=True)
    op.alter_column('questions', 'q_type',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='题目类型',
               existing_nullable=False)
    op.alter_column('questions', 'content',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='题目内容(JSON格式)',
               existing_nullable=False)
    op.alter_column('questions', 'question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='题目ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('questions_question_id_seq'::regclass)"))
    op.drop_table_comment(
        'question_relation',
        existing_comment='题目关系表',
        schema=None
    )
    op.drop_index('idx_question_relation_source', table_name='question_relation')
    op.drop_index('idx_question_relation_composite', table_name='question_relation')
    op.alter_column('question_relation', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('question_relation', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('question_relation', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_relation', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_relation', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='关系描述',
               existing_nullable=True)
    op.alter_column('question_relation', 'source',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='标注来源(0=专家,1=算法,2=导入)',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('question_relation', 'confidence',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='置信度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('question_relation', 'strength',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='关系强度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('question_relation', 'relation_type',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='关系类型',
               existing_nullable=False)
    op.alter_column('question_relation', 'target_question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='目标题目ID',
               existing_nullable=False)
    op.alter_column('question_relation', 'source_question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='源题目ID',
               existing_nullable=False)
    op.alter_column('question_relation', 'relation_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='关系ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_table_comment(
        'question_assets',
        existing_comment='题目媒资表',
        schema=None
    )
    op.alter_column('question_assets', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_assets', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('question_assets', 'file_size',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='文件大小(字节)',
               existing_nullable=True)
    op.alter_column('question_assets', 'alt_text',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='替代文本',
               existing_nullable=True)
    op.alter_column('question_assets', 'media_type',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='媒体类型',
               existing_nullable=False)
    op.alter_column('question_assets', 'uri',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='媒资URI',
               existing_nullable=False)
    op.alter_column('question_assets', 'question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='题目ID',
               existing_nullable=False)
    op.alter_column('question_assets', 'asset_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='媒资ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_table_comment(
        'prerequisite_relation',
        existing_comment='知识点先修关系表',
        schema=None
    )
    op.drop_index('idx_prereq_source', table_name='prerequisite_relation')
    op.alter_column('prerequisite_relation', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('prerequisite_relation', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('prerequisite_relation', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('prerequisite_relation', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('prerequisite_relation', 'confidence',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='置信度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('prerequisite_relation', 'source',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='关系来源',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('prerequisite_relation', 'post_kp_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='后置知识点ID',
               existing_nullable=False)
    op.alter_column('prerequisite_relation', 'pre_kp_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='前置知识点ID',
               existing_nullable=False)
    op.add_column('knowledge_state', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_table_comment(
        'knowledge_state',
        existing_comment='知识状态表',
        schema=None
    )
    op.drop_index('idx_knowledge_state_mastery_count', table_name='knowledge_state')
    op.alter_column('knowledge_state', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('knowledge_state', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('knowledge_state', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_state', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_state', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='状态描述',
               existing_nullable=True)
    op.alter_column('knowledge_state', 'mastery_ratio',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='掌握比例',
               existing_nullable=False,
               existing_server_default=sa.text('0.0'))
    op.alter_column('knowledge_state', 'total_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='总知识点数量',
               existing_nullable=False)
    op.alter_column('knowledge_state', 'mastery_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='掌握的知识点数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('knowledge_state', 'is_valid',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否有效状态',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('knowledge_state', 'state_type',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='状态类型',
               existing_nullable=False,
               existing_server_default=sa.text('1'))
    op.alter_column('knowledge_state', 'state_hash',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='状态哈希值',
               existing_nullable=False)
    op.alter_column('knowledge_state', 'state_vector',
               existing_type=postgresql.ARRAY(sa.BOOLEAN()),
               comment=None,
               existing_comment='知识状态向量',
               existing_nullable=False)
    op.alter_column('knowledge_state', 'state_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='状态ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('knowledge_state_state_id_seq'::regclass)"))
    op.drop_column('knowledge_state', 'meta_data')
    op.drop_table_comment(
        'knowledge_points',
        existing_comment='知识点表',
        schema=None
    )
    op.drop_index('idx_kp_name', table_name='knowledge_points', postgresql_using='gin', postgresql_ops={'name': 'gin_trgm_ops'})
    op.alter_column('knowledge_points', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_points', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('knowledge_points', 'difficulty_level',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='难度等级(1-5)',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'is_leaf',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否叶子节点',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('knowledge_points', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='知识点描述',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'code',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='知识点编码',
               existing_nullable=False)
    op.alter_column('knowledge_points', 'name',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='知识点名称',
               existing_nullable=False)
    op.alter_column('knowledge_points', 'path',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='知识点层级路径',
               existing_nullable=False)
    op.alter_column('knowledge_points', 'parent_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='父级知识点ID',
               existing_nullable=True)
    op.alter_column('knowledge_points', 'kp_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='知识点ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('knowledge_points_kp_id_seq'::regclass)"))
    op.drop_table_comment(
        'item_param',
        existing_comment='IRT参数表',
        schema=None
    )
    op.alter_column('item_param', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_param', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_param', 'model_fit',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='模型拟合度',
               existing_nullable=True)
    op.alter_column('item_param', 'calibration_sample_size',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='校准样本量',
               existing_nullable=True)
    op.alter_column('item_param', 'last_calibrated',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='最后校准时间',
               existing_nullable=True)
    op.alter_column('item_param', 'c',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='猜测参数',
               existing_nullable=True)
    op.alter_column('item_param', 'b',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='难度参数',
               existing_nullable=True)
    op.alter_column('item_param', 'a',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='区分度参数',
               existing_nullable=True)
    op.alter_column('item_param', 'question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='题目ID',
               existing_nullable=False)
    op.drop_table_comment(
        'item_kp_map',
        existing_comment='题目-知识点映射表(Q-矩阵)',
        schema=None
    )
    op.drop_index('idx_item_kp_map_source', table_name='item_kp_map')
    op.alter_column('item_kp_map', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('item_kp_map', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('item_kp_map', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_kp_map', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('item_kp_map', 'source',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='标注来源(0=专家,1=算法,2=导入)',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('item_kp_map', 'confidence',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='置信度',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('item_kp_map', 'weight',
               existing_type=sa.REAL(),
               comment=None,
               existing_comment='权重',
               existing_nullable=False,
               existing_server_default=sa.text('1.0'))
    op.alter_column('item_kp_map', 'is_required',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否必需掌握',
               existing_nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('item_kp_map', 'kp_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='知识点ID',
               existing_nullable=False)
    op.alter_column('item_kp_map', 'question_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='题目ID',
               existing_nullable=False)
    op.drop_table_comment(
        'annotation_tasks',
        existing_comment='标注任务表',
        schema=None
    )
    op.drop_index('idx_annotation_tasks_reviewer_id', table_name='annotation_tasks')
    op.drop_index('idx_annotation_tasks_priority', table_name='annotation_tasks')
    op.drop_index('idx_annotation_tasks_due_date', table_name='annotation_tasks')
    op.drop_index('idx_annotation_tasks_created_by', table_name='annotation_tasks')
    op.alter_column('annotation_tasks', 'updated_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='更新用户ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'created_by',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='创建用户ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_tasks', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_tasks', 'progress',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='完成进度(0-100)',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('annotation_tasks', 'result_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='结果数据(JSON格式)',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'task_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='任务数据(JSON格式)',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'reviewed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='审核时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'completed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='完成时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'started_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='开始时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'due_date',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='截止时间',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'reviewer_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='审核员ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'assigned_to',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='分配给用户ID',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'priority',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='优先级(1-5)',
               existing_nullable=False,
               existing_server_default=sa.text('3'))
    op.alter_column('annotation_tasks', 'status',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='任务状态',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('annotation_tasks', 'task_type',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='任务类型',
               existing_nullable=False)
    op.alter_column('annotation_tasks', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='任务描述',
               existing_nullable=True)
    op.alter_column('annotation_tasks', 'title',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='任务标题',
               existing_nullable=False)
    op.alter_column('annotation_tasks', 'task_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='任务ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_table_comment(
        'annotation_logs',
        existing_comment='标注操作日志表',
        schema=None
    )
    op.alter_column('annotation_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('annotation_logs', 'user_agent',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='用户代理',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment=None,
               existing_comment='IP地址',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'new_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='操作后数据',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'old_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='操作前数据',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='操作描述',
               existing_nullable=False)
    op.alter_column('annotation_logs', 'action',
               existing_type=sa.SMALLINT(),
               comment=None,
               existing_comment='操作类型',
               existing_nullable=False)
    op.alter_column('annotation_logs', 'user_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='操作用户ID',
               existing_nullable=False)
    op.alter_column('annotation_logs', 'task_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='任务ID',
               existing_nullable=True)
    op.alter_column('annotation_logs', 'log_id',
               existing_type=sa.BIGINT(),
               comment=None,
               existing_comment='日志ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_index('idx_question_versions_version_number', table_name='question_versions')
    op.drop_index('idx_question_versions_question_id', table_name='question_versions')
    op.drop_index('idx_question_versions_is_published', table_name='question_versions')
    op.drop_index('idx_question_versions_is_current', table_name='question_versions')
    op.drop_index('idx_question_versions_composite', table_name='question_versions')
    op.drop_index('idx_question_versions_change_type', table_name='question_versions')
    op.drop_table('question_versions')
    op.drop_index('idx_kp_versions_version_number', table_name='kp_versions')
    op.drop_index('idx_kp_versions_kp_id', table_name='kp_versions')
    op.drop_index('idx_kp_versions_is_published', table_name='kp_versions')
    op.drop_index('idx_kp_versions_is_current', table_name='kp_versions')
    op.drop_index('idx_kp_versions_composite', table_name='kp_versions')
    op.drop_index('idx_kp_versions_change_type', table_name='kp_versions')
    op.drop_table('kp_versions')
    op.drop_index('idx_model_config_name', table_name='model_config')
    op.drop_index('idx_model_config_model_type', table_name='model_config')
    op.drop_index('idx_model_config_is_default', table_name='model_config')
    op.drop_index('idx_model_config_is_active', table_name='model_config')
    op.drop_table('model_config')
    # ### end Alembic commands ###

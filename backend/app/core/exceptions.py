"""
自定义异常和异常处理器
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from typing import Union

logger = logging.getLogger(__name__)


class AnnotationException(Exception):
    """标注系统基础异常"""
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(self.message)


class AuthenticationError(AnnotationException):
    """认证错误"""
    pass


class AuthorizationError(AnnotationException):
    """授权错误"""
    pass


class ValidationError(AnnotationException):
    """验证错误"""
    pass


class NotFoundError(AnnotationException):
    """资源未找到错误"""
    pass


class ConflictError(AnnotationException):
    """冲突错误"""
    pass


class DatabaseError(AnnotationException):
    """数据库错误"""
    pass


class ExternalServiceError(AnnotationException):
    """外部服务错误"""
    pass


async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "code": f"HTTP_{exc.status_code}",
            "timestamp": request.state.timestamp if hasattr(request.state, 'timestamp') else None
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.warning(f"验证错误: {exc.errors()}")
    
    errors = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        errors.append({
            "field": field,
            "message": error["msg"],
            "type": error["type"]
        })
    
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "请求数据验证失败",
            "code": "VALIDATION_ERROR",
            "errors": errors,
            "timestamp": request.state.timestamp if hasattr(request.state, 'timestamp') else None
        }
    )


async def annotation_exception_handler(request: Request, exc: AnnotationException):
    """标注系统异常处理器"""
    logger.error(f"标注系统异常: {exc.code} - {exc.message}")
    
    # 根据异常类型确定HTTP状态码
    status_code = 500
    if isinstance(exc, AuthenticationError):
        status_code = 401
    elif isinstance(exc, AuthorizationError):
        status_code = 403
    elif isinstance(exc, ValidationError):
        status_code = 422
    elif isinstance(exc, NotFoundError):
        status_code = 404
    elif isinstance(exc, ConflictError):
        status_code = 409
    
    return JSONResponse(
        status_code=status_code,
        content={
            "success": False,
            "message": exc.message,
            "code": exc.code or exc.__class__.__name__.upper(),
            "timestamp": request.state.timestamp if hasattr(request.state, 'timestamp') else None
        }
    )


async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "code": "INTERNAL_SERVER_ERROR",
            "timestamp": request.state.timestamp if hasattr(request.state, 'timestamp') else None
        }
    )


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    # HTTP异常
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    
    # 验证异常
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 自定义异常
    app.add_exception_handler(AnnotationException, annotation_exception_handler)
    
    # 通用异常
    app.add_exception_handler(Exception, general_exception_handler)

"""
权限控制系统
"""

from enum import Enum
from typing import Dict, List, Set
from app.models.user import UserRole


class Permission(str, Enum):
    """权限枚举"""
    # 题目权限
    QUESTIONS_READ = "questions:read"
    QUESTIONS_WRITE = "questions:write"
    QUESTIONS_DELETE = "questions:delete"
    QUESTIONS_IMPORT = "questions:import"
    QUESTIONS_EXPORT = "questions:export"
    
    # 知识点权限
    KNOWLEDGE_POINTS_READ = "knowledge_points:read"
    KNOWLEDGE_POINTS_WRITE = "knowledge_points:write"
    KNOWLEDGE_POINTS_DELETE = "knowledge_points:delete"
    KNOWLEDGE_POINTS_MANAGE = "knowledge_points:manage"
    
    # 标注权限
    ANNOTATION_READ = "annotation:read"
    ANNOTATION_WRITE = "annotation:write"
    ANNOTATION_REVIEW = "annotation:review"
    ANNOTATION_APPROVE = "annotation:approve"
    ANNOTATION_ASSIGN = "annotation:assign"
    
    # 用户管理权限
    USERS_READ = "users:read"
    USERS_WRITE = "users:write"
    USERS_DELETE = "users:delete"
    USERS_MANAGE_ROLES = "users:manage_roles"
    
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_LOGS = "system:logs"
    SYSTEM_BACKUP = "system:backup"
    
    # 数据权限
    DATA_EXPORT = "data:export"
    DATA_IMPORT = "data:import"
    DATA_ANALYTICS = "data:analytics"


# 角色权限映射
ROLE_PERMISSIONS: Dict[UserRole, Set[Permission]] = {
    UserRole.ADMIN: {
        # 管理员拥有所有权限
        Permission.QUESTIONS_READ,
        Permission.QUESTIONS_WRITE,
        Permission.QUESTIONS_DELETE,
        Permission.QUESTIONS_IMPORT,
        Permission.QUESTIONS_EXPORT,
        Permission.KNOWLEDGE_POINTS_READ,
        Permission.KNOWLEDGE_POINTS_WRITE,
        Permission.KNOWLEDGE_POINTS_DELETE,
        Permission.KNOWLEDGE_POINTS_MANAGE,
        Permission.ANNOTATION_READ,
        Permission.ANNOTATION_WRITE,
        Permission.ANNOTATION_REVIEW,
        Permission.ANNOTATION_APPROVE,
        Permission.ANNOTATION_ASSIGN,
        Permission.USERS_READ,
        Permission.USERS_WRITE,
        Permission.USERS_DELETE,
        Permission.USERS_MANAGE_ROLES,
        Permission.SYSTEM_ADMIN,
        Permission.SYSTEM_CONFIG,
        Permission.SYSTEM_LOGS,
        Permission.SYSTEM_BACKUP,
        Permission.DATA_EXPORT,
        Permission.DATA_IMPORT,
        Permission.DATA_ANALYTICS,
    },
    
    UserRole.REVIEWER: {
        # 审核员权限
        Permission.QUESTIONS_READ,
        Permission.QUESTIONS_WRITE,
        Permission.QUESTIONS_EXPORT,
        Permission.KNOWLEDGE_POINTS_READ,
        Permission.KNOWLEDGE_POINTS_WRITE,
        Permission.ANNOTATION_READ,
        Permission.ANNOTATION_WRITE,
        Permission.ANNOTATION_REVIEW,
        Permission.ANNOTATION_APPROVE,
        Permission.DATA_EXPORT,
        Permission.DATA_ANALYTICS,
    },
    
    UserRole.ANNOTATOR: {
        # 标注员权限
        Permission.QUESTIONS_READ,
        Permission.QUESTIONS_EXPORT,
        Permission.KNOWLEDGE_POINTS_READ,
        Permission.ANNOTATION_READ,
        Permission.ANNOTATION_WRITE,
        Permission.DATA_EXPORT,
    },
    
    UserRole.VIEWER: {
        # 查看者权限
        Permission.QUESTIONS_READ,
        Permission.KNOWLEDGE_POINTS_READ,
        Permission.ANNOTATION_READ,
    },
    
    UserRole.ENGINE: {
        # 引擎用户权限（API访问）
        Permission.QUESTIONS_READ,
        Permission.KNOWLEDGE_POINTS_READ,
        Permission.ANNOTATION_READ,
        Permission.DATA_EXPORT,
    }
}


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def has_permission(user_role: UserRole, permission: Permission) -> bool:
        """检查用户角色是否拥有指定权限"""
        return permission in ROLE_PERMISSIONS.get(user_role, set())
    
    @staticmethod
    def has_any_permission(user_role: UserRole, permissions: List[Permission]) -> bool:
        """检查用户角色是否拥有任意一个权限"""
        user_permissions = ROLE_PERMISSIONS.get(user_role, set())
        return any(permission in user_permissions for permission in permissions)
    
    @staticmethod
    def has_all_permissions(user_role: UserRole, permissions: List[Permission]) -> bool:
        """检查用户角色是否拥有所有权限"""
        user_permissions = ROLE_PERMISSIONS.get(user_role, set())
        return all(permission in user_permissions for permission in permissions)
    
    @staticmethod
    def get_user_permissions(user_role: UserRole) -> Set[Permission]:
        """获取用户角色的所有权限"""
        return ROLE_PERMISSIONS.get(user_role, set())
    
    @staticmethod
    def can_manage_users(user_role: UserRole) -> bool:
        """检查是否可以管理用户"""
        return PermissionChecker.has_permission(user_role, Permission.USERS_WRITE)
    
    @staticmethod
    def can_annotate(user_role: UserRole) -> bool:
        """检查是否可以进行标注"""
        return PermissionChecker.has_permission(user_role, Permission.ANNOTATION_WRITE)
    
    @staticmethod
    def can_review(user_role: UserRole) -> bool:
        """检查是否可以审核"""
        return PermissionChecker.has_permission(user_role, Permission.ANNOTATION_REVIEW)
    
    @staticmethod
    def can_admin(user_role: UserRole) -> bool:
        """检查是否为系统管理员"""
        return PermissionChecker.has_permission(user_role, Permission.SYSTEM_ADMIN)


def require_permission(permission: Permission):
    """权限装饰器工厂"""
    def decorator(func):
        func._required_permission = permission
        return func
    return decorator


def require_any_permission(*permissions: Permission):
    """任意权限装饰器工厂"""
    def decorator(func):
        func._required_any_permissions = list(permissions)
        return func
    return decorator


def require_all_permissions(*permissions: Permission):
    """所有权限装饰器工厂"""
    def decorator(func):
        func._required_all_permissions = list(permissions)
        return func
    return decorator

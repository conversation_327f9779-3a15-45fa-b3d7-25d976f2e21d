"""
Dapr客户端配置和服务
"""

import json
import logging
from typing import Any, Dict, Optional
import httpx
from app.core.config import settings

logger = logging.getLogger(__name__)


class DaprClient:
    """Dapr客户端"""
    
    def __init__(self):
        self.dapr_port = getattr(settings, 'DAPR_HTTP_PORT', 3500)
        self.base_url = f"http://localhost:{self.dapr_port}/v1.0"
        self.app_id = getattr(settings, 'APP_ID', 'annotation-backend')
        
    async def invoke_service(
        self, 
        service_id: str, 
        method: str, 
        data: Optional[Dict[str, Any]] = None,
        http_verb: str = "POST"
    ) -> Dict[str, Any]:
        """
        调用其他服务
        
        Args:
            service_id: 目标服务ID
            method: 调用方法
            data: 请求数据
            http_verb: HTTP方法
            
        Returns:
            响应数据
        """
        url = f"{self.base_url}/invoke/{service_id}/method/{method}"
        
        try:
            async with httpx.AsyncClient() as client:
                if http_verb.upper() == "GET":
                    response = await client.get(url, params=data)
                elif http_verb.upper() == "POST":
                    response = await client.post(url, json=data)
                elif http_verb.upper() == "PUT":
                    response = await client.put(url, json=data)
                elif http_verb.upper() == "DELETE":
                    response = await client.delete(url)
                else:
                    raise ValueError(f"不支持的HTTP方法: {http_verb}")
                
                response.raise_for_status()
                return response.json()
                
        except httpx.RequestError as e:
            logger.error(f"调用服务失败: {service_id}/{method} - {e}")
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"服务调用返回错误: {service_id}/{method} - {e.response.status_code}")
            raise
    
    async def get_state(self, store_name: str, key: str) -> Optional[Any]:
        """
        从状态存储获取数据
        
        Args:
            store_name: 状态存储名称
            key: 键名
            
        Returns:
            存储的值
        """
        url = f"{self.base_url}/state/{store_name}/{key}"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url)
                
                if response.status_code == 204:
                    return None
                
                response.raise_for_status()
                return response.json()
                
        except httpx.RequestError as e:
            logger.error(f"获取状态失败: {store_name}/{key} - {e}")
            raise
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return None
            logger.error(f"获取状态返回错误: {store_name}/{key} - {e.response.status_code}")
            raise
    
    async def save_state(self, store_name: str, key: str, value: Any) -> None:
        """
        保存数据到状态存储
        
        Args:
            store_name: 状态存储名称
            key: 键名
            value: 要保存的值
        """
        url = f"{self.base_url}/state/{store_name}"
        
        data = [
            {
                "key": key,
                "value": value
            }
        ]
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=data)
                response.raise_for_status()
                
        except httpx.RequestError as e:
            logger.error(f"保存状态失败: {store_name}/{key} - {e}")
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"保存状态返回错误: {store_name}/{key} - {e.response.status_code}")
            raise
    
    async def delete_state(self, store_name: str, key: str) -> None:
        """
        从状态存储删除数据
        
        Args:
            store_name: 状态存储名称
            key: 键名
        """
        url = f"{self.base_url}/state/{store_name}/{key}"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(url)
                response.raise_for_status()
                
        except httpx.RequestError as e:
            logger.error(f"删除状态失败: {store_name}/{key} - {e}")
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"删除状态返回错误: {store_name}/{key} - {e.response.status_code}")
            raise
    
    async def publish_event(
        self, 
        pubsub_name: str, 
        topic: str, 
        data: Dict[str, Any]
    ) -> None:
        """
        发布事件
        
        Args:
            pubsub_name: 发布订阅组件名称
            topic: 主题名称
            data: 事件数据
        """
        url = f"{self.base_url}/publish/{pubsub_name}/{topic}"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=data)
                response.raise_for_status()
                
        except httpx.RequestError as e:
            logger.error(f"发布事件失败: {pubsub_name}/{topic} - {e}")
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"发布事件返回错误: {pubsub_name}/{topic} - {e.response.status_code}")
            raise
    
    async def get_secret(self, secret_store: str, key: str) -> Optional[str]:
        """
        从密钥存储获取密钥
        
        Args:
            secret_store: 密钥存储名称
            key: 密钥名称
            
        Returns:
            密钥值
        """
        url = f"{self.base_url}/secrets/{secret_store}/{key}"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url)
                
                if response.status_code == 204:
                    return None
                
                response.raise_for_status()
                result = response.json()
                return result.get(key)
                
        except httpx.RequestError as e:
            logger.error(f"获取密钥失败: {secret_store}/{key} - {e}")
            raise
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return None
            logger.error(f"获取密钥返回错误: {secret_store}/{key} - {e.response.status_code}")
            raise


# 全局Dapr客户端实例
dapr_client = DaprClient()


# 便捷函数
async def invoke_service(service_id: str, method: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """调用其他服务的便捷函数"""
    return await dapr_client.invoke_service(service_id, method, data)


async def get_state(store_name: str, key: str) -> Optional[Any]:
    """获取状态的便捷函数"""
    return await dapr_client.get_state(store_name, key)


async def save_state(store_name: str, key: str, value: Any) -> None:
    """保存状态的便捷函数"""
    await dapr_client.save_state(store_name, key, value)


async def publish_event(pubsub_name: str, topic: str, data: Dict[str, Any]) -> None:
    """发布事件的便捷函数"""
    await dapr_client.publish_event(pubsub_name, topic, data)

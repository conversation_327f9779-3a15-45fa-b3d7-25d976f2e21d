"""
质量检测服务类
"""

from typing import Any, Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, text, case
from datetime import datetime, timedelta
import logging

from app.models.question import Question, ItemParam
from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from app.models.mapping import ItemKpMap, QuestionRelation
from app.models.annotation import AnnotationTask, AnnotationLog

logger = logging.getLogger(__name__)


class QualityService:
    """质量检测服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def run_full_quality_check(self) -> Dict[str, Any]:
        """执行完整的质量检测"""
        logger.info("开始执行完整质量检测")
        
        results = {
            'timestamp': datetime.utcnow().isoformat(),
            'basic_checks': self._run_basic_checks(),
            'business_rules': self._run_business_rules_check(),
            'consistency_checks': self._run_consistency_checks(),
            'anomaly_detection': self._run_anomaly_detection(),
            'quality_score': 0.0,
            'summary': {}
        }
        
        # 计算综合质量得分
        results['quality_score'] = self._calculate_quality_score(results)
        results['summary'] = self._generate_summary(results)
        
        logger.info(f"质量检测完成，综合得分: {results['quality_score']:.2f}")
        return results
    
    def _run_basic_checks(self) -> Dict[str, Any]:
        """基础格式检查"""
        logger.info("执行基础格式检查")
        
        checks = {
            'orphan_questions': self._check_orphan_questions(),
            'orphan_knowledge_points': self._check_orphan_knowledge_points(),
            'missing_content': self._check_missing_content(),
            'invalid_confidence': self._check_invalid_confidence(),
            'data_completeness': self._check_data_completeness()
        }
        
        # 计算基础检查得分
        total_issues = sum(check.get('count', 0) for check in checks.values())
        checks['score'] = max(0, 100 - total_issues * 2)  # 每个问题扣2分
        
        return checks
    
    def _run_business_rules_check(self) -> Dict[str, Any]:
        """业务规则验证"""
        logger.info("执行业务规则验证")
        
        checks = {
            'confidence_range': self._check_confidence_range(),
            'mapping_limits': self._check_mapping_limits(),
            'difficulty_consistency': self._check_difficulty_consistency(),
            'prerequisite_violations': self._check_prerequisite_violations()
        }
        
        # 计算业务规则得分
        total_violations = sum(check.get('count', 0) for check in checks.values())
        checks['score'] = max(0, 100 - total_violations * 3)  # 每个违规扣3分
        
        return checks
    
    def _run_consistency_checks(self) -> Dict[str, Any]:
        """逻辑一致性检查"""
        logger.info("执行逻辑一致性检查")
        
        checks = {
            'prerequisite_cycles': self._check_prerequisite_cycles(),
            'transitivity_violations': self._check_transitivity_violations(),
            'hierarchy_consistency': self._check_hierarchy_consistency(),
            'relation_conflicts': self._check_relation_conflicts()
        }
        
        # 计算一致性得分
        total_inconsistencies = sum(check.get('count', 0) for check in checks.values())
        checks['score'] = max(0, 100 - total_inconsistencies * 5)  # 每个不一致扣5分
        
        return checks
    
    def _run_anomaly_detection(self) -> Dict[str, Any]:
        """异常检测"""
        logger.info("执行异常检测")
        
        checks = {
            'statistical_outliers': self._detect_statistical_outliers(),
            'pattern_anomalies': self._detect_pattern_anomalies(),
            'frequency_anomalies': self._detect_frequency_anomalies(),
            'distribution_anomalies': self._detect_distribution_anomalies()
        }
        
        # 计算异常检测得分
        total_anomalies = sum(check.get('count', 0) for check in checks.values())
        checks['score'] = max(0, 100 - total_anomalies * 1)  # 每个异常扣1分
        
        return checks
    
    def _check_orphan_questions(self) -> Dict[str, Any]:
        """检查孤儿题目（无知识点关联的题目）"""
        query = self.db.query(Question.question_id, Question.content).filter(
            Question.is_active == True,
            ~Question.question_id.in_(
                self.db.query(ItemKpMap.question_id).distinct()
            )
        )
        
        orphan_questions = query.all()
        
        return {
            'count': len(orphan_questions),
            'items': [
                {
                    'question_id': q.question_id,
                    'content_preview': str(q.content.get('stem', ''))[:100] if q.content else ''
                }
                for q in orphan_questions[:10]  # 只返回前10个
            ],
            'severity': 'high' if len(orphan_questions) > 0 else 'none',
            'description': '无知识点关联的题目'
        }
    
    def _check_orphan_knowledge_points(self) -> Dict[str, Any]:
        """检查孤儿知识点（无题目关联的叶子知识点）"""
        query = self.db.query(KnowledgePoint.kp_id, KnowledgePoint.name).filter(
            KnowledgePoint.is_leaf == True,
            ~KnowledgePoint.kp_id.in_(
                self.db.query(ItemKpMap.kp_id).distinct()
            )
        )
        
        orphan_kps = query.all()
        
        return {
            'count': len(orphan_kps),
            'items': [
                {
                    'kp_id': kp.kp_id,
                    'name': kp.name
                }
                for kp in orphan_kps[:10]
            ],
            'severity': 'medium' if len(orphan_kps) > 0 else 'none',
            'description': '无题目关联的叶子知识点'
        }
    
    def _check_missing_content(self) -> Dict[str, Any]:
        """检查缺失内容的题目"""
        query = self.db.query(Question.question_id).filter(
            Question.is_active == True,
            or_(
                Question.content.is_(None),
                Question.content['stem'].astext.is_(None),
                Question.content['stem'].astext == ''
            )
        )
        
        missing_content = query.all()
        
        return {
            'count': len(missing_content),
            'items': [{'question_id': q.question_id} for q in missing_content[:10]],
            'severity': 'high' if len(missing_content) > 0 else 'none',
            'description': '缺失题干内容的题目'
        }
    
    def _check_invalid_confidence(self) -> Dict[str, Any]:
        """检查无效置信度的映射"""
        query = self.db.query(
            ItemKpMap.question_id, 
            ItemKpMap.kp_id, 
            ItemKpMap.confidence
        ).filter(
            or_(
                ItemKpMap.confidence < 0.6,
                ItemKpMap.confidence > 1.0,
                ItemKpMap.confidence.is_(None)
            )
        )
        
        invalid_mappings = query.all()
        
        return {
            'count': len(invalid_mappings),
            'items': [
                {
                    'question_id': m.question_id,
                    'kp_id': m.kp_id,
                    'confidence': m.confidence
                }
                for m in invalid_mappings[:10]
            ],
            'severity': 'high' if len(invalid_mappings) > 0 else 'none',
            'description': '置信度不在有效范围(0.6-1.0)的映射'
        }
    
    def _check_data_completeness(self) -> Dict[str, Any]:
        """检查数据完整性"""
        total_questions = self.db.query(func.count(Question.question_id)).filter(
            Question.is_active == True
        ).scalar()
        
        annotated_questions = self.db.query(
            func.count(func.distinct(ItemKpMap.question_id))
        ).scalar()
        
        completeness_rate = (annotated_questions / total_questions * 100) if total_questions > 0 else 0
        
        return {
            'total_questions': total_questions,
            'annotated_questions': annotated_questions,
            'completeness_rate': round(completeness_rate, 2),
            'missing_count': total_questions - annotated_questions,
            'severity': 'low' if completeness_rate >= 80 else 'medium' if completeness_rate >= 60 else 'high',
            'description': '数据标注完整性统计'
        }
    
    def _check_confidence_range(self) -> Dict[str, Any]:
        """检查置信度范围违规"""
        query = self.db.query(
            ItemKpMap.question_id,
            ItemKpMap.kp_id,
            ItemKpMap.confidence
        ).filter(
            or_(
                ItemKpMap.confidence < 0.6,
                ItemKpMap.confidence > 1.0
            )
        )
        
        violations = query.all()
        
        return {
            'count': len(violations),
            'items': [
                {
                    'question_id': v.question_id,
                    'kp_id': v.kp_id,
                    'confidence': v.confidence,
                    'issue': '置信度过低' if v.confidence < 0.6 else '置信度超出范围'
                }
                for v in violations[:10]
            ],
            'severity': 'medium' if len(violations) > 0 else 'none',
            'description': '置信度范围违规的映射'
        }
    
    def _check_mapping_limits(self) -> Dict[str, Any]:
        """检查映射数量限制"""
        # 检查每个题目关联的知识点数量
        query = self.db.query(
            ItemKpMap.question_id,
            func.count(ItemKpMap.kp_id).label('kp_count')
        ).group_by(ItemKpMap.question_id).having(
            func.count(ItemKpMap.kp_id) > 10  # 假设限制为10个知识点
        )
        
        violations = query.all()
        
        return {
            'count': len(violations),
            'items': [
                {
                    'question_id': v.question_id,
                    'kp_count': v.kp_count,
                    'issue': f'关联知识点过多({v.kp_count}个)'
                }
                for v in violations[:10]
            ],
            'severity': 'low' if len(violations) > 0 else 'none',
            'description': '关联知识点数量超限的题目'
        }

    def _check_difficulty_consistency(self) -> Dict[str, Any]:
        """检查难度一致性"""
        # 检查IRT难度与先修关系的一致性
        query = text("""
            SELECT
                pr.pre_kp_id,
                pr.post_kp_id,
                pre_q.question_id as pre_question_id,
                post_q.question_id as post_question_id,
                pre_param.b_param as pre_difficulty,
                post_param.b_param as post_difficulty
            FROM prerequisite_relation pr
            JOIN item_kp_map pre_map ON pr.pre_kp_id = pre_map.kp_id
            JOIN item_kp_map post_map ON pr.post_kp_id = post_map.kp_id
            JOIN questions pre_q ON pre_map.question_id = pre_q.question_id
            JOIN questions post_q ON post_map.question_id = post_q.question_id
            JOIN item_param pre_param ON pre_q.question_id = pre_param.question_id
            JOIN item_param post_param ON post_q.question_id = post_param.question_id
            WHERE pre_param.b_param > post_param.b_param + 0.2
            AND pre_q.is_active = true
            AND post_q.is_active = true
        """)

        violations = self.db.execute(query).fetchall()

        return {
            'count': len(violations),
            'items': [
                {
                    'pre_kp_id': v.pre_kp_id,
                    'post_kp_id': v.post_kp_id,
                    'pre_question_id': v.pre_question_id,
                    'post_question_id': v.post_question_id,
                    'pre_difficulty': float(v.pre_difficulty),
                    'post_difficulty': float(v.post_difficulty),
                    'issue': '先修题目难度高于后续题目'
                }
                for v in violations[:10]
            ],
            'severity': 'medium' if len(violations) > 0 else 'none',
            'description': '难度与先修关系不一致的题目对'
        }

    def _check_prerequisite_violations(self) -> Dict[str, Any]:
        """检查先修关系违规"""
        # 检查是否存在自引用
        self_ref_query = self.db.query(PrerequisiteRelation).filter(
            PrerequisiteRelation.pre_kp_id == PrerequisiteRelation.post_kp_id
        )

        self_references = self_ref_query.all()

        return {
            'count': len(self_references),
            'items': [
                {
                    'kp_id': sr.pre_kp_id,
                    'issue': '知识点自引用'
                }
                for sr in self_references[:10]
            ],
            'severity': 'high' if len(self_references) > 0 else 'none',
            'description': '先修关系自引用违规'
        }

    def _check_prerequisite_cycles(self) -> Dict[str, Any]:
        """检查先修关系环路"""
        # 使用递归CTE检测环路
        cycle_query = text("""
            WITH RECURSIVE cycle_detection AS (
                SELECT
                    pre_kp_id,
                    post_kp_id,
                    ARRAY[pre_kp_id] as path,
                    false as is_cycle
                FROM prerequisite_relation

                UNION ALL

                SELECT
                    pr.pre_kp_id,
                    pr.post_kp_id,
                    cd.path || pr.pre_kp_id,
                    pr.pre_kp_id = ANY(cd.path) as is_cycle
                FROM prerequisite_relation pr
                JOIN cycle_detection cd ON pr.pre_kp_id = cd.post_kp_id
                WHERE NOT cd.is_cycle AND array_length(cd.path, 1) < 20
            )
            SELECT DISTINCT path
            FROM cycle_detection
            WHERE is_cycle = true
            LIMIT 10
        """)

        cycles = self.db.execute(cycle_query).fetchall()

        return {
            'count': len(cycles),
            'items': [
                {
                    'cycle_path': cycle.path,
                    'issue': '检测到先修关系环路'
                }
                for cycle in cycles
            ],
            'severity': 'high' if len(cycles) > 0 else 'none',
            'description': '先修关系环路检测'
        }

    def _check_transitivity_violations(self) -> Dict[str, Any]:
        """检查传递性违规"""
        # 检查传递性：如果A->B, B->C，那么应该有A->C
        query = text("""
            SELECT DISTINCT
                pr1.pre_kp_id as a_kp_id,
                pr1.post_kp_id as b_kp_id,
                pr2.post_kp_id as c_kp_id
            FROM prerequisite_relation pr1
            JOIN prerequisite_relation pr2 ON pr1.post_kp_id = pr2.pre_kp_id
            LEFT JOIN prerequisite_relation pr3 ON pr1.pre_kp_id = pr3.pre_kp_id
                AND pr2.post_kp_id = pr3.post_kp_id
            WHERE pr3.pre_kp_id IS NULL
            LIMIT 10
        """)

        violations = self.db.execute(query).fetchall()

        return {
            'count': len(violations),
            'items': [
                {
                    'a_kp_id': v.a_kp_id,
                    'b_kp_id': v.b_kp_id,
                    'c_kp_id': v.c_kp_id,
                    'issue': '缺失传递性关系'
                }
                for v in violations
            ],
            'severity': 'medium' if len(violations) > 0 else 'none',
            'description': '传递性违规检测'
        }

    def _check_hierarchy_consistency(self) -> Dict[str, Any]:
        """检查层级一致性"""
        # 检查知识点层级结构的一致性
        query = self.db.query(KnowledgePoint).filter(
            and_(
                KnowledgePoint.parent_id.isnot(None),
                ~KnowledgePoint.parent_id.in_(
                    self.db.query(KnowledgePoint.kp_id)
                )
            )
        )

        orphaned_children = query.all()

        return {
            'count': len(orphaned_children),
            'items': [
                {
                    'kp_id': kp.kp_id,
                    'name': kp.name,
                    'parent_id': kp.parent_id,
                    'issue': '父级知识点不存在'
                }
                for kp in orphaned_children[:10]
            ],
            'severity': 'high' if len(orphaned_children) > 0 else 'none',
            'description': '层级结构不一致的知识点'
        }

    def _check_relation_conflicts(self) -> Dict[str, Any]:
        """检查关系冲突"""
        # 检查是否存在相互冲突的题目关系
        query = text("""
            SELECT
                qr1.source_question_id,
                qr1.target_question_id,
                qr1.relation_type as type1,
                qr2.relation_type as type2
            FROM question_relation qr1
            JOIN question_relation qr2 ON qr1.source_question_id = qr2.target_question_id
                AND qr1.target_question_id = qr2.source_question_id
            WHERE qr1.relation_type != qr2.relation_type
            LIMIT 10
        """)

        conflicts = self.db.execute(query).fetchall()

        return {
            'count': len(conflicts),
            'items': [
                {
                    'question1_id': c.source_question_id,
                    'question2_id': c.target_question_id,
                    'type1': c.type1,
                    'type2': c.type2,
                    'issue': '题目关系冲突'
                }
                for c in conflicts
            ],
            'severity': 'medium' if len(conflicts) > 0 else 'none',
            'description': '相互冲突的题目关系'
        }

    def _detect_statistical_outliers(self) -> Dict[str, Any]:
        """检测统计异常值"""
        # 检测置信度的异常值
        stats_query = self.db.query(
            func.avg(ItemKpMap.confidence).label('mean'),
            func.stddev(ItemKpMap.confidence).label('stddev')
        ).first()

        if stats_query.stddev is None or stats_query.stddev == 0:
            return {
                'count': 0,
                'items': [],
                'severity': 'none',
                'description': '置信度统计异常检测'
            }

        # 检测超出2个标准差的值
        threshold_low = stats_query.mean - 2 * stats_query.stddev
        threshold_high = stats_query.mean + 2 * stats_query.stddev

        outliers_query = self.db.query(
            ItemKpMap.question_id,
            ItemKpMap.kp_id,
            ItemKpMap.confidence
        ).filter(
            or_(
                ItemKpMap.confidence < threshold_low,
                ItemKpMap.confidence > threshold_high
            )
        )

        outliers = outliers_query.all()

        return {
            'count': len(outliers),
            'items': [
                {
                    'question_id': o.question_id,
                    'kp_id': o.kp_id,
                    'confidence': o.confidence,
                    'mean': float(stats_query.mean),
                    'stddev': float(stats_query.stddev),
                    'issue': '置信度异常值'
                }
                for o in outliers[:10]
            ],
            'severity': 'low' if len(outliers) > 0 else 'none',
            'description': '置信度统计异常检测'
        }

    def _detect_pattern_anomalies(self) -> Dict[str, Any]:
        """检测模式异常"""
        # 检测异常的标注模式（例如：某个用户的标注模式与其他用户差异很大）
        pattern_query = self.db.query(
            ItemKpMap.created_by,
            func.count(ItemKpMap.question_id).label('annotation_count'),
            func.avg(ItemKpMap.confidence).label('avg_confidence')
        ).group_by(ItemKpMap.created_by).having(
            or_(
                func.avg(ItemKpMap.confidence) < 0.7,
                func.avg(ItemKpMap.confidence) > 0.98
            )
        )

        anomalies = pattern_query.all()

        return {
            'count': len(anomalies),
            'items': [
                {
                    'user_id': a.created_by,
                    'annotation_count': a.annotation_count,
                    'avg_confidence': float(a.avg_confidence),
                    'issue': '标注模式异常'
                }
                for a in anomalies[:10]
            ],
            'severity': 'low' if len(anomalies) > 0 else 'none',
            'description': '用户标注模式异常检测'
        }

    def _detect_frequency_anomalies(self) -> Dict[str, Any]:
        """检测频率异常"""
        # 检测标注频率异常（某些知识点被过度或过少标注）
        freq_query = self.db.query(
            ItemKpMap.kp_id,
            func.count(ItemKpMap.question_id).label('usage_count')
        ).group_by(ItemKpMap.kp_id)

        # 计算使用频率的统计信息
        freq_stats = self.db.query(
            func.avg(func.count(ItemKpMap.question_id)).label('mean'),
            func.stddev(func.count(ItemKpMap.question_id)).label('stddev')
        ).group_by(ItemKpMap.kp_id).first()

        if freq_stats is None or freq_stats.stddev is None:
            return {
                'count': 0,
                'items': [],
                'severity': 'none',
                'description': '知识点使用频率异常检测'
            }

        # 检测异常频率
        threshold_low = max(0, freq_stats.mean - 2 * freq_stats.stddev)
        threshold_high = freq_stats.mean + 2 * freq_stats.stddev

        anomalies = freq_query.having(
            or_(
                func.count(ItemKpMap.question_id) < threshold_low,
                func.count(ItemKpMap.question_id) > threshold_high
            )
        ).all()

        return {
            'count': len(anomalies),
            'items': [
                {
                    'kp_id': a.kp_id,
                    'usage_count': a.usage_count,
                    'mean': float(freq_stats.mean),
                    'issue': '使用频率异常'
                }
                for a in anomalies[:10]
            ],
            'severity': 'low' if len(anomalies) > 0 else 'none',
            'description': '知识点使用频率异常检测'
        }

    def _detect_distribution_anomalies(self) -> Dict[str, Any]:
        """检测分布异常"""
        # 检测标注分布的异常情况
        dist_query = self.db.query(
            ItemKpMap.confidence,
            func.count().label('count')
        ).group_by(ItemKpMap.confidence).order_by(ItemKpMap.confidence)

        distribution = dist_query.all()

        # 简单的分布异常检测：检查是否有过度集中在某个值
        total_count = sum(d.count for d in distribution)
        anomalies = []

        for d in distribution:
            percentage = (d.count / total_count) * 100 if total_count > 0 else 0
            if percentage > 50:  # 如果某个置信度值占比超过50%
                anomalies.append({
                    'confidence': d.confidence,
                    'count': d.count,
                    'percentage': round(percentage, 2),
                    'issue': '置信度分布过度集中'
                })

        return {
            'count': len(anomalies),
            'items': anomalies,
            'severity': 'low' if len(anomalies) > 0 else 'none',
            'description': '置信度分布异常检测'
        }

    def _calculate_quality_score(self, results: Dict[str, Any]) -> float:
        """计算综合质量得分"""
        weights = {
            'basic_checks': 0.3,
            'business_rules': 0.4,
            'consistency_checks': 0.2,
            'anomaly_detection': 0.1
        }

        total_score = 0.0
        for category, weight in weights.items():
            if category in results and 'score' in results[category]:
                total_score += results[category]['score'] * weight

        return round(total_score, 2)

    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成质量检测摘要"""
        total_issues = 0
        critical_issues = 0

        for category in ['basic_checks', 'business_rules', 'consistency_checks', 'anomaly_detection']:
            if category in results:
                for check_name, check_result in results[category].items():
                    if check_name != 'score' and isinstance(check_result, dict):
                        count = check_result.get('count', 0)
                        severity = check_result.get('severity', 'none')

                        total_issues += count
                        if severity == 'high':
                            critical_issues += count

        quality_level = 'excellent'
        if results['quality_score'] < 70:
            quality_level = 'poor'
        elif results['quality_score'] < 80:
            quality_level = 'fair'
        elif results['quality_score'] < 90:
            quality_level = 'good'

        return {
            'quality_level': quality_level,
            'total_issues': total_issues,
            'critical_issues': critical_issues,
            'recommendations': self._generate_recommendations(results)
        }

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 基于检测结果生成建议
        if results.get('basic_checks', {}).get('orphan_questions', {}).get('count', 0) > 0:
            recommendations.append("建议为孤儿题目添加知识点标注")

        if results.get('basic_checks', {}).get('orphan_knowledge_points', {}).get('count', 0) > 0:
            recommendations.append("建议为孤儿知识点添加题目关联或调整知识点结构")

        if results.get('basic_checks', {}).get('invalid_confidence', {}).get('count', 0) > 0:
            recommendations.append("建议修正置信度不在有效范围的标注")

        if results.get('consistency_checks', {}).get('prerequisite_cycles', {}).get('count', 0) > 0:
            recommendations.append("建议修复先修关系中的环路问题")

        if results.get('business_rules', {}).get('difficulty_consistency', {}).get('count', 0) > 0:
            recommendations.append("建议检查和调整题目难度与先修关系的一致性")

        if results['quality_score'] < 80:
            recommendations.append("建议加强标注质量控制和审核流程")

        return recommendations

    def get_quality_dashboard_data(self) -> Dict[str, Any]:
        """获取质量监控仪表板数据"""
        # 基础统计
        total_questions = self.db.query(func.count(Question.question_id)).filter(
            Question.is_active == True
        ).scalar()

        annotated_questions = self.db.query(
            func.count(func.distinct(ItemKpMap.question_id))
        ).scalar()

        total_mappings = self.db.query(func.count(ItemKpMap.question_id)).scalar()

        # 质量趋势（最近7天）
        recent_date = datetime.utcnow() - timedelta(days=7)
        recent_annotations = self.db.query(func.count(ItemKpMap.question_id)).filter(
            ItemKpMap.created_at >= recent_date
        ).scalar()

        # 用户标注统计
        user_stats = self.db.query(
            ItemKpMap.created_by,
            func.count(ItemKpMap.question_id).label('annotation_count'),
            func.avg(ItemKpMap.confidence).label('avg_confidence')
        ).group_by(ItemKpMap.created_by).all()

        return {
            'overview': {
                'total_questions': total_questions,
                'annotated_questions': annotated_questions,
                'annotation_coverage': round((annotated_questions / total_questions * 100) if total_questions > 0 else 0, 2),
                'total_mappings': total_mappings,
                'avg_mappings_per_question': round((total_mappings / annotated_questions) if annotated_questions > 0 else 0, 2)
            },
            'recent_activity': {
                'recent_annotations': recent_annotations,
                'daily_average': round(recent_annotations / 7, 2)
            },
            'user_performance': [
                {
                    'user_id': stat.created_by,
                    'annotation_count': stat.annotation_count,
                    'avg_confidence': round(float(stat.avg_confidence), 3)
                }
                for stat in user_stats[:10]
            ]
        }

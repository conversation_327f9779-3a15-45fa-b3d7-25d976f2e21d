"""
知识空间构建服务类
"""

import hashlib
import time
from typing import List, Dict, Set, Tuple, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from app.models.knowledge_space import KnowledgeState, StateTransition, StateType, TransitionType
from app.schemas.knowledge_space import (
    KnowledgeStateCreate, KnowledgeStateUpdate, StateTransitionCreate,
    KnowledgeSpaceStats, KnowledgeSpaceBuildRequest, KnowledgeStateQuery,
    StateTransitionQuery
)
from app.services.knowledge_service import KnowledgeService


class KnowledgeSpaceService:
    """知识空间服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.knowledge_service = KnowledgeService(db)
    
    def build_knowledge_space(self, request: KnowledgeSpaceBuildRequest) -> Dict[str, Any]:
        """构建知识空间"""
        start_time = time.time()
        
        try:
            # 获取知识点列表
            if request.knowledge_point_ids:
                kp_ids = request.knowledge_point_ids
            else:
                kp_ids = [kp.kp_id for kp in self.db.query(KnowledgePoint.kp_id).all()]
            
            if not kp_ids:
                return {
                    "success": False,
                    "message": "没有找到知识点",
                    "build_time": time.time() - start_time
                }
            
            # 如果强制重建，清除现有状态
            if request.force_rebuild:
                self._clear_existing_states(kp_ids)
            
            # 获取先修关系的传递闭包
            closure = self.knowledge_service.get_transitive_closure(kp_ids)
            
            # 使用Next-Closure算法生成知识空间
            states = self._generate_knowledge_space(kp_ids, closure)
            
            # 保存知识状态到数据库
            saved_states = self._save_knowledge_states(states, kp_ids)
            
            # 生成状态转移关系
            transitions = self._generate_state_transitions(saved_states, kp_ids)
            
            # 保存状态转移到数据库
            self._save_state_transitions(transitions)
            
            # 计算统计信息
            stats = self._calculate_stats(kp_ids)
            
            build_time = time.time() - start_time
            
            return {
                "success": True,
                "message": f"知识空间构建成功，生成{len(saved_states)}个状态，{len(transitions)}个转移",
                "stats": stats,
                "build_time": build_time
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"知识空间构建失败：{str(e)}",
                "build_time": time.time() - start_time
            }
    
    def _generate_knowledge_space(self, kp_ids: List[int], closure: Dict[int, List[int]]) -> List[List[bool]]:
        """使用Next-Closure算法生成知识空间"""
        n = len(kp_ids)
        kp_index_map = {kp_id: i for i, kp_id in enumerate(kp_ids)}
        
        # 初始化空状态
        states = []
        current_state = [False] * n
        
        while True:
            # 计算当前状态的闭包
            closed_state = self._compute_closure(current_state, kp_ids, closure, kp_index_map)
            
            # 如果闭包等于当前状态，则这是一个有效的知识状态
            if closed_state == current_state:
                states.append(current_state[:])
            
            # 生成下一个状态
            next_state = self._next_closure(current_state, kp_ids, closure, kp_index_map)
            
            if next_state is None:
                break
                
            current_state = next_state
        
        return states
    
    def _compute_closure(self, state: List[bool], kp_ids: List[int], 
                        closure: Dict[int, List[int]], kp_index_map: Dict[int, int]) -> List[bool]:
        """计算状态的闭包"""
        closed_state = state[:]
        changed = True
        
        while changed:
            changed = False
            for i, kp_id in enumerate(kp_ids):
                if not closed_state[i]:
                    # 检查所有先修知识点是否都已掌握
                    prerequisites = closure.get(kp_id, [])
                    if all(closed_state[kp_index_map[prereq_id]] for prereq_id in prerequisites if prereq_id in kp_index_map):
                        closed_state[i] = True
                        changed = True
        
        return closed_state
    
    def _next_closure(self, current_state: List[bool], kp_ids: List[int],
                     closure: Dict[int, List[int]], kp_index_map: Dict[int, int]) -> Optional[List[bool]]:
        """生成下一个闭包状态"""
        n = len(current_state)
        
        # 从右到左查找第一个可以翻转的位置
        for i in range(n - 1, -1, -1):
            if not current_state[i]:
                # 尝试设置这个位置为True
                test_state = current_state[:]
                test_state[i] = True
                
                # 计算闭包
                closed_state = self._compute_closure(test_state, kp_ids, closure, kp_index_map)
                
                # 检查是否是字典序的下一个状态
                if self._is_next_in_order(current_state, closed_state, i):
                    return closed_state
        
        return None
    
    def _is_next_in_order(self, current: List[bool], candidate: List[bool], flip_pos: int) -> bool:
        """检查候选状态是否是字典序的下一个状态"""
        # 检查flip_pos之前的位置是否相同
        for i in range(flip_pos):
            if current[i] != candidate[i]:
                return False
        
        # 检查flip_pos位置是否正确翻转
        return candidate[flip_pos] and not current[flip_pos]
    
    def _save_knowledge_states(self, states: List[List[bool]], kp_ids: List[int]) -> List[KnowledgeState]:
        """保存知识状态到数据库"""
        saved_states = []
        
        for state_vector in states:
            # 计算状态哈希
            state_hash = self._compute_state_hash(state_vector)
            
            # 检查是否已存在
            existing_state = self.db.query(KnowledgeState).filter(
                KnowledgeState.state_hash == state_hash
            ).first()
            
            if existing_state:
                saved_states.append(existing_state)
                continue
            
            # 计算统计信息
            mastery_count = sum(state_vector)
            total_count = len(state_vector)
            mastery_ratio = mastery_count / total_count if total_count > 0 else 0.0
            
            # 确定状态类型
            state_type = self._determine_state_type(state_vector)
            
            # 创建新状态
            new_state = KnowledgeState(
                state_vector=state_vector,
                state_hash=state_hash,
                state_type=state_type.value,
                is_valid=True,
                mastery_count=mastery_count,
                total_count=total_count,
                mastery_ratio=mastery_ratio,
                created_by=1,  # 系统用户
                updated_by=1
            )
            
            self.db.add(new_state)
            self.db.flush()
            saved_states.append(new_state)
        
        self.db.commit()
        return saved_states
    
    def _determine_state_type(self, state_vector: List[bool]) -> StateType:
        """确定状态类型"""
        mastery_count = sum(state_vector)
        total_count = len(state_vector)
        
        if mastery_count == 0:
            return StateType.EMPTY
        elif mastery_count == total_count:
            return StateType.FULL
        else:
            return StateType.INTERMEDIATE
    
    def _compute_state_hash(self, state_vector: List[bool]) -> str:
        """计算状态向量的哈希值"""
        state_str = ''.join('1' if b else '0' for b in state_vector)
        return hashlib.md5(state_str.encode()).hexdigest()
    
    def _generate_state_transitions(self, states: List[KnowledgeState], kp_ids: List[int]) -> List[Dict[str, Any]]:
        """生成状态转移关系"""
        transitions = []
        
        for i, from_state in enumerate(states):
            for j, to_state in enumerate(states):
                if i == j:
                    continue
                
                # 检查是否是有效的学习转移
                transition_info = self._check_learning_transition(
                    from_state.state_vector, to_state.state_vector, kp_ids
                )
                
                if transition_info:
                    transitions.append({
                        'from_state_id': from_state.state_id,
                        'to_state_id': to_state.state_id,
                        'transition_type': TransitionType.LEARNING.value,
                        'probability': 1.0,
                        'trigger_kp_id': transition_info['trigger_kp_id']
                    })
        
        return transitions
    
    def _check_learning_transition(self, from_vector: List[bool], to_vector: List[bool], 
                                 kp_ids: List[int]) -> Optional[Dict[str, Any]]:
        """检查是否是有效的学习转移"""
        # 找出新掌握的知识点
        new_mastered = []
        for i, (from_val, to_val) in enumerate(zip(from_vector, to_vector)):
            if not from_val and to_val:
                new_mastered.append(i)
            elif from_val and not to_val:
                # 不能失去已掌握的知识点
                return None
        
        # 必须恰好掌握一个新知识点
        if len(new_mastered) == 1:
            trigger_kp_index = new_mastered[0]
            return {
                'trigger_kp_id': kp_ids[trigger_kp_index]
            }
        
        return None

    def _save_state_transitions(self, transitions: List[Dict[str, Any]]) -> None:
        """保存状态转移到数据库"""
        for transition_data in transitions:
            # 检查是否已存在
            existing = self.db.query(StateTransition).filter(
                and_(
                    StateTransition.from_state_id == transition_data['from_state_id'],
                    StateTransition.to_state_id == transition_data['to_state_id'],
                    StateTransition.transition_type == transition_data['transition_type']
                )
            ).first()

            if not existing:
                new_transition = StateTransition(
                    from_state_id=transition_data['from_state_id'],
                    to_state_id=transition_data['to_state_id'],
                    transition_type=transition_data['transition_type'],
                    probability=transition_data['probability'],
                    trigger_kp_id=transition_data.get('trigger_kp_id'),
                    created_by=1,
                    updated_by=1
                )
                self.db.add(new_transition)

        self.db.commit()

    def _clear_existing_states(self, kp_ids: List[int]) -> None:
        """清除现有的知识状态"""
        # 删除相关的状态转移
        self.db.query(StateTransition).filter(
            StateTransition.from_state_id.in_(
                self.db.query(KnowledgeState.state_id).filter(
                    KnowledgeState.total_count == len(kp_ids)
                )
            )
        ).delete(synchronize_session=False)

        # 删除知识状态
        self.db.query(KnowledgeState).filter(
            KnowledgeState.total_count == len(kp_ids)
        ).delete(synchronize_session=False)

        self.db.commit()

    def _calculate_stats(self, kp_ids: List[int]) -> Dict[str, Any]:
        """计算知识空间统计信息"""
        total_states = self.db.query(func.count(KnowledgeState.state_id)).filter(
            KnowledgeState.total_count == len(kp_ids)
        ).scalar()

        valid_states = self.db.query(func.count(KnowledgeState.state_id)).filter(
            and_(
                KnowledgeState.total_count == len(kp_ids),
                KnowledgeState.is_valid == True
            )
        ).scalar()

        total_transitions = self.db.query(func.count(StateTransition.transition_id)).filter(
            StateTransition.from_state_id.in_(
                self.db.query(KnowledgeState.state_id).filter(
                    KnowledgeState.total_count == len(kp_ids)
                )
            )
        ).scalar()

        # 查找空状态和满状态
        empty_state = self.db.query(KnowledgeState).filter(
            and_(
                KnowledgeState.total_count == len(kp_ids),
                KnowledgeState.mastery_count == 0
            )
        ).first()

        full_state = self.db.query(KnowledgeState).filter(
            and_(
                KnowledgeState.total_count == len(kp_ids),
                KnowledgeState.mastery_count == len(kp_ids)
            )
        ).first()

        return {
            "total_knowledge_points": len(kp_ids),
            "total_states": total_states or 0,
            "valid_states": valid_states or 0,
            "total_transitions": total_transitions or 0,
            "empty_state_id": empty_state.state_id if empty_state else None,
            "full_state_id": full_state.state_id if full_state else None
        }

    def get_knowledge_states(self, query: KnowledgeStateQuery, skip: int = 0, limit: int = 100) -> List[KnowledgeState]:
        """查询知识状态"""
        db_query = self.db.query(KnowledgeState)

        if query.state_type is not None:
            db_query = db_query.filter(KnowledgeState.state_type == query.state_type)

        if query.is_valid is not None:
            db_query = db_query.filter(KnowledgeState.is_valid == query.is_valid)

        if query.min_mastery_ratio is not None:
            db_query = db_query.filter(KnowledgeState.mastery_ratio >= query.min_mastery_ratio)

        if query.max_mastery_ratio is not None:
            db_query = db_query.filter(KnowledgeState.mastery_ratio <= query.max_mastery_ratio)

        return db_query.offset(skip).limit(limit).all()

    def get_state_transitions(self, query: StateTransitionQuery, skip: int = 0, limit: int = 100) -> List[StateTransition]:
        """查询状态转移"""
        db_query = self.db.query(StateTransition)

        if query.from_state_id is not None:
            db_query = db_query.filter(StateTransition.from_state_id == query.from_state_id)

        if query.to_state_id is not None:
            db_query = db_query.filter(StateTransition.to_state_id == query.to_state_id)

        if query.transition_type is not None:
            db_query = db_query.filter(StateTransition.transition_type == query.transition_type)

        if query.trigger_kp_id is not None:
            db_query = db_query.filter(StateTransition.trigger_kp_id == query.trigger_kp_id)

        if query.min_probability is not None:
            db_query = db_query.filter(StateTransition.probability >= query.min_probability)

        return db_query.offset(skip).limit(limit).all()

    def get_knowledge_space_overview(self, kp_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """获取知识空间概览"""
        if kp_ids is None:
            kp_ids = [kp.kp_id for kp in self.db.query(KnowledgePoint.kp_id).all()]

        # 获取知识点信息
        knowledge_points = self.db.query(KnowledgePoint).filter(
            KnowledgePoint.kp_id.in_(kp_ids)
        ).all()

        # 获取知识状态
        states = self.db.query(KnowledgeState).filter(
            KnowledgeState.total_count == len(kp_ids)
        ).all()

        # 获取状态转移
        transitions = self.db.query(StateTransition).filter(
            StateTransition.from_state_id.in_([s.state_id for s in states])
        ).all()

        # 计算统计信息
        stats = self._calculate_stats(kp_ids)

        return {
            "knowledge_points": [
                {
                    "kp_id": kp.kp_id,
                    "name": kp.name,
                    "code": kp.code,
                    "description": kp.description,
                    "is_leaf": kp.is_leaf
                }
                for kp in knowledge_points
            ],
            "states": states,
            "transitions": transitions,
            "stats": stats
        }

"""
题目管理API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.schemas.question import QuestionCreate, QuestionUpdate, QuestionResponse, QuestionList
from app.models.user import User
from app.services.question_service import QuestionService

router = APIRouter()


@router.get("/", response_model=QuestionList)
async def read_questions(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    subject: Optional[str] = Query(None, description="学科筛选"),
    difficulty: Optional[str] = Query(None, description="难度筛选"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取题目列表
    """
    question_service = QuestionService(db)
    
    filters = {}
    if subject:
        filters['subject'] = subject
    if difficulty:
        filters['difficulty'] = difficulty
    
    questions = question_service.get_multi(
        skip=skip, 
        limit=limit, 
        search=search,
        filters=filters
    )
    total = question_service.count(search=search, filters=filters)
    
    return {
        "items": questions,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=QuestionResponse)
async def create_question(
    *,
    db: Session = Depends(get_db),
    question_in: QuestionCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建新题目
    """
    question_service = QuestionService(db)
    question = question_service.create(question_in, creator_id=current_user.id)
    return question


@router.get("/{question_id}", response_model=QuestionResponse)
async def read_question(
    question_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    根据ID获取题目详情
    """
    question_service = QuestionService(db)
    question = question_service.get(question_id)
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )
    
    return question


@router.put("/{question_id}", response_model=QuestionResponse)
async def update_question(
    *,
    db: Session = Depends(get_db),
    question_id: str,
    question_in: QuestionUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新题目信息
    """
    question_service = QuestionService(db)
    question = question_service.get(question_id)
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )
    
    # 检查权限：只有创建者或管理员可以编辑
    if question.creator_id != current_user.id and current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限编辑此题目"
        )
    
    question = question_service.update(question, question_in, current_user.id)
    return question


@router.delete("/{question_id}")
async def delete_question(
    question_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    删除题目
    """
    question_service = QuestionService(db)
    question = question_service.get(question_id)
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )
    
    # 检查权限：只有创建者或管理员可以删除
    if question.creator_id != current_user.id and current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此题目"
        )
    
    question_service.remove(question_id)
    return {"message": "题目删除成功"}


@router.get("/{question_id}/knowledge-points")
async def get_question_knowledge_points(
    question_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取题目关联的知识点
    """
    question_service = QuestionService(db)
    question = question_service.get(question_id)
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )
    
    knowledge_points = question_service.get_knowledge_points(question_id)
    return {"knowledge_points": knowledge_points}


@router.post("/{question_id}/knowledge-points/{kp_id}")
async def link_question_knowledge_point(
    question_id: str,
    kp_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    关联题目和知识点
    """
    question_service = QuestionService(db)
    
    # 检查题目是否存在
    question = question_service.get(question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )
    
    # 创建关联
    question_service.link_knowledge_point(
        question_id=question_id,
        kp_id=kp_id,
        annotator_id=current_user.id
    )
    
    return {"message": "关联创建成功"}


@router.delete("/{question_id}/knowledge-points/{kp_id}")
async def unlink_question_knowledge_point(
    question_id: str,
    kp_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    取消题目和知识点的关联
    """
    question_service = QuestionService(db)
    
    # 检查题目是否存在
    question = question_service.get(question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )
    
    # 取消关联
    question_service.unlink_knowledge_point(question_id, kp_id)
    
    return {"message": "关联取消成功"}


@router.get("/subjects/")
async def get_subjects(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取所有学科列表
    """
    question_service = QuestionService(db)
    subjects = question_service.get_subjects()
    return {"subjects": subjects}


@router.get("/difficulties/")
async def get_difficulties(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取所有难度级别
    """
    question_service = QuestionService(db)
    difficulties = question_service.get_difficulties()
    return {"difficulties": difficulties}

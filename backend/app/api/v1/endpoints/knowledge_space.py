"""
知识空间相关的API接口
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.services.knowledge_space_service import KnowledgeSpaceService
from app.schemas.knowledge_space import (
    KnowledgeSpaceBuildRequest, KnowledgeSpaceBuildResponse,
    KnowledgeStateQuery, KnowledgeStateResponse,
    StateTransitionQuery, StateTransitionResponse,
    KnowledgeSpaceResponse
)

router = APIRouter()


@router.post("/build", response_model=KnowledgeSpaceBuildResponse)
async def build_knowledge_space(
    *,
    db: Session = Depends(get_db),
    request: KnowledgeSpaceBuildRequest,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    构建知识空间
    """
    # 检查权限（只有管理员和专家可以构建知识空间）
    if current_user.role not in ["admin", "expert"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和专家可以构建知识空间"
        )
    
    knowledge_space_service = KnowledgeSpaceService(db)
    result = knowledge_space_service.build_knowledge_space(request)
    
    return KnowledgeSpaceBuildResponse(**result)


@router.get("/overview", response_model=KnowledgeSpaceResponse)
async def get_knowledge_space_overview(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    kp_ids: Optional[List[int]] = Query(None, description="知识点ID列表")
) -> Any:
    """
    获取知识空间概览
    """
    knowledge_space_service = KnowledgeSpaceService(db)
    overview = knowledge_space_service.get_knowledge_space_overview(kp_ids)
    
    return KnowledgeSpaceResponse(**overview)


@router.get("/states", response_model=List[KnowledgeStateResponse])
async def get_knowledge_states(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    state_type: Optional[int] = Query(None, ge=0, le=3, description="状态类型"),
    is_valid: Optional[bool] = Query(None, description="是否有效状态"),
    min_mastery_ratio: Optional[float] = Query(None, ge=0.0, le=1.0, description="最小掌握比例"),
    max_mastery_ratio: Optional[float] = Query(None, ge=0.0, le=1.0, description="最大掌握比例"),
    knowledge_point_ids: Optional[List[int]] = Query(None, description="包含的知识点ID")
) -> Any:
    """
    查询知识状态
    """
    query = KnowledgeStateQuery(
        state_type=state_type,
        is_valid=is_valid,
        min_mastery_ratio=min_mastery_ratio,
        max_mastery_ratio=max_mastery_ratio,
        knowledge_point_ids=knowledge_point_ids
    )
    
    knowledge_space_service = KnowledgeSpaceService(db)
    states = knowledge_space_service.get_knowledge_states(query, skip, limit)
    
    return states


@router.get("/states/{state_id}", response_model=KnowledgeStateResponse)
async def get_knowledge_state(
    state_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取单个知识状态
    """
    from app.models.knowledge_space import KnowledgeState
    
    state = db.query(KnowledgeState).filter(
        KnowledgeState.state_id == state_id
    ).first()
    
    if not state:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识状态不存在"
        )
    
    return state


@router.get("/transitions", response_model=List[StateTransitionResponse])
async def get_state_transitions(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    from_state_id: Optional[int] = Query(None, description="源状态ID"),
    to_state_id: Optional[int] = Query(None, description="目标状态ID"),
    transition_type: Optional[int] = Query(None, ge=0, le=2, description="转移类型"),
    trigger_kp_id: Optional[int] = Query(None, description="触发知识点ID"),
    min_probability: Optional[float] = Query(None, ge=0.0, le=1.0, description="最小转移概率")
) -> Any:
    """
    查询状态转移
    """
    query = StateTransitionQuery(
        from_state_id=from_state_id,
        to_state_id=to_state_id,
        transition_type=transition_type,
        trigger_kp_id=trigger_kp_id,
        min_probability=min_probability
    )
    
    knowledge_space_service = KnowledgeSpaceService(db)
    transitions = knowledge_space_service.get_state_transitions(query, skip, limit)
    
    return transitions


@router.get("/transitions/{transition_id}", response_model=StateTransitionResponse)
async def get_state_transition(
    transition_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取单个状态转移
    """
    from app.models.knowledge_space import StateTransition
    
    transition = db.query(StateTransition).filter(
        StateTransition.transition_id == transition_id
    ).first()
    
    if not transition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="状态转移不存在"
        )
    
    return transition


@router.get("/states/{state_id}/transitions", response_model=List[StateTransitionResponse])
async def get_state_outgoing_transitions(
    state_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取状态的出边转移
    """
    from app.models.knowledge_space import StateTransition
    
    transitions = db.query(StateTransition).filter(
        StateTransition.from_state_id == state_id
    ).all()
    
    return transitions


@router.get("/states/{state_id}/incoming-transitions", response_model=List[StateTransitionResponse])
async def get_state_incoming_transitions(
    state_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取状态的入边转移
    """
    from app.models.knowledge_space import StateTransition
    
    transitions = db.query(StateTransition).filter(
        StateTransition.to_state_id == state_id
    ).all()
    
    return transitions


@router.delete("/states")
async def clear_knowledge_space(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    kp_ids: Optional[List[int]] = Query(None, description="要清除的知识点ID列表")
) -> Any:
    """
    清除知识空间
    """
    # 检查权限（只有管理员可以清除知识空间）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员可以清除知识空间"
        )
    
    knowledge_space_service = KnowledgeSpaceService(db)
    
    if kp_ids:
        knowledge_space_service._clear_existing_states(kp_ids)
    else:
        # 清除所有知识空间
        from app.models.knowledge_space import KnowledgeState, StateTransition
        db.query(StateTransition).delete()
        db.query(KnowledgeState).delete()
        db.commit()
    
    return {"message": "知识空间已清除"}

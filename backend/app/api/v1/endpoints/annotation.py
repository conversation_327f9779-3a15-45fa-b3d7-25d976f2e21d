"""
数据标注API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.schemas.annotation import (
    AnnotationTaskCreate, AnnotationTaskUpdate, AnnotationTaskResponse, AnnotationTaskList,
    ItemKpMapCreate, ItemKpMapUpdate, ItemKpMapResponse, ItemKpMapList,
    BatchAnnotationRequest, BatchAnnotationResponse, AnnotationStats
)
from app.models.user import User
from app.models.mapping import ItemKpMap
from app.services.annotation_service import AnnotationService

router = APIRouter()


# 题目-知识点映射相关端点
@router.get("/mappings", response_model=ItemKpMapList)
async def read_mappings(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    question_id: Optional[int] = Query(None, description="题目ID筛选"),
    kp_id: Optional[int] = Query(None, description="知识点ID筛选"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取题目-知识点映射列表
    """
    annotation_service = AnnotationService(db)

    mappings = annotation_service.get_mappings(
        question_id=question_id,
        kp_id=kp_id,
        skip=skip,
        limit=limit
    )

    # 计算总数
    total_query = annotation_service.db.query(annotation_service.db.query(ItemKpMap).count())
    if question_id:
        total_query = total_query.filter(ItemKpMap.question_id == question_id)
    if kp_id:
        total_query = total_query.filter(ItemKpMap.kp_id == kp_id)

    total = len(mappings)  # 简化处理

    return {
        "items": mappings,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/mappings", response_model=ItemKpMapResponse)
async def create_mapping(
    *,
    db: Session = Depends(get_db),
    mapping_in: ItemKpMapCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建题目-知识点映射
    """
    annotation_service = AnnotationService(db)

    try:
        mapping = annotation_service.create_mapping(mapping_in, creator_id=current_user.user_id)
        return mapping
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/mappings/{question_id}/{kp_id}", response_model=ItemKpMapResponse)
async def update_mapping(
    question_id: int,
    kp_id: int,
    *,
    db: Session = Depends(get_db),
    mapping_in: ItemKpMapUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新题目-知识点映射
    """
    annotation_service = AnnotationService(db)

    try:
        mapping = annotation_service.update_mapping(
            question_id=question_id,
            kp_id=kp_id,
            mapping_in=mapping_in,
            operator_id=current_user.user_id
        )
        return mapping
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/mappings/{question_id}/{kp_id}")
async def delete_mapping(
    question_id: int,
    kp_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    删除题目-知识点映射
    """
    annotation_service = AnnotationService(db)

    success = annotation_service.remove_mapping(
        question_id=question_id,
        kp_id=kp_id,
        operator_id=current_user.user_id
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="映射关系不存在"
        )

    return {"message": "映射关系删除成功"}


@router.post("/batch-annotate", response_model=BatchAnnotationResponse)
async def batch_annotate(
    *,
    db: Session = Depends(get_db),
    request: BatchAnnotationRequest,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    批量标注题目-知识点映射
    """
    annotation_service = AnnotationService(db)

    result = annotation_service.batch_annotate(request, creator_id=current_user.user_id)
    return result


@router.get("/stats", response_model=AnnotationStats)
async def get_annotation_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取标注统计信息
    """
    annotation_service = AnnotationService(db)
    stats = annotation_service.get_annotation_stats()
    return stats


@router.get("/q-matrix")
async def get_q_matrix(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取Q矩阵
    """
    annotation_service = AnnotationService(db)
    q_matrix = annotation_service.get_q_matrix()
    return q_matrix

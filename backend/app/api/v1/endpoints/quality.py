"""
质量检测相关API端点
"""

from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.services.quality_service import QualityService

router = APIRouter()


@router.get("/check", summary="执行质量检测")
async def run_quality_check(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    执行完整的质量检测
    
    需要管理员或质量管理员权限
    """
    # 检查权限
    if not (current_user.role == "admin" or "quality_manager" in (current_user.permissions or [])):
        raise HTTPException(status_code=403, detail="权限不足")
    
    quality_service = QualityService(db)
    
    # 在后台执行质量检测
    def run_check():
        return quality_service.run_full_quality_check()
    
    # 同步执行（对于演示，实际生产环境可能需要异步）
    results = run_check()
    
    return {
        "message": "质量检测完成",
        "results": results
    }


@router.get("/dashboard", summary="获取质量监控仪表板数据")
async def get_quality_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取质量监控仪表板数据
    
    包括基础统计、质量趋势、用户表现等信息
    """
    quality_service = QualityService(db)
    dashboard_data = quality_service.get_quality_dashboard_data()
    
    return {
        "message": "获取仪表板数据成功",
        "data": dashboard_data
    }


@router.get("/stats", summary="获取质量统计信息")
async def get_quality_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取质量统计信息
    
    包括各类质量指标的统计数据
    """
    quality_service = QualityService(db)
    
    # 执行基础检查获取统计信息
    basic_checks = quality_service._run_basic_checks()
    business_rules = quality_service._run_business_rules_check()
    
    return {
        "message": "获取质量统计成功",
        "data": {
            "basic_checks": basic_checks,
            "business_rules": business_rules,
            "timestamp": quality_service.db.execute("SELECT NOW()").scalar().isoformat()
        }
    }


@router.get("/report", summary="生成质量报告")
async def generate_quality_report(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    生成详细的质量报告
    
    包括完整的质量检测结果和改进建议
    """
    # 检查权限
    if not (current_user.role == "admin" or "quality_manager" in (current_user.permissions or [])):
        raise HTTPException(status_code=403, detail="权限不足")
    
    quality_service = QualityService(db)
    
    # 执行完整质量检测
    full_results = quality_service.run_full_quality_check()
    
    # 生成报告格式
    report = {
        "report_id": f"QR_{int(full_results['timestamp'].replace('-', '').replace(':', '').replace('T', '').replace('.', '')[:14])}",
        "generated_at": full_results['timestamp'],
        "generated_by": current_user.user_id,
        "quality_score": full_results['quality_score'],
        "quality_level": full_results['summary']['quality_level'],
        "total_issues": full_results['summary']['total_issues'],
        "critical_issues": full_results['summary']['critical_issues'],
        "sections": {
            "basic_checks": {
                "title": "基础格式检查",
                "score": full_results['basic_checks']['score'],
                "checks": {k: v for k, v in full_results['basic_checks'].items() if k != 'score'}
            },
            "business_rules": {
                "title": "业务规则验证",
                "score": full_results['business_rules']['score'],
                "checks": {k: v for k, v in full_results['business_rules'].items() if k != 'score'}
            },
            "consistency_checks": {
                "title": "逻辑一致性检查",
                "score": full_results['consistency_checks']['score'],
                "checks": {k: v for k, v in full_results['consistency_checks'].items() if k != 'score'}
            },
            "anomaly_detection": {
                "title": "异常检测",
                "score": full_results['anomaly_detection']['score'],
                "checks": {k: v for k, v in full_results['anomaly_detection'].items() if k != 'score'}
            }
        },
        "recommendations": full_results['summary']['recommendations']
    }
    
    return {
        "message": "质量报告生成成功",
        "report": report
    }


@router.get("/issues", summary="获取质量问题列表")
async def get_quality_issues(
    severity: str = None,
    category: str = None,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取质量问题列表
    
    支持按严重程度和类别筛选
    """
    quality_service = QualityService(db)
    
    # 执行检测获取问题列表
    results = quality_service.run_full_quality_check()
    
    issues = []
    
    # 收集所有问题
    for category_name, category_data in results.items():
        if category_name in ['basic_checks', 'business_rules', 'consistency_checks', 'anomaly_detection']:
            for check_name, check_data in category_data.items():
                if check_name != 'score' and isinstance(check_data, dict) and check_data.get('count', 0) > 0:
                    for item in check_data.get('items', []):
                        issues.append({
                            'category': category_name,
                            'check_type': check_name,
                            'severity': check_data.get('severity', 'unknown'),
                            'description': check_data.get('description', ''),
                            'details': item
                        })
    
    # 筛选
    if severity:
        issues = [issue for issue in issues if issue['severity'] == severity]
    
    if category:
        issues = [issue for issue in issues if issue['category'] == category]
    
    # 限制数量
    issues = issues[:limit]
    
    return {
        "message": "获取质量问题列表成功",
        "total": len(issues),
        "issues": issues
    }


@router.post("/fix/{issue_type}", summary="修复质量问题")
async def fix_quality_issue(
    issue_type: str,
    issue_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    修复特定类型的质量问题
    
    需要管理员权限
    """
    # 检查权限
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    # 这里可以实现自动修复逻辑
    # 目前返回成功消息
    return {
        "message": f"质量问题修复请求已提交: {issue_type}",
        "issue_data": issue_data,
        "status": "pending"
    }

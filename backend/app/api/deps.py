"""
API依赖注入
"""

from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import verify_token
from app.core.permissions import Permission, PermissionChecker
from app.models.user import User, UserRole
from app.services.user_service import UserService

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # 验证token
    user_id = verify_token(token)
    if user_id is None:
        raise credentials_exception
    
    # 获取用户
    user_service = UserService(db)
    user = user_service.get(int(user_id))
    
    if user is None:
        raise credentials_exception
    
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前激活用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    return current_user


def get_current_active_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前激活的超级用户（管理员）"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    return current_user


def get_current_annotator(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前标注员用户"""
    if current_user.role not in [UserRole.ADMIN, UserRole.ANNOTATOR, UserRole.REVIEWER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要标注员权限"
        )
    
    return current_user


def get_current_reviewer(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前审核员用户"""
    if current_user.role not in [UserRole.ADMIN, UserRole.REVIEWER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要审核员权限"
        )
    
    return current_user


def check_permission(required_role: UserRole):
    """检查权限装饰器"""
    def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        # 管理员拥有所有权限
        if current_user.role == UserRole.ADMIN:
            return current_user

        # 检查特定角色权限
        if current_user.role != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要{required_role.value}权限"
            )

        return current_user

    return permission_checker


def require_permission(permission: Permission):
    """要求特定权限的依赖函数"""
    def permission_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not PermissionChecker.has_permission(current_user.role, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要权限: {permission.value}"
            )
        return current_user

    return permission_dependency


def require_any_permission(*permissions: Permission):
    """要求任意权限的依赖函数"""
    def permission_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not PermissionChecker.has_any_permission(current_user.role, list(permissions)):
            permission_names = [p.value for p in permissions]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下任意权限: {', '.join(permission_names)}"
            )
        return current_user

    return permission_dependency


def require_all_permissions(*permissions: Permission):
    """要求所有权限的依赖函数"""
    def permission_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not PermissionChecker.has_all_permissions(current_user.role, list(permissions)):
            permission_names = [p.value for p in permissions]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下所有权限: {', '.join(permission_names)}"
            )
        return current_user

    return permission_dependency

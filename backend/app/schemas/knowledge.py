"""
知识点相关的Pydantic schemas
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime


class KnowledgePointBase(BaseModel):
    """知识点基础字段"""
    name: str = Field(..., description="知识点名称")
    code: str = Field(..., description="知识点编码")
    description: Optional[str] = Field(None, description="知识点描述")
    difficulty_level: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    parent_id: Optional[int] = Field(None, description="父级知识点ID")


class KnowledgePointCreate(KnowledgePointBase):
    """创建知识点的请求模型"""
    pass


class KnowledgePointUpdate(BaseModel):
    """更新知识点的请求模型"""
    name: Optional[str] = Field(None, description="知识点名称")
    code: Optional[str] = Field(None, description="知识点编码")
    description: Optional[str] = Field(None, description="知识点描述")
    difficulty_level: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    parent_id: Optional[int] = Field(None, description="父级知识点ID")


class KnowledgePointResponse(KnowledgePointBase):
    """知识点响应模型"""
    kp_id: int = Field(..., description="知识点ID")
    path: str = Field(..., description="知识点层级路径")
    is_leaf: bool = Field(..., description="是否叶子节点")
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class KnowledgePointList(BaseModel):
    """知识点列表响应模型"""
    items: List[KnowledgePointResponse] = Field(..., description="知识点列表")
    total: int = Field(..., description="总数")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class KnowledgePointTreeNode(BaseModel):
    """知识点树节点"""
    kp_id: int = Field(..., description="知识点ID")
    name: str = Field(..., description="知识点名称")
    code: str = Field(..., description="知识点编码")
    description: Optional[str] = Field(None, description="知识点描述")
    difficulty_level: Optional[int] = Field(None, description="难度等级")
    is_leaf: bool = Field(..., description="是否叶子节点")
    children: List['KnowledgePointTreeNode'] = Field(default_factory=list, description="子节点")
    
    class Config:
        from_attributes = True


# 更新前向引用
KnowledgePointTreeNode.model_rebuild()


class KnowledgePointTree(BaseModel):
    """知识点树响应模型"""
    tree: List[KnowledgePointTreeNode] = Field(..., description="知识点树")


class PrerequisiteRelationBase(BaseModel):
    """先修关系基础字段"""
    pre_kp_id: int = Field(..., description="前置知识点ID")
    post_kp_id: int = Field(..., description="后置知识点ID")
    source: int = Field(0, ge=0, le=2, description="关系来源")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="置信度")


class PrerequisiteRelationCreate(PrerequisiteRelationBase):
    """创建先修关系的请求模型"""
    pass


class PrerequisiteRelationUpdate(BaseModel):
    """更新先修关系的请求模型"""
    source: Optional[int] = Field(None, ge=0, le=2, description="关系来源")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度")


class PrerequisiteRelationResponse(PrerequisiteRelationBase):
    """先修关系响应模型"""
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联的知识点信息
    pre_kp: Optional[KnowledgePointResponse] = Field(None, description="前置知识点")
    post_kp: Optional[KnowledgePointResponse] = Field(None, description="后置知识点")
    
    class Config:
        from_attributes = True


class KnowledgePointImportItem(BaseModel):
    """知识点导入项"""
    name: str = Field(..., description="知识点名称")
    code: str = Field(..., description="知识点编码")
    description: Optional[str] = Field(None, description="知识点描述")
    difficulty_level: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    parent_code: Optional[str] = Field(None, description="父级知识点编码")


class KnowledgePointImportRequest(BaseModel):
    """知识点批量导入请求"""
    knowledge_points: List[KnowledgePointImportItem] = Field(..., description="知识点列表")
    overwrite_existing: bool = Field(False, description="是否覆盖已存在的知识点")


class KnowledgePointImportResponse(BaseModel):
    """知识点批量导入响应"""
    success_count: int = Field(..., description="成功导入数量")
    error_count: int = Field(..., description="失败数量")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    created_knowledge_points: List[int] = Field(default_factory=list, description="创建的知识点ID列表")


class KnowledgePointStats(BaseModel):
    """知识点统计信息"""
    total_count: int = Field(..., description="总数量")
    leaf_count: int = Field(..., description="叶子节点数量")
    max_depth: int = Field(..., description="最大深度")
    avg_children: float = Field(..., description="平均子节点数")


class KnowledgePointPath(BaseModel):
    """知识点路径信息"""
    kp_id: int = Field(..., description="知识点ID")
    path: List[KnowledgePointResponse] = Field(..., description="从根到当前节点的路径")


class KnowledgePointValidation(BaseModel):
    """知识点验证结果"""
    is_valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    warnings: List[str] = Field(default_factory=list, description="警告信息")

"""
知识空间相关的Pydantic模型
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class KnowledgeStateBase(BaseModel):
    """知识状态基础字段"""
    state_vector: List[bool] = Field(..., description="知识状态向量")
    state_type: int = Field(0, ge=0, le=3, description="状态类型")
    is_valid: bool = Field(True, description="是否有效状态")
    description: Optional[str] = Field(None, description="状态描述")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


class KnowledgeStateCreate(KnowledgeStateBase):
    """创建知识状态的请求模型"""
    pass


class KnowledgeStateUpdate(BaseModel):
    """更新知识状态的请求模型"""
    state_type: Optional[int] = Field(None, ge=0, le=3, description="状态类型")
    is_valid: Optional[bool] = Field(None, description="是否有效状态")
    description: Optional[str] = Field(None, description="状态描述")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


class KnowledgeStateResponse(KnowledgeStateBase):
    """知识状态响应模型"""
    state_id: int = Field(..., description="状态ID")
    state_hash: str = Field(..., description="状态哈希值")
    mastery_count: int = Field(..., description="掌握的知识点数量")
    total_count: int = Field(..., description="总知识点数量")
    mastery_ratio: float = Field(..., description="掌握比例")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class StateTransitionBase(BaseModel):
    """状态转移基础字段"""
    from_state_id: int = Field(..., description="源状态ID")
    to_state_id: int = Field(..., description="目标状态ID")
    transition_type: int = Field(0, ge=0, le=2, description="转移类型")
    probability: float = Field(1.0, ge=0.0, le=1.0, description="转移概率")
    trigger_kp_id: Optional[int] = Field(None, description="触发知识点ID")
    trigger_question_id: Optional[int] = Field(None, description="触发题目ID")


class StateTransitionCreate(StateTransitionBase):
    """创建状态转移的请求模型"""
    pass


class StateTransitionUpdate(BaseModel):
    """更新状态转移的请求模型"""
    transition_type: Optional[int] = Field(None, ge=0, le=2, description="转移类型")
    probability: Optional[float] = Field(None, ge=0.0, le=1.0, description="转移概率")
    trigger_kp_id: Optional[int] = Field(None, description="触发知识点ID")
    trigger_question_id: Optional[int] = Field(None, description="触发题目ID")


class StateTransitionResponse(StateTransitionBase):
    """状态转移响应模型"""
    transition_id: int = Field(..., description="转移ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class KnowledgeSpaceStats(BaseModel):
    """知识空间统计信息"""
    total_knowledge_points: int = Field(..., description="总知识点数量")
    total_states: int = Field(..., description="总状态数量")
    valid_states: int = Field(..., description="有效状态数量")
    total_transitions: int = Field(..., description="总转移数量")
    empty_state_id: Optional[int] = Field(None, description="空状态ID")
    full_state_id: Optional[int] = Field(None, description="满状态ID")


class KnowledgeSpaceResponse(BaseModel):
    """知识空间响应模型"""
    knowledge_points: List[Dict[str, Any]] = Field(..., description="知识点列表")
    states: List[KnowledgeStateResponse] = Field(..., description="知识状态列表")
    transitions: List[StateTransitionResponse] = Field(..., description="状态转移列表")
    stats: KnowledgeSpaceStats = Field(..., description="统计信息")


class KnowledgeSpaceBuildRequest(BaseModel):
    """知识空间构建请求"""
    knowledge_point_ids: Optional[List[int]] = Field(None, description="指定的知识点ID列表，为空则使用所有知识点")
    force_rebuild: bool = Field(False, description="是否强制重建")
    include_invalid_states: bool = Field(False, description="是否包含无效状态")


class KnowledgeSpaceBuildResponse(BaseModel):
    """知识空间构建响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    stats: Optional[KnowledgeSpaceStats] = Field(None, description="构建后的统计信息")
    build_time: float = Field(..., description="构建耗时（秒）")


class KnowledgeStateQuery(BaseModel):
    """知识状态查询参数"""
    state_type: Optional[int] = Field(None, description="状态类型")
    is_valid: Optional[bool] = Field(None, description="是否有效")
    min_mastery_ratio: Optional[float] = Field(None, description="最小掌握比例")
    max_mastery_ratio: Optional[float] = Field(None, description="最大掌握比例")
    knowledge_point_ids: Optional[List[int]] = Field(None, description="包含的知识点ID")


class StateTransitionQuery(BaseModel):
    """状态转移查询参数"""
    from_state_id: Optional[int] = Field(None, description="源状态ID")
    to_state_id: Optional[int] = Field(None, description="目标状态ID")
    transition_type: Optional[int] = Field(None, description="转移类型")
    trigger_kp_id: Optional[int] = Field(None, description="触发知识点ID")
    min_probability: Optional[float] = Field(None, description="最小转移概率")

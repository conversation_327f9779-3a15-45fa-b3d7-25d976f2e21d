"""
版本管理模块数据模式
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field

from app.models.version import ChangeType


class VersionBase(BaseModel):
    """版本基础模式"""
    version_tag: Optional[str] = Field(None, description="版本标签")
    change_description: Optional[str] = Field(None, description="变更描述")


class QuestionVersionCreate(VersionBase):
    """创建题目版本请求模式"""
    question_id: int = Field(..., description="题目ID")
    change_type: ChangeType = Field(..., description="变更类型")
    snapshot_data: Dict[str, Any] = Field(..., description="题目快照数据")
    diff_data: Optional[Dict[str, Any]] = Field(None, description="与前一版本的差异")


class QuestionVersionUpdate(VersionBase):
    """更新题目版本请求模式"""
    is_current: Optional[bool] = Field(None, description="是否为当前版本")
    is_published: Optional[bool] = Field(None, description="是否已发布")


class QuestionVersionResponse(VersionBase):
    """题目版本响应模式"""
    version_id: int = Field(..., description="版本ID")
    question_id: int = Field(..., description="题目ID")
    version_number: int = Field(..., description="版本号")
    change_type: ChangeType = Field(..., description="变更类型")
    snapshot_data: Dict[str, Any] = Field(..., description="题目快照数据")
    diff_data: Optional[Dict[str, Any]] = Field(None, description="与前一版本的差异")
    is_current: bool = Field(..., description="是否为当前版本")
    is_published: bool = Field(..., description="是否已发布")
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class KpVersionCreate(VersionBase):
    """创建知识点版本请求模式"""
    kp_id: int = Field(..., description="知识点ID")
    change_type: ChangeType = Field(..., description="变更类型")
    snapshot_data: Dict[str, Any] = Field(..., description="知识点快照数据")
    diff_data: Optional[Dict[str, Any]] = Field(None, description="与前一版本的差异")


class KpVersionUpdate(VersionBase):
    """更新知识点版本请求模式"""
    is_current: Optional[bool] = Field(None, description="是否为当前版本")
    is_published: Optional[bool] = Field(None, description="是否已发布")


class KpVersionResponse(VersionBase):
    """知识点版本响应模式"""
    version_id: int = Field(..., description="版本ID")
    kp_id: int = Field(..., description="知识点ID")
    version_number: int = Field(..., description="版本号")
    change_type: ChangeType = Field(..., description="变更类型")
    snapshot_data: Dict[str, Any] = Field(..., description="知识点快照数据")
    diff_data: Optional[Dict[str, Any]] = Field(None, description="与前一版本的差异")
    is_current: bool = Field(..., description="是否为当前版本")
    is_published: bool = Field(..., description="是否已发布")
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class VersionListResponse(BaseModel):
    """版本列表响应模式"""
    items: List[Union[QuestionVersionResponse, KpVersionResponse]] = Field(..., description="版本列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class VersionCompareRequest(BaseModel):
    """版本比较请求模式"""
    source_version_id: int = Field(..., description="源版本ID")
    target_version_id: int = Field(..., description="目标版本ID")


class VersionCompareResponse(BaseModel):
    """版本比较响应模式"""
    source_version: Union[QuestionVersionResponse, KpVersionResponse] = Field(..., description="源版本")
    target_version: Union[QuestionVersionResponse, KpVersionResponse] = Field(..., description="目标版本")
    differences: Dict[str, Any] = Field(..., description="差异详情")


class VersionRollbackRequest(BaseModel):
    """版本回滚请求模式"""
    target_version_id: int = Field(..., description="目标版本ID")
    rollback_reason: str = Field(..., description="回滚原因")


class VersionRollbackResponse(BaseModel):
    """版本回滚响应模式"""
    success: bool = Field(..., description="是否成功")
    new_version: Union[QuestionVersionResponse, KpVersionResponse] = Field(..., description="新版本信息")
    message: str = Field(..., description="操作消息")


class VersionPublishRequest(BaseModel):
    """版本发布请求模式"""
    version_id: int = Field(..., description="版本ID")
    publish_note: Optional[str] = Field(None, description="发布说明")


class VersionPublishResponse(BaseModel):
    """版本发布响应模式"""
    success: bool = Field(..., description="是否成功")
    version: Union[QuestionVersionResponse, KpVersionResponse] = Field(..., description="版本信息")
    message: str = Field(..., description="操作消息")


class VersionStatsResponse(BaseModel):
    """版本统计响应模式"""
    total_versions: int = Field(..., description="总版本数")
    published_versions: int = Field(..., description="已发布版本数")
    draft_versions: int = Field(..., description="草稿版本数")
    recent_changes: int = Field(..., description="最近变更数")
    change_type_stats: Dict[str, int] = Field(..., description="变更类型统计")

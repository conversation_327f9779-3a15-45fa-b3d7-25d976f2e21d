"""
题目相关的Pydantic schemas
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime


class QuestionContent(BaseModel):
    """题目内容结构"""
    stem: str = Field(..., description="题干")
    options: Optional[List[str]] = Field(None, description="选项列表")
    passages: Optional[List[str]] = Field(None, description="阅读材料")
    media_urls: Optional[List[str]] = Field(None, description="媒体文件URL")


class QuestionBase(BaseModel):
    """题目基础字段"""
    content: QuestionContent = Field(..., description="题目内容")
    q_type: int = Field(..., ge=0, le=10, description="题目类型")
    difficulty_lvl: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    answer_key: Optional[Dict[str, Any]] = Field(None, description="正确答案")
    analysis: Optional[str] = Field(None, description="题目解析")
    source: Optional[str] = Field(None, description="题目来源")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")


class QuestionCreate(QuestionBase):
    """创建题目的请求模型"""
    pass


class QuestionUpdate(BaseModel):
    """更新题目的请求模型"""
    content: Optional[QuestionContent] = Field(None, description="题目内容")
    q_type: Optional[int] = Field(None, ge=0, le=10, description="题目类型")
    difficulty_lvl: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    answer_key: Optional[Dict[str, Any]] = Field(None, description="正确答案")
    analysis: Optional[str] = Field(None, description="题目解析")
    source: Optional[str] = Field(None, description="题目来源")
    tags: Optional[List[str]] = Field(None, description="标签")
    is_active: Optional[bool] = Field(None, description="是否激活")


class QuestionResponse(QuestionBase):
    """题目响应模型"""
    question_id: int = Field(..., description="题目ID")
    irt_ready: bool = Field(..., description="IRT是否已校准")
    is_active: bool = Field(..., description="是否激活")
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class QuestionList(BaseModel):
    """题目列表响应模型"""
    items: List[QuestionResponse] = Field(..., description="题目列表")
    total: int = Field(..., description="总数")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class QuestionAssetBase(BaseModel):
    """题目资源基础字段"""
    uri: str = Field(..., description="资源URI")
    media_type: str = Field(..., description="媒体类型")
    alt_text: Optional[str] = Field(None, description="替代文本")


class QuestionAssetCreate(QuestionAssetBase):
    """创建题目资源的请求模型"""
    question_id: int = Field(..., description="题目ID")


class QuestionAssetResponse(QuestionAssetBase):
    """题目资源响应模型"""
    asset_id: int = Field(..., description="资源ID")
    question_id: int = Field(..., description="题目ID")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class ItemParamBase(BaseModel):
    """IRT参数基础字段"""
    a: Optional[float] = Field(None, description="区分度参数")
    b: Optional[float] = Field(None, description="难度参数")
    c: Optional[float] = Field(None, description="猜测参数")


class ItemParamCreate(ItemParamBase):
    """创建IRT参数的请求模型"""
    question_id: int = Field(..., description="题目ID")


class ItemParamUpdate(ItemParamBase):
    """更新IRT参数的请求模型"""
    pass


class ItemParamResponse(ItemParamBase):
    """IRT参数响应模型"""
    question_id: int = Field(..., description="题目ID")
    last_calibrated: Optional[datetime] = Field(None, description="最后校准时间")
    calibration_sample_size: Optional[int] = Field(None, description="校准样本量")
    model_fit: Optional[float] = Field(None, description="模型拟合度")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class QuestionImportItem(BaseModel):
    """题目导入项"""
    content: QuestionContent = Field(..., description="题目内容")
    q_type: int = Field(..., ge=0, le=10, description="题目类型")
    difficulty_lvl: Optional[int] = Field(None, ge=1, le=5, description="难度等级")
    answer_key: Optional[Dict[str, Any]] = Field(None, description="正确答案")
    analysis: Optional[str] = Field(None, description="题目解析")
    source: Optional[str] = Field(None, description="题目来源")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")


class QuestionImportRequest(BaseModel):
    """题目批量导入请求"""
    questions: List[QuestionImportItem] = Field(..., description="题目列表")
    overwrite_existing: bool = Field(False, description="是否覆盖已存在的题目")


class QuestionImportResponse(BaseModel):
    """题目批量导入响应"""
    success_count: int = Field(..., description="成功导入数量")
    error_count: int = Field(..., description="失败数量")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    created_questions: List[int] = Field(default_factory=list, description="创建的题目ID列表")

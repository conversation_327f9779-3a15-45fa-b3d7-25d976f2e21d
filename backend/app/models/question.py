"""
题库管理模块数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List

from sqlalchemy import (
    Column, String, Boolean, Integer, SmallInteger, Text, 
    Index, CheckConstraint, ForeignKey, REAL, BigInteger,
    DateTime, ARRAY
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class QuestionType(int, Enum):
    """题目类型枚举"""
    SINGLE_CHOICE = 0      # 单选题
    MULTIPLE_CHOICE = 1    # 多选题
    TRUE_FALSE = 2         # 判断题
    FILL_BLANK = 3         # 填空题
    SHORT_ANSWER = 4       # 简答题
    ESSAY = 5              # 论述题
    MATCHING = 6           # 匹配题
    ORDERING = 7           # 排序题
    CALCULATION = 8        # 计算题
    PROGRAMMING = 9        # 编程题
    COMPOSITE = 10         # 复合题


class Question(Base, TimestampMixin, UserTrackingMixin):
    """题目主表"""
    
    __tablename__ = "questions"
    
    # 主键
    question_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="题目ID"
    )
    
    # 题目内容
    content = Column(
        JSONB,
        nullable=False,
        comment="题目内容(JSON格式)"
    )
    
    # 题目类型
    q_type = Column(
        SmallInteger,
        nullable=False,
        comment="题目类型"
    )
    
    # 难度等级
    difficulty_lvl = Column(
        SmallInteger,
        nullable=True,
        comment="难度等级(1-5)"
    )
    
    # IRT相关
    irt_ready = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="IRT是否已校准"
    )
    
    # 答案和解析
    answer_key = Column(
        JSONB,
        nullable=True,
        comment="正确答案(JSON格式)"
    )
    
    analysis = Column(
        Text,
        nullable=True,
        comment="题目解析"
    )
    
    # 元数据
    source = Column(
        Text,
        nullable=True,
        comment="题目来源"
    )
    
    tags = Column(
        ARRAY(Text),
        nullable=True,
        comment="标签数组"
    )
    
    # 状态
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否启用"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            q_type.in_([t.value for t in QuestionType]),
            name="ck_questions_q_type"
        ),
        CheckConstraint(
            "difficulty_lvl IS NULL OR (difficulty_lvl >= 1 AND difficulty_lvl <= 5)",
            name="ck_questions_difficulty_lvl"
        ),
        Index("idx_questions_q_type", q_type),
        Index("idx_questions_difficulty_lvl", difficulty_lvl),
        Index("idx_questions_irt_ready", irt_ready),
        Index("idx_questions_is_active", is_active),
        Index("idx_questions_created_by", "created_by"),
        Index("idx_questions_source", source),
        Index("idx_questions_tags", tags, postgresql_using="gin"),
        Index("idx_questions_content", content, postgresql_using="gin"),
        {"comment": "题目主表"}
    )
    
    # 关系
    assets = relationship("QuestionAsset", back_populates="question", cascade="all, delete-orphan")
    irt_params = relationship("ItemParam", back_populates="question", uselist=False, cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Question(id={self.question_id}, type={self.q_type}, active={self.is_active})>"


class QuestionAsset(Base, TimestampMixin):
    """题目媒资表"""
    
    __tablename__ = "question_assets"
    
    # 主键
    asset_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="媒资ID"
    )
    
    # 外键
    question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        nullable=False,
        comment="题目ID"
    )
    
    # 媒资信息
    uri = Column(
        Text,
        nullable=False,
        comment="媒资URI"
    )
    
    media_type = Column(
        String(50),
        nullable=False,
        comment="媒体类型"
    )
    
    alt_text = Column(
        Text,
        nullable=True,
        comment="替代文本"
    )
    
    file_size = Column(
        BigInteger,
        nullable=True,
        comment="文件大小(字节)"
    )
    
    # 约束和索引
    __table_args__ = (
        Index("idx_question_assets_question_id", question_id),
        Index("idx_question_assets_media_type", media_type),
        {"comment": "题目媒资表"}
    )
    
    # 关系
    question = relationship("Question", back_populates="assets")
    
    def __repr__(self) -> str:
        return f"<QuestionAsset(id={self.asset_id}, question_id={self.question_id}, type='{self.media_type}')>"


class ItemParam(Base, TimestampMixin):
    """IRT参数表"""
    
    __tablename__ = "item_param"
    
    # 主键(外键)
    question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        primary_key=True,
        comment="题目ID"
    )
    
    # IRT参数
    a = Column(
        REAL,
        nullable=True,
        comment="区分度参数"
    )
    
    b = Column(
        REAL,
        nullable=True,
        comment="难度参数"
    )
    
    c = Column(
        REAL,
        nullable=True,
        comment="猜测参数"
    )
    
    # 校准信息
    last_calibrated = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后校准时间"
    )
    
    calibration_sample_size = Column(
        Integer,
        nullable=True,
        comment="校准样本量"
    )
    
    model_fit = Column(
        REAL,
        nullable=True,
        comment="模型拟合度"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "a IS NULL OR a > 0",
            name="ck_item_param_a_positive"
        ),
        CheckConstraint(
            "c IS NULL OR (c >= 0 AND c <= 1)",
            name="ck_item_param_c_range"
        ),
        Index("idx_item_param_last_calibrated", last_calibrated),
        {"comment": "IRT参数表"}
    )
    
    # 关系
    question = relationship("Question", back_populates="irt_params")
    
    def __repr__(self) -> str:
        return f"<ItemParam(question_id={self.question_id}, a={self.a}, b={self.b}, c={self.c})>"

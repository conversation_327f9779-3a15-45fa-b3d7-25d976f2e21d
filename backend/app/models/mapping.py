"""
关联映射模块数据模型
"""

from enum import Enum
from typing import Optional

from sqlalchemy import (
    Column, Boolean, Integer, SmallInteger, Text, 
    Index, CheckConstraint, ForeignKey, REAL, BigInteger
)
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class RelationType(int, Enum):
    """题目关系类型枚举"""
    SIMILAR = 0         # 相似题目
    VARIANT = 1         # 变式题目
    PREREQUISITE = 2    # 前置题目
    FOLLOW_UP = 3       # 后续题目
    COMPOSITE = 4       # 复合关系


class ItemKpMap(Base, TimestampMixin, UserTrackingMixin):
    """题目-知识点映射表(Q-矩阵)"""
    
    __tablename__ = "item_kp_map"
    
    # 复合主键
    question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        primary_key=True,
        comment="题目ID"
    )
    
    kp_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        primary_key=True,
        comment="知识点ID"
    )
    
    # 映射属性
    is_required = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否必需掌握"
    )
    
    weight = Column(
        REAL,
        nullable=False,
        default=1.0,
        comment="权重"
    )
    
    confidence = Column(
        REAL,
        nullable=False,
        default=1.0,
        comment="置信度"
    )
    
    source = Column(
        SmallInteger,
        nullable=False,
        default=0,
        comment="标注来源(0=专家,1=算法,2=导入)"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "weight >= 0 AND weight <= 1",
            name="ck_item_kp_map_weight"
        ),
        CheckConstraint(
            "confidence >= 0 AND confidence <= 1",
            name="ck_item_kp_map_confidence"
        ),
        CheckConstraint(
            "source IN (0, 1, 2)",
            name="ck_item_kp_map_source"
        ),
        Index("idx_item_kp_map_question_id", question_id),
        Index("idx_item_kp_map_kp_id", kp_id),
        Index("idx_item_kp_map_is_required", is_required),
        Index("idx_item_kp_map_source", source),
        {"comment": "题目-知识点映射表(Q-矩阵)"}
    )
    
    # 关系
    question = relationship("Question")
    knowledge_point = relationship("KnowledgePoint")
    
    def __repr__(self) -> str:
        return f"<ItemKpMap(question_id={self.question_id}, kp_id={self.kp_id}, required={self.is_required})>"


class QuestionRelation(Base, TimestampMixin, UserTrackingMixin):
    """题目关系表"""
    
    __tablename__ = "question_relation"
    
    # 主键
    relation_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="关系ID"
    )
    
    # 外键
    source_question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        nullable=False,
        comment="源题目ID"
    )
    
    target_question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        nullable=False,
        comment="目标题目ID"
    )
    
    # 关系属性
    relation_type = Column(
        SmallInteger,
        nullable=False,
        comment="关系类型"
    )
    
    strength = Column(
        REAL,
        nullable=False,
        default=1.0,
        comment="关系强度"
    )
    
    confidence = Column(
        REAL,
        nullable=False,
        default=1.0,
        comment="置信度"
    )
    
    source = Column(
        SmallInteger,
        nullable=False,
        default=0,
        comment="标注来源(0=专家,1=算法,2=导入)"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="关系描述"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "source_question_id != target_question_id",
            name="ck_question_relation_no_self_reference"
        ),
        CheckConstraint(
            relation_type.in_([t.value for t in RelationType]),
            name="ck_question_relation_type"
        ),
        CheckConstraint(
            "strength >= 0 AND strength <= 1",
            name="ck_question_relation_strength"
        ),
        CheckConstraint(
            "confidence >= 0 AND confidence <= 1",
            name="ck_question_relation_confidence"
        ),
        CheckConstraint(
            "source IN (0, 1, 2)",
            name="ck_question_relation_source"
        ),
        Index("idx_question_relation_source_id", source_question_id),
        Index("idx_question_relation_target_id", target_question_id),
        Index("idx_question_relation_type", relation_type),
        Index("idx_question_relation_source", source),
        Index("idx_question_relation_composite", source_question_id, target_question_id, relation_type),
        {"comment": "题目关系表"}
    )
    
    # 关系
    source_question = relationship(
        "Question",
        foreign_keys=[source_question_id]
    )
    
    target_question = relationship(
        "Question",
        foreign_keys=[target_question_id]
    )
    
    def __repr__(self) -> str:
        return f"<QuestionRelation(id={self.relation_id}, source={self.source_question_id}, target={self.target_question_id}, type={self.relation_type})>"

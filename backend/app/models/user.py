"""
用户与权限模块数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import (
    String, Boolean, DateTime, Integer, BigInteger,
    Text, Index, CheckConstraint, func
)
from sqlalchemy.orm import relationship, Mapped, mapped_column

from .base import Base, TimestampMixin


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"           # 系统管理员
    ANNOTATOR = "annotator"   # 标注员
    REVIEWER = "reviewer"     # 审核员
    VIEWER = "viewer"         # 查看者
    ENGINE = "engine"         # 引擎用户


class User(Base, TimestampMixin):
    """系统用户表"""
    
    __tablename__ = "users"
    
    # 主键
    user_id: Mapped[int] = mapped_column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="用户ID"
    )

    # 基本信息
    username: Mapped[str] = mapped_column(
        Text,
        unique=True,
        nullable=False,
        comment="用户名"
    )

    password_hash: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="密码哈希"
    )

    role: Mapped[str] = mapped_column(
        String(16),
        nullable=False,
        comment="用户角色"
    )

    full_name: Mapped[str] = mapped_column(
        Text,
        nullable=True,
        comment="真实姓名"
    )

    email: Mapped[str] = mapped_column(
        Text,
        unique=True,
        nullable=True,
        comment="邮箱地址"
    )

    # 状态信息
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )

    last_login: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后登录时间"
    )

    login_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="登录次数"
    )
    
    # 约束
    __table_args__ = (
        CheckConstraint(
            role.in_([r.value for r in UserRole]),
            name="ck_users_role"
        ),
        Index("idx_users_username", username),
        Index("idx_users_email", email),
        Index("idx_users_role", role),
        Index("idx_users_is_active", is_active),
        {"comment": "系统用户表"}
    )
    
    # 关系
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<User(id={self.user_id}, username='{self.username}', role='{self.role}')>"


class UserSession(Base, TimestampMixin):
    """用户会话表"""
    
    __tablename__ = "user_sessions"
    
    # 主键
    session_id: Mapped[str] = mapped_column(
        String(128),
        primary_key=True,
        comment="会话ID"
    )

    # 外键
    user_id: Mapped[int] = mapped_column(
        BigInteger,
        nullable=False,
        comment="用户ID"
    )

    # 会话信息
    ip_address: Mapped[str] = mapped_column(
        String(45),
        nullable=True,
        comment="IP地址"
    )

    user_agent: Mapped[str] = mapped_column(
        Text,
        nullable=True,
        comment="用户代理"
    )

    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        comment="过期时间"
    )

    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # 约束和索引
    __table_args__ = (
        Index("idx_user_sessions_user_id", user_id),
        Index("idx_user_sessions_expires_at", expires_at),
        Index("idx_user_sessions_is_active", is_active),
        {"comment": "用户会话表"}
    )
    
    # 关系
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self) -> str:
        return f"<UserSession(id='{self.session_id}', user_id={self.user_id})>"

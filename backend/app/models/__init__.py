"""
数据模型包
包含所有SQLAlchemy数据模型定义
"""

from .base import Base, TimestampMixin, UserTrackingMixin
from .user import User, UserSession
from .knowledge import KnowledgePoint, PrerequisiteRelation
from .question import Question, QuestionAsset, ItemParam
from .annotation import AnnotationTask, AnnotationLog
from .mapping import ItemKpMap, QuestionRelation
from .knowledge_space import KnowledgeState, StateTransition
from .version import QuestionVersion, KpVersion
from .model_param import SkillParam

__all__ = [
    # 基础类
    "Base",
    "TimestampMixin", 
    "UserTrackingMixin",
    
    # 用户与权限模块
    "User",
    "UserSession",
    
    # 知识本体模块
    "KnowledgePoint",
    "PrerequisiteRelation",
    
    # 题库管理模块
    "Question",
    "QuestionAsset", 
    "ItemParam",
    
    # 标注工作流模块
    "AnnotationTask",
    "AnnotationLog",
    
    # 关联映射模块
    "ItemKpMap",
    "QuestionRelation",
    
    # 知识空间模块
    "KnowledgeState",
    "StateTransition",
    
    # 版本管理模块
    "QuestionVersion",
    "KpVersion",
    
    # 模型参数模块
    "SkillParam",
]

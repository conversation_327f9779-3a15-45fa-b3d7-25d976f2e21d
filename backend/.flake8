[flake8]
# 最大行长度
max-line-length = 88

# 忽略的错误代码
extend-ignore = 
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by black)
    E501,
    # F401: imported but unused (handled by isort)
    F401,
    # E402: module level import not at top of file
    E402

# 排除的目录
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    migrations,
    alembic,
    build,
    dist,
    .eggs,
    *.egg-info,
    .pytest_cache,
    .mypy_cache,
    .coverage,
    htmlcov,
    .tox

# 每个文件的最大复杂度
max-complexity = 10

# 导入顺序检查
import-order-style = google
application-import-names = app

# 文档字符串检查
docstring-convention = google

# 变量命名检查
inline-quotes = double

# 统计信息
statistics = True
count = True

# 显示源代码
show-source = True

# 基准测试
benchmark = True

# 插件配置
per-file-ignores =
    # 测试文件可以有更长的行
    tests/*:E501,F401,F811
    # __init__.py 文件可以有未使用的导入
    __init__.py:F401
    # 配置文件可以有更复杂的结构
    */config.py:C901
    # 迁移文件忽略大部分检查
    migrations/*:E501,F401,F811,E402

# 选择要检查的错误类型
select = 
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity
    N,  # pep8-naming
    B,  # flake8-bugbear
    A,  # flake8-builtins
    COM, # flake8-commas
    T20, # flake8-print

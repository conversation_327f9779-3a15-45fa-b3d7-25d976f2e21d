#!/usr/bin/env python3
"""
知识空间构建功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.knowledge import KnowledgePoint, PrerequisiteRelation
from app.services.knowledge_space_service import KnowledgeSpaceService
from app.schemas.knowledge_space import KnowledgeSpaceBuildRequest


def create_test_knowledge_points(db: Session):
    """创建测试知识点"""
    print("创建测试知识点...")
    
    # 清除现有数据
    db.query(PrerequisiteRelation).delete()
    db.query(KnowledgePoint).delete()
    db.commit()
    
    # 创建知识点
    kps = [
        KnowledgePoint(
            name="基础数学",
            code="MATH_BASIC",
            description="基础数学概念",
            is_leaf=True,
            created_by=1,
            updated_by=1
        ),
        KnowledgePoint(
            name="代数",
            code="ALGEBRA",
            description="代数运算",
            is_leaf=True,
            created_by=1,
            updated_by=1
        ),
        KnowledgePoint(
            name="几何",
            code="GEOMETRY",
            description="几何概念",
            is_leaf=True,
            created_by=1,
            updated_by=1
        ),
        KnowledgePoint(
            name="微积分",
            code="CALCULUS",
            description="微积分",
            is_leaf=True,
            created_by=1,
            updated_by=1
        )
    ]
    
    for kp in kps:
        db.add(kp)
    
    db.commit()
    
    # 刷新以获取ID
    for kp in kps:
        db.refresh(kp)
    
    print(f"创建了 {len(kps)} 个知识点")
    return kps


def create_test_prerequisites(db: Session, kps):
    """创建测试先修关系"""
    print("创建测试先修关系...")
    
    # 建立先修关系：基础数学 -> 代数 -> 微积分
    #                基础数学 -> 几何
    prerequisites = [
        PrerequisiteRelation(
            pre_kp_id=kps[0].kp_id,  # 基础数学
            post_kp_id=kps[1].kp_id,  # 代数
            created_by=1,
            updated_by=1
        ),
        PrerequisiteRelation(
            pre_kp_id=kps[0].kp_id,  # 基础数学
            post_kp_id=kps[2].kp_id,  # 几何
            created_by=1,
            updated_by=1
        ),
        PrerequisiteRelation(
            pre_kp_id=kps[1].kp_id,  # 代数
            post_kp_id=kps[3].kp_id,  # 微积分
            created_by=1,
            updated_by=1
        )
    ]
    
    for prereq in prerequisites:
        db.add(prereq)
    
    db.commit()
    print(f"创建了 {len(prerequisites)} 个先修关系")


def test_knowledge_space_building(db: Session):
    """测试知识空间构建"""
    print("\n开始测试知识空间构建...")
    
    # 创建测试数据
    kps = create_test_knowledge_points(db)
    create_test_prerequisites(db, kps)
    
    # 创建知识空间服务
    ks_service = KnowledgeSpaceService(db)
    
    # 构建知识空间
    request = KnowledgeSpaceBuildRequest(
        knowledge_point_ids=[kp.kp_id for kp in kps],
        force_rebuild=True,
        include_invalid_states=False
    )
    
    print("构建知识空间...")
    result = ks_service.build_knowledge_space(request)
    
    print(f"构建结果: {result}")
    
    if result['success']:
        print("✅ 知识空间构建成功!")
        
        # 获取概览
        overview = ks_service.get_knowledge_space_overview([kp.kp_id for kp in kps])
        
        print(f"\n知识空间统计:")
        print(f"- 知识点数量: {overview['stats']['total_knowledge_points']}")
        print(f"- 知识状态数量: {overview['stats']['total_states']}")
        print(f"- 有效状态数量: {overview['stats']['valid_states']}")
        print(f"- 状态转移数量: {overview['stats']['total_transitions']}")
        print(f"- 空状态ID: {overview['stats']['empty_state_id']}")
        print(f"- 满状态ID: {overview['stats']['full_state_id']}")
        
        # 显示一些知识状态
        print(f"\n前5个知识状态:")
        for i, state in enumerate(overview['states'][:5]):
            vector_str = ''.join('1' if v else '0' for v in state.state_vector)
            print(f"  状态#{state.state_id}: {vector_str} (掌握率: {state.mastery_ratio:.1%})")
        
        # 显示一些状态转移
        print(f"\n前5个状态转移:")
        for i, transition in enumerate(overview['transitions'][:5]):
            print(f"  转移#{transition.transition_id}: #{transition.from_state_id} -> #{transition.to_state_id} (触发知识点: {transition.trigger_kp_id})")
        
    else:
        print(f"❌ 知识空间构建失败: {result['message']}")


def main():
    """主函数"""
    print("知识空间构建功能测试")
    print("=" * 50)
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        test_knowledge_space_building(db)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()

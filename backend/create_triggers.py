"""
创建数据库触发器和函数
"""

from sqlalchemy import create_engine, text
from app.core.config import settings

# 创建同步引擎
database_url = str(settings.DATABASE_URL)
if "sslmode" not in database_url:
    database_url += "?sslmode=require"
engine = create_engine(database_url)

def create_triggers_and_functions():
    """创建触发器和函数"""
    
    # 更新时间戳触发器函数
    update_timestamp_function = """
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    """
    
    # 知识点路径维护函数
    update_kp_path_function = """
    CREATE OR REPLACE FUNCTION update_kp_path()
    RETURNS TRIGGER AS $$
    BEGIN
        -- 如果是根节点
        IF NEW.parent_id IS NULL THEN
            NEW.path = NEW.kp_id::text;
        ELSE
            -- 获取父节点路径并构建新路径
            SELECT path || '.' || NEW.kp_id::text
            INTO NEW.path
            FROM knowledge_points
            WHERE kp_id = NEW.parent_id;
            
            -- 检查是否会形成环路
            IF NEW.path ~ ('(^|\\.)' || NEW.kp_id::text || '(\\.|$)') THEN
                RAISE EXCEPTION '知识点层级不能形成环路';
            END IF;
        END IF;
        
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    """
    
    # 知识点叶子节点状态维护函数
    update_kp_leaf_status_function = """
    CREATE OR REPLACE FUNCTION update_kp_leaf_status()
    RETURNS TRIGGER AS $$
    BEGIN
        -- 更新父节点的叶子状态
        IF TG_OP = 'INSERT' AND NEW.parent_id IS NOT NULL THEN
            UPDATE knowledge_points 
            SET is_leaf = FALSE 
            WHERE kp_id = NEW.parent_id;
        END IF;
        
        -- 如果删除节点，检查父节点是否应该变为叶子节点
        IF TG_OP = 'DELETE' AND OLD.parent_id IS NOT NULL THEN
            UPDATE knowledge_points 
            SET is_leaf = TRUE 
            WHERE kp_id = OLD.parent_id 
            AND NOT EXISTS (
                SELECT 1 FROM knowledge_points 
                WHERE parent_id = OLD.parent_id AND kp_id != OLD.kp_id
            );
        END IF;
        
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            RETURN NEW;
        END IF;
    END;
    $$ language 'plpgsql';
    """
    
    # 知识状态掌握比例计算函数
    update_mastery_ratio_function = """
    CREATE OR REPLACE FUNCTION update_mastery_ratio()
    RETURNS TRIGGER AS $$
    BEGIN
        -- 计算掌握的知识点数量
        NEW.mastery_count = (
            SELECT COUNT(*)
            FROM unnest(NEW.state_vector) AS val
            WHERE val = TRUE
        );
        
        -- 计算掌握比例
        IF NEW.total_count > 0 THEN
            NEW.mastery_ratio = NEW.mastery_count::REAL / NEW.total_count::REAL;
        ELSE
            NEW.mastery_ratio = 0.0;
        END IF;
        
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    """
    
    # 创建触发器的SQL
    create_triggers_sql = """
    -- 为所有表创建更新时间戳触发器
    DROP TRIGGER IF EXISTS update_users_updated_at ON users;
    CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON users
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_knowledge_points_updated_at ON knowledge_points;
    CREATE TRIGGER update_knowledge_points_updated_at
        BEFORE UPDATE ON knowledge_points
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_questions_updated_at ON questions;
    CREATE TRIGGER update_questions_updated_at
        BEFORE UPDATE ON questions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_item_kp_map_updated_at ON item_kp_map;
    CREATE TRIGGER update_item_kp_map_updated_at
        BEFORE UPDATE ON item_kp_map
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_prerequisite_relation_updated_at ON prerequisite_relation;
    CREATE TRIGGER update_prerequisite_relation_updated_at
        BEFORE UPDATE ON prerequisite_relation
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_annotation_tasks_updated_at ON annotation_tasks;
    CREATE TRIGGER update_annotation_tasks_updated_at
        BEFORE UPDATE ON annotation_tasks
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_user_sessions_updated_at ON user_sessions;
    CREATE TRIGGER update_user_sessions_updated_at
        BEFORE UPDATE ON user_sessions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_question_assets_updated_at ON question_assets;
    CREATE TRIGGER update_question_assets_updated_at
        BEFORE UPDATE ON question_assets
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_item_param_updated_at ON item_param;
    CREATE TRIGGER update_item_param_updated_at
        BEFORE UPDATE ON item_param
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_question_relation_updated_at ON question_relation;
    CREATE TRIGGER update_question_relation_updated_at
        BEFORE UPDATE ON question_relation
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_knowledge_state_updated_at ON knowledge_state;
    CREATE TRIGGER update_knowledge_state_updated_at
        BEFORE UPDATE ON knowledge_state
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_state_transition_updated_at ON state_transition;
    CREATE TRIGGER update_state_transition_updated_at
        BEFORE UPDATE ON state_transition
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_annotation_logs_updated_at ON annotation_logs;
    CREATE TRIGGER update_annotation_logs_updated_at
        BEFORE UPDATE ON annotation_logs
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_skill_param_updated_at ON skill_param;
    CREATE TRIGGER update_skill_param_updated_at
        BEFORE UPDATE ON skill_param
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    -- 知识点路径维护触发器
    DROP TRIGGER IF EXISTS update_kp_path_trigger ON knowledge_points;
    CREATE TRIGGER update_kp_path_trigger
        BEFORE INSERT OR UPDATE ON knowledge_points
        FOR EACH ROW EXECUTE FUNCTION update_kp_path();
    
    -- 知识点叶子状态维护触发器
    DROP TRIGGER IF EXISTS update_kp_leaf_status_trigger ON knowledge_points;
    CREATE TRIGGER update_kp_leaf_status_trigger
        AFTER INSERT OR DELETE ON knowledge_points
        FOR EACH ROW EXECUTE FUNCTION update_kp_leaf_status();
    
    -- 知识状态掌握比例计算触发器
    DROP TRIGGER IF EXISTS update_mastery_ratio_trigger ON knowledge_state;
    CREATE TRIGGER update_mastery_ratio_trigger
        BEFORE INSERT OR UPDATE ON knowledge_state
        FOR EACH ROW EXECUTE FUNCTION update_mastery_ratio();
    """
    
    with engine.connect() as conn:
        print("创建更新时间戳函数...")
        conn.execute(text(update_timestamp_function))
        
        print("创建知识点路径维护函数...")
        conn.execute(text(update_kp_path_function))
        
        print("创建知识点叶子状态维护函数...")
        conn.execute(text(update_kp_leaf_status_function))
        
        print("创建知识状态掌握比例计算函数...")
        conn.execute(text(update_mastery_ratio_function))
        
        print("创建所有触发器...")
        conn.execute(text(create_triggers_sql))
        
        conn.commit()
        print("所有触发器和函数创建成功！")

if __name__ == "__main__":
    create_triggers_and_functions()

# 后端API服务

基于 FastAPI + SQLAlchemy 的数据标注后端服务。

## 技术栈

- **FastAPI** - 现代Python Web框架
- **SQLAlchemy** - ORM框架
- **Alembic** - 数据库迁移工具
- **Pydantic** - 数据验证
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Celery** - 异步任务队列
- **JWT** - 身份认证

## 项目结构

```
app/
├── api/              # API路由
│   ├── v1/          # API版本1
│   │   ├── auth.py  # 认证相关
│   │   ├── users.py # 用户管理
│   │   ├── questions.py # 题目管理
│   │   ├── knowledge.py # 知识点管理
│   │   └── annotation.py # 标注相关
│   └── deps.py      # 依赖注入
├── core/            # 核心配置
│   ├── config.py    # 应用配置
│   ├── security.py  # 安全相关
│   └── database.py  # 数据库配置
├── models/          # 数据模型
│   ├── user.py      # 用户模型
│   ├── question.py  # 题目模型
│   ├── knowledge.py # 知识点模型
│   └── annotation.py # 标注模型
├── schemas/         # Pydantic模式
├── services/        # 业务逻辑
├── utils/           # 工具函数
├── tasks/           # Celery任务
└── main.py          # 应用入口
```

## 主要功能模块

### 1. 用户认证与授权
- JWT令牌认证
- 基于角色的权限控制
- 用户会话管理

### 2. 题目管理
- 题目CRUD操作
- 批量导入/导出
- 全文搜索
- IRT参数管理

### 3. 知识点管理
- 知识点层级结构
- 先修关系维护
- 知识空间构建

### 4. 数据标注
- 标注任务管理
- 标注工作流
- 质量检测
- 版本控制

### 5. 知识空间算法
- 闭包计算
- 状态图生成
- 一致性检测

## API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 开发指南

### 环境设置

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件

# 数据库迁移
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload
```

### 数据库操作

```bash
# 创建新迁移
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

### 测试

```bash
# 运行测试
pytest

# 测试覆盖率
pytest --cov=app

# 运行特定测试
pytest tests/test_auth.py
```

## 部署

### 开发环境
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境
```bash
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker部署
```bash
docker build -t annotation-backend .
docker run -p 8000:8000 annotation-backend
```

## 配置说明

主要配置项在 `app/core/config.py` 中：

- `DATABASE_URL`: 数据库连接字符串
- `SECRET_KEY`: JWT密钥
- `REDIS_URL`: Redis连接字符串
- `CORS_ORIGINS`: 允许的跨域源

"""
插入示例数据
"""

from sqlalchemy import create_engine, text
from app.core.config import settings
import json

# 创建同步引擎
database_url = str(settings.DATABASE_URL)
if "sslmode" not in database_url:
    database_url += "?sslmode=require"
engine = create_engine(database_url)

def insert_sample_data():
    """插入示例数据"""
    
    with engine.connect() as conn:
        # 插入管理员用户
        print("插入管理员用户...")
        admin_sql = """
        INSERT INTO users (username, password_hash, role, full_name, email, is_active)
        VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5W', 'admin', '系统管理员', '<EMAIL>', TRUE)
        ON CONFLICT (username) DO NOTHING;
        """
        conn.execute(text(admin_sql))
        
        # 插入标注员用户
        print("插入标注员用户...")
        annotator_sql = """
        INSERT INTO users (username, password_hash, role, full_name, email, is_active)
        VALUES 
        ('annotator1', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5W', 'annotator', '标注员1', '<EMAIL>', TRUE),
        ('annotator2', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5W', 'annotator', '标注员2', '<EMAIL>', TRUE)
        ON CONFLICT (username) DO NOTHING;
        """
        conn.execute(text(annotator_sql))
        
        # 插入知识点数据
        print("插入知识点数据...")
        kp_sql = """
        INSERT INTO knowledge_points (kp_id, parent_id, name, code, description, is_leaf, difficulty_level, created_by)
        VALUES 
        (1, NULL, '数学', 'MATH', '数学学科', FALSE, NULL, 1),
        (2, 1, '代数', 'MATH.ALGEBRA', '代数分支', FALSE, 3, 1),
        (3, 1, '几何', 'MATH.GEOMETRY', '几何分支', FALSE, 3, 1),
        (4, 2, '一元一次方程', 'MATH.ALGEBRA.LINEAR_EQ', '一元一次方程求解', TRUE, 2, 1),
        (5, 2, '二次方程', 'MATH.ALGEBRA.QUADRATIC_EQ', '二次方程求解', TRUE, 4, 1),
        (6, 3, '三角形', 'MATH.GEOMETRY.TRIANGLE', '三角形相关知识', TRUE, 3, 1),
        (7, 3, '圆', 'MATH.GEOMETRY.CIRCLE', '圆相关知识', TRUE, 3, 1)
        ON CONFLICT (kp_id) DO NOTHING;
        """
        conn.execute(text(kp_sql))
        
        # 插入先修关系
        print("插入先修关系...")
        prereq_sql = """
        INSERT INTO prerequisite_relation (pre_kp_id, post_kp_id, source, confidence, created_by)
        VALUES 
        (4, 5, 0, 0.9, 1),  -- 一元一次方程 -> 二次方程
        (2, 6, 0, 0.7, 1),  -- 代数 -> 三角形
        (2, 7, 0, 0.6, 1)   -- 代数 -> 圆
        ON CONFLICT (pre_kp_id, post_kp_id) DO NOTHING;
        """
        conn.execute(text(prereq_sql))
        
        # 插入题目数据
        print("插入题目数据...")
        question_content_1 = json.dumps({
            "stem": "解方程：2x + 3 = 7",
            "options": [],
            "type": "fill_blank"
        })
        question_answer_1 = json.dumps({
            "answer": "x = 2",
            "steps": ["2x = 7 - 3", "2x = 4", "x = 2"]
        })
        
        question_content_2 = json.dumps({
            "stem": "下列哪个是二次方程？",
            "options": ["A. x + 1 = 0", "B. x² + 2x + 1 = 0", "C. 2x = 4", "D. x³ + 1 = 0"],
            "type": "single_choice"
        })
        question_answer_2 = json.dumps({
            "answer": "B",
            "explanation": "二次方程的最高次项是2次"
        })
        
        questions_sql = f"""
        INSERT INTO questions (question_id, content, q_type, difficulty_lvl, answer_key, analysis, source, is_active, created_by)
        VALUES 
        (1, '{question_content_1}', 3, 2, '{question_answer_1}', '基础的一元一次方程求解', '教材例题', TRUE, 1),
        (2, '{question_content_2}', 0, 3, '{question_answer_2}', '二次方程的识别', '练习题库', TRUE, 1)
        ON CONFLICT (question_id) DO NOTHING;
        """
        conn.execute(text(questions_sql))
        
        # 插入题目-知识点映射
        print("插入题目-知识点映射...")
        mapping_sql = """
        INSERT INTO item_kp_map (question_id, kp_id, is_required, weight, confidence, source, created_by)
        VALUES 
        (1, 4, TRUE, 1.0, 0.95, 0, 1),  -- 题目1 -> 一元一次方程
        (2, 5, TRUE, 1.0, 0.90, 0, 1)   -- 题目2 -> 二次方程
        ON CONFLICT (question_id, kp_id) DO NOTHING;
        """
        conn.execute(text(mapping_sql))
        
        # 插入标注任务
        print("插入标注任务...")
        task_data = json.dumps({
            "question_ids": [1, 2],
            "kp_ids": [4, 5, 6, 7],
            "instructions": "请为题目标注相关的知识点"
        })
        
        tasks_sql = f"""
        INSERT INTO annotation_tasks (title, description, task_type, status, priority, assigned_to, task_data, created_by)
        VALUES 
        ('数学题目知识点标注', '为新导入的数学题目标注相关知识点', 0, 0, 3, 2, '{task_data}', 1)
        """
        conn.execute(text(tasks_sql))
        
        # 插入技能参数
        print("插入技能参数...")
        skill_params_sql = """
        INSERT INTO skill_param (kp_id, p_l0, p_t, p_g, p_s, model_type, created_by)
        VALUES 
        (4, 0.1, 0.3, 0.2, 0.1, 'BKT', 1),  -- 一元一次方程的BKT参数
        (5, 0.05, 0.25, 0.15, 0.05, 'BKT', 1), -- 二次方程的BKT参数
        (6, 0.08, 0.28, 0.18, 0.08, 'BKT', 1), -- 三角形的BKT参数
        (7, 0.06, 0.26, 0.16, 0.06, 'BKT', 1)  -- 圆的BKT参数
        ON CONFLICT (kp_id) DO NOTHING;
        """
        conn.execute(text(skill_params_sql))
        
        conn.commit()
        print("示例数据插入成功！")

def verify_data():
    """验证数据插入"""
    with engine.connect() as conn:
        # 检查用户数据
        users_result = conn.execute(text("SELECT username, role FROM users ORDER BY user_id;"))
        print("\\n用户数据:")
        for row in users_result:
            print(f"  {row.username} - {row.role}")
        
        # 检查知识点数据
        kp_result = conn.execute(text("SELECT name, code, path FROM knowledge_points ORDER BY kp_id;"))
        print("\\n知识点数据:")
        for row in kp_result:
            print(f"  {row.name} ({row.code}) - 路径: {row.path}")
        
        # 检查题目数据
        questions_result = conn.execute(text("SELECT question_id, content->>'stem' as stem FROM questions ORDER BY question_id;"))
        print("\\n题目数据:")
        for row in questions_result:
            print(f"  题目{row.question_id}: {row.stem}")
        
        # 检查映射数据
        mapping_result = conn.execute(text("""
            SELECT q.question_id, q.content->>'stem' as stem, kp.name as kp_name
            FROM item_kp_map ikm
            JOIN questions q ON ikm.question_id = q.question_id
            JOIN knowledge_points kp ON ikm.kp_id = kp.kp_id
            ORDER BY q.question_id;
        """))
        print("\\n题目-知识点映射:")
        for row in mapping_result:
            print(f"  题目{row.question_id} -> {row.kp_name}")

if __name__ == "__main__":
    insert_sample_data()
    verify_data()

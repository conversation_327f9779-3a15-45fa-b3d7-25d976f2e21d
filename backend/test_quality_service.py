#!/usr/bin/env python3
"""
质量检测服务测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.quality_service import QualityService
    from app.core.database import SessionLocal
    
    print("✓ 成功导入质量检测服务")
    
    # 测试质量检测服务
    db = SessionLocal()
    try:
        quality_service = QualityService(db)
        print("✓ 质量检测服务初始化成功")
        
        # 测试仪表板数据获取
        dashboard_data = quality_service.get_quality_dashboard_data()
        print("✓ 仪表板数据获取成功")
        print(f"  - 总题目数: {dashboard_data['overview']['total_questions']}")
        print(f"  - 已标注题目数: {dashboard_data['overview']['annotated_questions']}")
        print(f"  - 标注覆盖率: {dashboard_data['overview']['annotation_coverage']}%")
        
        # 测试基础检查
        basic_checks = quality_service._run_basic_checks()
        print("✓ 基础检查执行成功")
        print(f"  - 基础检查得分: {basic_checks['score']}")
        
        print("\n🎉 质量检测系统测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()
        
except ImportError as e:
    print(f"❌ 导入失败: {str(e)}")
    print("请确保已安装所有依赖包")
except Exception as e:
    print(f"❌ 未知错误: {str(e)}")
    import traceback
    traceback.print_exc()

"""
创建其他数据库表
"""

from sqlalchemy import create_engine, text
from app.core.config import settings

# 创建同步引擎
database_url = str(settings.DATABASE_URL)
if "sslmode" not in database_url:
    database_url += "?sslmode=require"
engine = create_engine(database_url)

def create_additional_tables():
    """创建其他数据库表"""
    
    # 用户会话表
    user_sessions_sql = """
    CREATE TABLE IF NOT EXISTS user_sessions (
        session_id VARCHAR(128) PRIMARY KEY,
        user_id BIGINT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at TIMESTAMPTZ NOT NULL,
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
    );
    
    CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
    CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
    """
    
    # 题目媒资表
    question_assets_sql = """
    CREATE TABLE IF NOT EXISTS question_assets (
        asset_id BIGSERIAL PRIMARY KEY,
        question_id BIGINT REFERENCES questions(question_id) NOT NULL,
        uri TEXT NOT NULL,
        media_type VARCHAR(50) NOT NULL,
        alt_text TEXT,
        file_size BIGINT,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
    );
    
    CREATE INDEX IF NOT EXISTS idx_question_assets_question_id ON question_assets(question_id);
    CREATE INDEX IF NOT EXISTS idx_question_assets_media_type ON question_assets(media_type);
    """
    
    # IRT参数表
    item_param_sql = """
    CREATE TABLE IF NOT EXISTS item_param (
        question_id BIGINT PRIMARY KEY REFERENCES questions(question_id),
        a REAL CHECK (a IS NULL OR a > 0),
        b REAL,
        c REAL CHECK (c IS NULL OR (c >= 0 AND c <= 1)),
        last_calibrated TIMESTAMPTZ,
        calibration_sample_size INTEGER,
        model_fit REAL,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
    );
    
    CREATE INDEX IF NOT EXISTS idx_item_param_last_calibrated ON item_param(last_calibrated);
    """
    
    # 题目关系表
    question_relation_sql = """
    CREATE TABLE IF NOT EXISTS question_relation (
        relation_id BIGSERIAL PRIMARY KEY,
        source_question_id BIGINT REFERENCES questions(question_id) NOT NULL,
        target_question_id BIGINT REFERENCES questions(question_id) NOT NULL,
        relation_type SMALLINT NOT NULL CHECK (relation_type IN (0,1,2,3,4)),
        strength REAL DEFAULT 1.0 NOT NULL CHECK (strength >= 0 AND strength <= 1),
        confidence REAL DEFAULT 1.0 NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
        source SMALLINT DEFAULT 0 NOT NULL CHECK (source IN (0, 1, 2)),
        description TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT,
        CHECK (source_question_id != target_question_id)
    );
    
    CREATE INDEX IF NOT EXISTS idx_question_relation_source_id ON question_relation(source_question_id);
    CREATE INDEX IF NOT EXISTS idx_question_relation_target_id ON question_relation(target_question_id);
    CREATE INDEX IF NOT EXISTS idx_question_relation_type ON question_relation(relation_type);
    """
    
    # 知识状态表
    knowledge_state_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_state (
        state_id BIGSERIAL PRIMARY KEY,
        state_vector BOOLEAN[] NOT NULL,
        state_hash TEXT UNIQUE NOT NULL,
        state_type SMALLINT DEFAULT 1 NOT NULL CHECK (state_type IN (0,1,2,3)),
        is_valid BOOLEAN DEFAULT TRUE NOT NULL,
        mastery_count INTEGER DEFAULT 0 NOT NULL CHECK (mastery_count >= 0),
        total_count INTEGER NOT NULL CHECK (total_count > 0),
        mastery_ratio REAL DEFAULT 0.0 NOT NULL CHECK (mastery_ratio >= 0 AND mastery_ratio <= 1),
        description TEXT,
        metadata JSONB,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT,
        CHECK (mastery_count <= total_count)
    );
    
    CREATE INDEX IF NOT EXISTS idx_knowledge_state_state_hash ON knowledge_state(state_hash);
    CREATE INDEX IF NOT EXISTS idx_knowledge_state_state_type ON knowledge_state(state_type);
    CREATE INDEX IF NOT EXISTS idx_knowledge_state_is_valid ON knowledge_state(is_valid);
    CREATE INDEX IF NOT EXISTS idx_knowledge_state_mastery_ratio ON knowledge_state(mastery_ratio);
    """
    
    # 状态转移表
    state_transition_sql = """
    CREATE TABLE IF NOT EXISTS state_transition (
        transition_id BIGSERIAL PRIMARY KEY,
        from_state_id BIGINT REFERENCES knowledge_state(state_id) NOT NULL,
        to_state_id BIGINT REFERENCES knowledge_state(state_id) NOT NULL,
        transition_type SMALLINT DEFAULT 0 NOT NULL CHECK (transition_type IN (0,1,2)),
        probability REAL DEFAULT 1.0 NOT NULL CHECK (probability >= 0 AND probability <= 1),
        trigger_kp_id INTEGER REFERENCES knowledge_points(kp_id),
        trigger_question_id BIGINT REFERENCES questions(question_id),
        learning_cost REAL CHECK (learning_cost IS NULL OR learning_cost >= 0),
        difficulty REAL CHECK (difficulty IS NULL OR (difficulty >= 0 AND difficulty <= 1)),
        description TEXT,
        metadata JSONB,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT,
        CHECK (from_state_id != to_state_id)
    );
    
    CREATE INDEX IF NOT EXISTS idx_state_transition_from_state_id ON state_transition(from_state_id);
    CREATE INDEX IF NOT EXISTS idx_state_transition_to_state_id ON state_transition(to_state_id);
    CREATE INDEX IF NOT EXISTS idx_state_transition_transition_type ON state_transition(transition_type);
    """
    
    # 标注日志表
    annotation_logs_sql = """
    CREATE TABLE IF NOT EXISTS annotation_logs (
        log_id BIGSERIAL PRIMARY KEY,
        task_id BIGINT REFERENCES annotation_tasks(task_id),
        user_id BIGINT REFERENCES users(user_id) NOT NULL,
        action SMALLINT NOT NULL CHECK (action IN (0,1,2,3,4,5,6,7,8)),
        description TEXT NOT NULL,
        old_data JSONB,
        new_data JSONB,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
    );
    
    CREATE INDEX IF NOT EXISTS idx_annotation_logs_task_id ON annotation_logs(task_id);
    CREATE INDEX IF NOT EXISTS idx_annotation_logs_user_id ON annotation_logs(user_id);
    CREATE INDEX IF NOT EXISTS idx_annotation_logs_action ON annotation_logs(action);
    CREATE INDEX IF NOT EXISTS idx_annotation_logs_created_at ON annotation_logs(created_at);
    """
    
    # 技能参数表
    skill_param_sql = """
    CREATE TABLE IF NOT EXISTS skill_param (
        kp_id INTEGER PRIMARY KEY REFERENCES knowledge_points(kp_id),
        p_l0 REAL CHECK (p_l0 IS NULL OR (p_l0 >= 0 AND p_l0 <= 1)),
        p_t REAL CHECK (p_t IS NULL OR (p_t >= 0 AND p_t <= 1)),
        p_g REAL CHECK (p_g IS NULL OR (p_g >= 0 AND p_g <= 1)),
        p_s REAL CHECK (p_s IS NULL OR (p_s >= 0 AND p_s <= 1)),
        model_version VARCHAR(16),
        model_type VARCHAR(10) DEFAULT 'BKT' NOT NULL CHECK (model_type IN ('BKT', 'IRT', 'DKT', 'DINA', 'DINO')),
        last_update TIMESTAMPTZ,
        calibration_quality NUMERIC(3,2) CHECK (calibration_quality IS NULL OR (calibration_quality >= 0 AND calibration_quality <= 1)),
        sample_size INTEGER CHECK (sample_size IS NULL OR sample_size > 0),
        extended_params JSONB,
        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        created_by BIGINT,
        updated_by BIGINT
    );
    
    CREATE INDEX IF NOT EXISTS idx_skill_param_model_type ON skill_param(model_type);
    CREATE INDEX IF NOT EXISTS idx_skill_param_last_update ON skill_param(last_update);
    """
    
    with engine.connect() as conn:
        print("创建用户会话表...")
        conn.execute(text(user_sessions_sql))
        
        print("创建题目媒资表...")
        conn.execute(text(question_assets_sql))
        
        print("创建IRT参数表...")
        conn.execute(text(item_param_sql))
        
        print("创建题目关系表...")
        conn.execute(text(question_relation_sql))
        
        print("创建知识状态表...")
        conn.execute(text(knowledge_state_sql))
        
        print("创建状态转移表...")
        conn.execute(text(state_transition_sql))
        
        print("创建标注日志表...")
        conn.execute(text(annotation_logs_sql))
        
        print("创建技能参数表...")
        conn.execute(text(skill_param_sql))
        
        conn.commit()
        print("所有附加表创建成功！")

if __name__ == "__main__":
    create_additional_tables()

"""
简化的测试模型，用于验证数据库连接
"""

from sqlalchemy import String, Boolean, DateTime, Integer, BigInteger, Text, func
from sqlalchemy.orm import Mapped, mapped_column
from app.models.base import Base, TimestampMixin


class TestUser(Base, TimestampMixin):
    """测试用户表"""
    
    __tablename__ = "test_users"
    
    user_id: Mapped[int] = mapped_column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="用户ID"
    )
    
    username: Mapped[str] = mapped_column(
        Text,
        unique=True,
        nullable=False,
        comment="用户名"
    )
    
    email: Mapped[str] = mapped_column(
        Text,
        unique=True,
        nullable=True,
        comment="邮箱地址"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    def __repr__(self) -> str:
        return f"<TestUser(id={self.user_id}, username='{self.username}')>"


if __name__ == "__main__":
    # 测试数据库连接
    from app.core.database import engine
    
    print("创建测试表...")
    Base.metadata.create_all(bind=engine)
    print("测试表创建成功！")

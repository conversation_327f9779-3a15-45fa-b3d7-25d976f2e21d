# 质量检测系统使用指南

## 概述

质量检测系统是自适应学习服务数据标注模块的重要组成部分，负责对标注数据进行全面的质量检查和一致性验证，确保标注数据的准确性、完整性和一致性。

## 功能特性

### 1. 自动化质量检测
- **基础格式检查**：检查必填字段、数据类型、格式规范
- **业务规则验证**：验证题目与知识点关联的合理性、先修关系规则、置信度范围
- **逻辑一致性检查**：环路检测、传递性验证、难度单调性检查
- **异常数据识别**：统计异常、规则异常、模式异常检测

### 2. 质量监控仪表板
- **实时统计**：总题目数、标注覆盖率、映射数量等基础指标
- **质量趋势**：质量得分变化趋势、问题数量统计
- **用户表现**：各用户的标注数量和质量统计

### 3. 质量报告生成
- **综合评分**：基于多维度检测的综合质量得分
- **问题分类**：按严重程度和类别分类的问题列表
- **改进建议**：基于检测结果的具体改进建议

## 使用方法

### 1. 访问质量监控仪表板

1. 登录系统后，在左侧导航栏中点击"质量检测" → "质量监控"
2. 查看基础统计信息：
   - 总题目数和已标注题目数
   - 标注覆盖率进度条
   - 总映射数和平均映射数
   - 近期活动统计

3. 执行质量检测：
   - 点击"执行质量检测"按钮
   - 系统将自动运行完整的质量检测流程
   - 检测完成后显示综合得分和问题统计

### 2. 查看质量报告

1. 在左侧导航栏中点击"质量检测" → "质量报告"
2. 点击"生成报告"按钮创建最新的质量分析报告
3. 报告包含以下内容：
   - **报告头部**：报告ID、生成时间、综合得分、质量等级
   - **检测模块得分**：基础检查、业务规则、一致性检查、异常检测的详细得分
   - **改进建议**：基于检测结果的具体改进措施

### 3. 查看和处理质量问题

1. 在质量报告页面切换到"问题列表"标签
2. 使用筛选按钮查看不同严重程度的问题：
   - **严重**：影响系统功能的问题，需要立即处理
   - **中等**：影响数据质量的问题，需要及时处理
   - **轻微**：不影响核心功能的问题，可以延后处理

3. 查看问题详情：
   - 问题描述和分类
   - 具体的问题数据
   - 建议的处理方式

## 质量检测规则

### 1. 基础检测规则
- **孤儿题目检测**：识别无知识点关联的题目
- **孤儿知识点检测**：识别无题目关联的叶子知识点
- **缺失内容检测**：检查题干内容缺失的题目
- **无效置信度检测**：检查置信度不在0.6-1.0范围的映射

### 2. 业务规则检测
- **置信度范围检查**：置信度必须在0.6-1.0之间
- **映射数量限制**：每个题目关联的知识点数量不超过10个
- **难度一致性检查**：先修题目难度不应高于后续题目
- **先修关系检查**：不允许自引用的先修关系

### 3. 一致性检测
- **环路检测**：检测先修关系图中的环路
- **传递性验证**：验证先修关系的传递性
- **层级一致性**：检查知识点层级结构的合理性
- **关系冲突检测**：识别相互冲突的题目关系

### 4. 异常检测
- **统计异常**：识别置信度的异常值（超出2个标准差）
- **模式异常**：检测用户标注模式的异常情况
- **频率异常**：识别知识点使用频率的异常
- **分布异常**：检测置信度分布的过度集中

## 质量等级划分

- **优秀(A级)**：质量得分≥90分
- **良好(B级)**：质量得分80-89分
- **合格(C级)**：质量得分70-79分
- **不合格(D级)**：质量得分<70分

## 权限要求

- **查看权限**：所有用户都可以查看质量监控仪表板
- **执行检测**：需要管理员或质量管理员权限
- **生成报告**：需要管理员或质量管理员权限
- **修复问题**：需要管理员权限

## API接口

### 1. 执行质量检测
```
GET /api/v1/quality/check
```

### 2. 获取仪表板数据
```
GET /api/v1/quality/dashboard
```

### 3. 生成质量报告
```
GET /api/v1/quality/report
```

### 4. 获取质量问题列表
```
GET /api/v1/quality/issues?severity=high&category=basic_checks&limit=100
```

## 最佳实践

1. **定期检测**：建议每周执行一次完整的质量检测
2. **及时处理**：优先处理严重和中等级别的质量问题
3. **持续改进**：根据质量报告的建议持续优化标注流程
4. **培训指导**：基于质量问题为标注员提供针对性培训

## 故障排除

### 常见问题

1. **检测执行失败**
   - 检查数据库连接是否正常
   - 确认用户权限是否足够
   - 查看系统日志获取详细错误信息

2. **仪表板数据加载缓慢**
   - 检查数据库性能
   - 考虑添加数据库索引优化查询

3. **质量得分异常**
   - 检查检测规则配置是否正确
   - 验证权重设置是否合理

如有其他问题，请联系系统管理员或查看详细的技术文档。

# 代码规范

本文档定义了自适应学习数据标注系统的代码规范和最佳实践。

## 总体原则

1. **一致性** - 保持代码风格的一致性
2. **可读性** - 代码应该易于理解和维护
3. **简洁性** - 避免不必要的复杂性
4. **安全性** - 遵循安全编码实践
5. **性能** - 考虑代码的性能影响

## Python 代码规范

### 代码风格

- 遵循 [PEP 8](https://pep8.org/) 规范
- 使用 **Black** 进行代码格式化
- 使用 **isort** 进行导入排序
- 最大行长度：88 字符

### 命名规范

```python
# 变量和函数：snake_case
user_name = "john"
def get_user_data():
    pass

# 类名：PascalCase
class UserService:
    pass

# 常量：UPPER_SNAKE_CASE
MAX_RETRY_COUNT = 3

# 私有成员：前缀下划线
class MyClass:
    def __init__(self):
        self._private_var = None
        self.__very_private = None
```

### 类型注解

```python
from typing import List, Optional, Dict, Any

def process_users(
    users: List[Dict[str, Any]], 
    active_only: bool = True
) -> Optional[List[str]]:
    """处理用户数据"""
    pass
```

### 文档字符串

```python
def calculate_score(
    answers: List[bool], 
    weights: Optional[List[float]] = None
) -> float:
    """
    计算加权得分
    
    Args:
        answers: 答题结果列表
        weights: 权重列表，如果为None则使用等权重
        
    Returns:
        计算得到的加权得分
        
    Raises:
        ValueError: 当answers为空时
        
    Example:
        >>> calculate_score([True, False, True])
        0.67
    """
    pass
```

### 异常处理

```python
# 具体的异常类型
try:
    result = risky_operation()
except ValueError as e:
    logger.error(f"值错误: {e}")
    raise
except Exception as e:
    logger.error(f"未知错误: {e}")
    raise

# 自定义异常
class AnnotationError(Exception):
    """标注相关异常"""
    pass
```

## TypeScript/React 代码规范

### 代码风格

- 使用 **Prettier** 进行代码格式化
- 使用 **ESLint** 进行代码检查
- 最大行长度：100 字符
- 使用分号结尾
- 使用单引号

### 命名规范

```typescript
// 变量和函数：camelCase
const userName = 'john'
const getUserData = () => {}

// 类型和接口：PascalCase
interface UserData {
  id: string
  name: string
}

type UserRole = 'admin' | 'user'

// 组件：PascalCase
const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  return <div>User Profile</div>
}

// 常量：UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3

// 枚举：PascalCase
enum UserStatus {
  Active = 'active',
  Inactive = 'inactive',
}
```

### 组件规范

```typescript
// 组件Props接口
interface UserCardProps {
  user: User
  onEdit?: (user: User) => void
  className?: string
}

// 函数组件
export const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  onEdit, 
  className 
}) => {
  const [isEditing, setIsEditing] = useState(false)
  
  const handleEdit = useCallback(() => {
    onEdit?.(user)
  }, [user, onEdit])
  
  return (
    <div className={cn('user-card', className)}>
      <h3>{user.name}</h3>
      <button onClick={handleEdit}>编辑</button>
    </div>
  )
}
```

### Hooks 规范

```typescript
// 自定义Hook
export const useUserData = (userId: string) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true)
        const userData = await userService.getUser(userId)
        setUser(userData)
      } catch (err) {
        setError(err instanceof Error ? err.message : '未知错误')
      } finally {
        setLoading(false)
      }
    }
    
    fetchUser()
  }, [userId])
  
  return { user, loading, error }
}
```

## 提交规范

### 提交信息格式

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档变更
- `style`: 代码格式化
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 添加或修改测试
- `build`: 构建系统变更
- `ci`: CI配置变更
- `chore`: 其他变更

### 示例

```bash
feat(auth): 添加JWT认证功能

- 实现用户登录和注册
- 添加JWT令牌验证中间件
- 更新API文档

Closes #123
```

## 测试规范

### 测试文件命名

```
# Python
test_user_service.py
test_auth_endpoints.py

# TypeScript
UserService.test.ts
AuthForm.test.tsx
```

### 测试结构

```python
# Python测试
class TestUserService:
    def setup_method(self):
        """每个测试方法前执行"""
        self.user_service = UserService()
    
    def test_create_user_success(self):
        """测试成功创建用户"""
        # Arrange
        user_data = {"username": "test", "email": "<EMAIL>"}
        
        # Act
        result = self.user_service.create_user(user_data)
        
        # Assert
        assert result.username == "test"
        assert result.email == "<EMAIL>"
    
    def test_create_user_duplicate_username(self):
        """测试重复用户名创建失败"""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="用户名已存在"):
            self.user_service.create_user({"username": "existing"})
```

```typescript
// TypeScript测试
describe('UserService', () => {
  let userService: UserService
  
  beforeEach(() => {
    userService = new UserService()
  })
  
  it('should create user successfully', async () => {
    // Arrange
    const userData = { username: 'test', email: '<EMAIL>' }
    
    // Act
    const result = await userService.createUser(userData)
    
    // Assert
    expect(result.username).toBe('test')
    expect(result.email).toBe('<EMAIL>')
  })
  
  it('should throw error for duplicate username', async () => {
    // Arrange & Act & Assert
    await expect(
      userService.createUser({ username: 'existing' })
    ).rejects.toThrow('用户名已存在')
  })
})
```

## 代码审查清单

### 功能性
- [ ] 代码实现了预期功能
- [ ] 边界条件处理正确
- [ ] 错误处理适当
- [ ] 性能考虑合理

### 代码质量
- [ ] 代码风格一致
- [ ] 命名清晰有意义
- [ ] 函数和类职责单一
- [ ] 避免代码重复

### 安全性
- [ ] 输入验证充分
- [ ] 敏感信息不暴露
- [ ] SQL注入防护
- [ ] XSS防护

### 测试
- [ ] 单元测试覆盖充分
- [ ] 测试用例有意义
- [ ] 集成测试通过
- [ ] 边界测试完整

## 工具配置

### 开发环境设置

```bash
# 安装pre-commit hooks
pip install pre-commit
pre-commit install

# 运行代码质量检查
./scripts/code-quality.sh check

# 自动修复格式问题
./scripts/code-quality.sh fix
```

### IDE配置

推荐使用以下IDE设置：

- **VSCode**: 安装Python、TypeScript、ESLint、Prettier插件
- **PyCharm**: 配置Black、isort、flake8集成
- **WebStorm**: 配置ESLint、Prettier集成

## 持续改进

代码规范是一个持续改进的过程：

1. 定期审查和更新规范
2. 收集团队反馈
3. 学习行业最佳实践
4. 工具和流程优化

遵循这些规范有助于提高代码质量、减少bug、提升团队协作效率。

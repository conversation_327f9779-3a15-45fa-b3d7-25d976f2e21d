<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            padding: var(--space-6);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
        }
        
        .stat-number {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-2);
        }
        
        .stat-label {
            font-size: var(--text-sm);
            opacity: 0.9;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-8);
        }
        
        .action-card {
            padding: var(--space-6);
            text-align: center;
            border: 2px dashed var(--gray-300);
            border-radius: var(--radius-lg);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-card:hover {
            border-color: var(--primary-500);
            background-color: var(--primary-50);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: var(--space-3);
            color: var(--primary-500);
        }
        
        .recent-activities {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }
        
        .activity-item {
            padding: var(--space-4);
            border-bottom: 1px solid var(--gray-100);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: var(--primary-100);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-600);
            font-weight: var(--font-semibold);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: var(--font-medium);
            margin-bottom: var(--space-1);
        }
        
        .activity-time {
            font-size: var(--text-xs);
            color: var(--gray-500);
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link active">首页</a>
                <a href="knowledge-space.html" class="nav-link">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link">标注工作流</a>
                <a href="quality-control.html" class="nav-link">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item active">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <h1 class="text-2xl font-bold mb-2">系统概览</h1>
            <p class="text-gray-600">欢迎使用自适应学习服务数据标注模块</p>
        </div>

        <!-- 统计卡片 -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-number">1,234</div>
                <div class="stat-label">知识点总数</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, var(--success-500), var(--success-600));">
                <div class="stat-number">5,678</div>
                <div class="stat-label">题目总数</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, var(--warning-500), var(--warning-600));">
                <div class="stat-number">89</div>
                <div class="stat-label">进行中的任务</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, var(--info-500), var(--info-600));">
                <div class="stat-number">95.6%</div>
                <div class="stat-label">标注质量得分</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="card-title">快速操作</h2>
            </div>
            <div class="card-body">
                <div class="quick-actions">
                    <div class="action-card" onclick="location.href='knowledge-points-form.html'">
                        <div class="action-icon">➕</div>
                        <h3 class="font-semibold mb-2">添加知识点</h3>
                        <p class="text-sm text-gray-600">创建新的知识点</p>
                    </div>
                    <div class="action-card" onclick="location.href='questions-form.html'">
                        <div class="action-icon">📝</div>
                        <h3 class="font-semibold mb-2">添加题目</h3>
                        <p class="text-sm text-gray-600">录入新的题目</p>
                    </div>
                    <div class="action-card" onclick="location.href='annotation-tasks-form.html'">
                        <div class="action-icon">✏️</div>
                        <h3 class="font-semibold mb-2">创建标注任务</h3>
                        <p class="text-sm text-gray-600">分派标注工作</p>
                    </div>
                    <div class="action-card" onclick="location.href='quality-reports.html'">
                        <div class="action-icon">📊</div>
                        <h3 class="font-semibold mb-2">查看质量报告</h3>
                        <p class="text-sm text-gray-600">监控标注质量</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h2 class="card-title">最近活动</h2>
                    <a href="#" class="text-sm text-primary-600">查看全部</a>
                </div>
            </div>
            <div class="recent-activities">
                <div class="activity-item">
                    <div class="activity-avatar">张</div>
                    <div class="activity-content">
                        <div class="activity-title">张三完成了"英语语法知识点"的标注任务</div>
                        <div class="activity-time">2小时前</div>
                    </div>
                    <span class="badge badge-success">已完成</span>
                </div>
                <div class="activity-item">
                    <div class="activity-avatar">李</div>
                    <div class="activity-content">
                        <div class="activity-title">李四创建了新的标注任务"数学函数关系标注"</div>
                        <div class="activity-time">4小时前</div>
                    </div>
                    <span class="badge badge-primary">新建</span>
                </div>
                <div class="activity-item">
                    <div class="activity-avatar">王</div>
                    <div class="activity-content">
                        <div class="activity-title">王五审核通过了"物理力学"知识点的先修关系</div>
                        <div class="activity-time">6小时前</div>
                    </div>
                    <span class="badge badge-success">已审核</span>
                </div>
                <div class="activity-item">
                    <div class="activity-avatar">赵</div>
                    <div class="activity-content">
                        <div class="activity-title">赵六发布了新版本 v2.1.3 到生产环境</div>
                        <div class="activity-time">1天前</div>
                    </div>
                    <span class="badge badge-warning">已发布</span>
                </div>
                <div class="activity-item">
                    <div class="activity-avatar">系</div>
                    <div class="activity-content">
                        <div class="activity-title">系统自动检测到5个数据质量问题</div>
                        <div class="activity-time">1天前</div>
                    </div>
                    <span class="badge badge-error">需处理</span>
                </div>
            </div>
        </div>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 页面特定的JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟实时数据更新
            setInterval(() => {
                // 这里可以添加实时数据更新逻辑
                console.log('更新仪表板数据...');
            }, 30000);

            // 添加快速操作的点击效果
            document.querySelectorAll('.action-card').forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量报告 - 自适应学习服务数据标注模块</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .metric-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .metric-card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .metric-value {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-2);
        }
        
        .metric-value.success { color: var(--success-600); }
        .metric-value.warning { color: var(--warning-600); }
        .metric-value.error { color: var(--error-600); }
        .metric-value.info { color: var(--info-600); }
        
        .metric-label {
            font-size: var(--text-sm);
            color: var(--gray-600);
            margin-bottom: var(--space-2);
        }
        
        .metric-trend {
            font-size: var(--text-xs);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
        }
        
        .trend-up {
            background: var(--success-100);
            color: var(--success-700);
        }
        
        .trend-down {
            background: var(--error-100);
            color: var(--error-700);
        }
        
        .chart-container {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
        }
        
        .chart-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
        }
        
        .chart-controls {
            display: flex;
            gap: var(--space-2);
        }
        
        .chart-placeholder {
            height: 300px;
            background: var(--gray-50);
            border: 2px dashed var(--gray-300);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
            font-size: var(--text-lg);
        }
        
        .issues-list {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .issue-item {
            padding: var(--space-4);
            border-bottom: 1px solid var(--gray-100);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .issue-item:last-child {
            border-bottom: none;
        }
        
        .issue-severity {
            width: 12px;
            height: 12px;
            border-radius: var(--radius-full);
        }
        
        .severity-high { background: var(--error-500); }
        .severity-medium { background: var(--warning-500); }
        .severity-low { background: var(--success-500); }
        
        .issue-content {
            flex: 1;
        }
        
        .issue-title {
            font-weight: var(--font-medium);
            margin-bottom: var(--space-1);
        }
        
        .issue-description {
            font-size: var(--text-sm);
            color: var(--gray-600);
        }
        
        .issue-meta {
            font-size: var(--text-xs);
            color: var(--gray-500);
            text-align: right;
        }
        
        .filters-section {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            align-items: end;
        }
        
        .quality-score {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto var(--space-4);
        }
        
        .score-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(var(--success-500) 0deg 324deg, var(--gray-200) 324deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .score-inner {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-xl);
            font-weight: var(--font-bold);
            color: var(--success-600);
        }
        
        .annotator-performance {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .performance-header {
            background: var(--gray-50);
            padding: var(--space-4);
            font-weight: var(--font-semibold);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .performance-item {
            padding: var(--space-4);
            border-bottom: 1px solid var(--gray-100);
            display: grid;
            grid-template-columns: 1fr auto auto auto auto;
            gap: var(--space-4);
            align-items: center;
        }
        
        .performance-item:last-child {
            border-bottom: none;
        }
        
        .annotator-name {
            font-weight: var(--font-medium);
        }
        
        .performance-score {
            text-align: center;
            font-weight: var(--font-semibold);
        }
        
        .score-excellent { color: var(--success-600); }
        .score-good { color: var(--info-600); }
        .score-average { color: var(--warning-600); }
        .score-poor { color: var(--error-600); }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">知识空间标注系统</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">首页</a>
                <a href="knowledge-space.html" class="nav-link">知识空间</a>
                <a href="annotation-workflow.html" class="nav-link">标注工作流</a>
                <a href="quality-control.html" class="nav-link active">质量控制</a>
                <a href="version-management.html" class="nav-link">版本管理</a>
            </nav>
            <div class="user-menu">
                <span>欢迎，张三</span>
                <button class="btn btn-outline btn-sm">退出</button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="sidebar-item">
                📊 系统概览
            </a>
            <a href="knowledge-points.html" class="sidebar-item">
                🧠 知识点管理
            </a>
            <a href="questions.html" class="sidebar-item">
                📝 题目管理
            </a>
            <a href="annotation-tasks.html" class="sidebar-item">
                ✏️ 标注任务
            </a>
            <a href="quality-reports.html" class="sidebar-item active">
                📈 质量报告
            </a>
            <a href="model-integration.html" class="sidebar-item">
                🔗 模型集成
            </a>
            <a href="system-settings.html" class="sidebar-item">
                ⚙️ 系统设置
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">质量报告</h1>
                    <p class="text-gray-600">监控和分析标注数据的质量状况</p>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-outline" onclick="exportReport()">
                        📤 导出报告
                    </button>
                    <button class="btn btn-outline" onclick="scheduleReport()">
                        ⏰ 定时报告
                    </button>
                    <button class="btn btn-primary" onclick="generateReport()">
                        📊 生成报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="form-group">
                    <label class="label">时间范围</label>
                    <select class="select" id="time-range">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">标注类型</label>
                    <select class="select" id="annotation-type">
                        <option value="">全部类型</option>
                        <option value="0">题-知识点标注</option>
                        <option value="1">题-题关系标注</option>
                        <option value="2">先修关系标注</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="label">标注员</label>
                    <select class="select" id="annotator">
                        <option value="">全部标注员</option>
                        <option value="1">张三</option>
                        <option value="2">李四</option>
                        <option value="3">王五</option>
                        <option value="4">赵六</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="updateReport()">更新报告</button>
                </div>
            </div>
        </div>

        <!-- 质量指标概览 -->
        <div class="dashboard-grid">
            <div class="metric-card">
                <div class="quality-score">
                    <div class="score-circle">
                        <div class="score-inner">95.6%</div>
                    </div>
                </div>
                <div class="metric-label">整体质量得分</div>
                <div class="metric-trend trend-up">↗ +2.3%</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value success">98.2%</div>
                <div class="metric-label">标注完整性</div>
                <div class="metric-trend trend-up">↗ +1.5%</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value info">94.7%</div>
                <div class="metric-label">标注准确性</div>
                <div class="metric-trend trend-up">↗ +0.8%</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value warning">89.3%</div>
                <div class="metric-label">标注一致性</div>
                <div class="metric-trend trend-down">↘ -1.2%</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value error">23</div>
                <div class="metric-label">待处理问题</div>
                <div class="metric-trend trend-down">↘ -5</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value info">0.85</div>
                <div class="metric-label">标注员一致性(Kappa)</div>
                <div class="metric-trend trend-up">↗ +0.03</div>
            </div>
        </div>

        <!-- 质量趋势图表 -->
        <div class="chart-container">
            <div class="chart-header">
                <h2 class="chart-title">质量趋势分析</h2>
                <div class="chart-controls">
                    <button class="btn btn-sm btn-outline" onclick="switchChart('quality')">质量得分</button>
                    <button class="btn btn-sm btn-outline" onclick="switchChart('volume')">标注量</button>
                    <button class="btn btn-sm btn-outline" onclick="switchChart('efficiency')">效率</button>
                </div>
            </div>
            <div class="chart-placeholder">
                📈 质量趋势图表
                <br>
                <small>（这里将显示实际的图表组件）</small>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-6);">
            <!-- 质量问题列表 -->
            <div class="card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h2 class="card-title">质量问题</h2>
                        <span class="badge badge-error">23个问题</span>
                    </div>
                </div>
                <div class="issues-list">
                    <div class="issue-item">
                        <div class="issue-severity severity-high"></div>
                        <div class="issue-content">
                            <div class="issue-title">知识点标注缺失</div>
                            <div class="issue-description">发现15个题目未标注知识点</div>
                        </div>
                        <div class="issue-meta">
                            <div>2小时前</div>
                            <div>张三</div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-severity severity-medium"></div>
                        <div class="issue-content">
                            <div class="issue-title">先修关系冲突</div>
                            <div class="issue-description">检测到3处先修关系存在逻辑冲突</div>
                        </div>
                        <div class="issue-meta">
                            <div>4小时前</div>
                            <div>系统检测</div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-severity severity-low"></div>
                        <div class="issue-content">
                            <div class="issue-title">置信度偏低</div>
                            <div class="issue-description">5个标注的置信度低于0.8</div>
                        </div>
                        <div class="issue-meta">
                            <div>6小时前</div>
                            <div>李四</div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-severity severity-medium"></div>
                        <div class="issue-content">
                            <div class="issue-title">标注不一致</div>
                            <div class="issue-description">同一题目的多人标注结果不一致</div>
                        </div>
                        <div class="issue-meta">
                            <div>8小时前</div>
                            <div>王五</div>
                        </div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-severity severity-high"></div>
                        <div class="issue-content">
                            <div class="issue-title">IRT参数异常</div>
                            <div class="issue-description">检测到2个题目的IRT参数超出正常范围</div>
                        </div>
                        <div class="issue-meta">
                            <div>1天前</div>
                            <div>系统检测</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary btn-sm" onclick="viewAllIssues()">查看全部问题</button>
                </div>
            </div>

            <!-- 标注员表现 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">标注员表现</h2>
                </div>
                <div class="annotator-performance">
                    <div class="performance-header">
                        <div style="display: grid; grid-template-columns: 1fr auto auto auto auto; gap: var(--space-4);">
                            <span>标注员</span>
                            <span>完成量</span>
                            <span>准确率</span>
                            <span>一致性</span>
                            <span>综合得分</span>
                        </div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="annotator-name">张三</div>
                        <div class="performance-score">156</div>
                        <div class="performance-score">96.8%</div>
                        <div class="performance-score">0.89</div>
                        <div class="performance-score score-excellent">A+</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="annotator-name">李四</div>
                        <div class="performance-score">142</div>
                        <div class="performance-score">94.2%</div>
                        <div class="performance-score">0.85</div>
                        <div class="performance-score score-good">A</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="annotator-name">王五</div>
                        <div class="performance-score">128</div>
                        <div class="performance-score">91.5%</div>
                        <div class="performance-score">0.82</div>
                        <div class="performance-score score-good">B+</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="annotator-name">赵六</div>
                        <div class="performance-score">98</div>
                        <div class="performance-score">88.7%</div>
                        <div class="performance-score">0.78</div>
                        <div class="performance-score score-average">B</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="annotator-name">钱七</div>
                        <div class="performance-score">76</div>
                        <div class="performance-score">85.3%</div>
                        <div class="performance-score">0.72</div>
                        <div class="performance-score score-poor">C+</div>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary btn-sm" onclick="viewDetailedPerformance()">详细表现分析</button>
                </div>
            </div>
        </div>

        <!-- 改进建议 -->
        <div class="card mt-6">
            <div class="card-header">
                <h2 class="card-title">质量改进建议</h2>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="alert alert-warning">
                        <strong>标注一致性需要改进：</strong>建议加强标注员培训，统一标注标准，定期进行标注一致性检查。
                    </div>
                    <div class="alert alert-info">
                        <strong>自动化检测优化：</strong>可以增加更多自动化质量检测规则，减少人工审核工作量。
                    </div>
                    <div class="alert alert-success">
                        <strong>表现优秀：</strong>整体标注质量保持在高水平，继续保持当前的质量管理流程。
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="assets/js/common.js"></script>
    <script>
        // 质量报告相关功能
        let currentChart = 'quality';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            loadQualityData();
        });

        // 初始化报告
        function initializeReport() {
            console.log('初始化质量报告');
        }

        // 加载质量数据
        function loadQualityData() {
            // 模拟加载数据
            console.log('加载质量数据');
        }

        // 更新报告
        function updateReport() {
            const timeRange = document.getElementById('time-range').value;
            const annotationType = document.getElementById('annotation-type').value;
            const annotator = document.getElementById('annotator').value;
            
            console.log('更新报告:', { timeRange, annotationType, annotator });
            Message.info('正在更新报告数据...');
            
            setTimeout(() => {
                Message.success('报告数据更新完成');
            }, 2000);
        }

        // 切换图表
        function switchChart(chartType) {
            currentChart = chartType;
            
            // 更新按钮状态
            document.querySelectorAll('.chart-controls .btn').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline');
            });
            event.target.classList.remove('btn-outline');
            event.target.classList.add('btn-primary');
            
            // 更新图表内容
            const placeholder = document.querySelector('.chart-placeholder');
            const chartTitles = {
                'quality': '📈 质量得分趋势图表',
                'volume': '📊 标注量统计图表',
                'efficiency': '⚡ 效率分析图表'
            };
            
            placeholder.innerHTML = `
                ${chartTitles[chartType]}
                <br>
                <small>（这里将显示实际的图表组件）</small>
            `;
            
            Message.info(`切换到${chartTitles[chartType]}`);
        }

        // 查看所有问题
        function viewAllIssues() {
            location.href = 'quality-issues.html';
        }

        // 查看详细表现分析
        function viewDetailedPerformance() {
            location.href = 'annotator-performance.html';
        }

        // 生成报告
        function generateReport() {
            Message.info('正在生成质量报告...');
            
            setTimeout(() => {
                Message.success('质量报告生成完成');
            }, 3000);
        }

        // 导出报告
        function exportReport() {
            const formats = ['PDF', 'Excel', 'Word'];
            const formatOptions = formats.map(format => 
                `<label class="flex items-center gap-2 mb-2">
                    <input type="radio" name="format" value="${format.toLowerCase()}">
                    <span>${format}</span>
                </label>`
            ).join('');
            
            const content = `
                <div class="space-y-4">
                    <div>
                        <label class="label">选择导出格式：</label>
                        <div class="mt-2">
                            ${formatOptions}
                        </div>
                    </div>
                    <div>
                        <label class="label">报告内容：</label>
                        <div class="mt-2">
                            <label class="flex items-center gap-2 mb-2">
                                <input type="checkbox" checked>
                                <span>质量指标概览</span>
                            </label>
                            <label class="flex items-center gap-2 mb-2">
                                <input type="checkbox" checked>
                                <span>趋势分析图表</span>
                            </label>
                            <label class="flex items-center gap-2 mb-2">
                                <input type="checkbox" checked>
                                <span>问题列表</span>
                            </label>
                            <label class="flex items-center gap-2 mb-2">
                                <input type="checkbox" checked>
                                <span>标注员表现</span>
                            </label>
                        </div>
                    </div>
                </div>
            `;
            
            const footer = `
                <div class="flex gap-2 justify-end">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="doExportReport(); this.closest('.modal-overlay').remove()">导出</button>
                </div>
            `;
            
            Modal.show('导出质量报告', content, { footer });
        }

        // 执行导出
        function doExportReport() {
            Message.info('正在导出报告...');
            setTimeout(() => {
                Message.success('报告导出成功');
            }, 2000);
        }

        // 定时报告
        function scheduleReport() {
            const content = `
                <div class="space-y-4">
                    <div class="form-group">
                        <label class="label">报告频率：</label>
                        <select class="select">
                            <option value="daily">每日</option>
                            <option value="weekly">每周</option>
                            <option value="monthly">每月</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="label">发送时间：</label>
                        <input type="time" class="input" value="09:00">
                    </div>
                    <div class="form-group">
                        <label class="label">接收邮箱：</label>
                        <input type="email" class="input" placeholder="输入邮箱地址">
                    </div>
                </div>
            `;
            
            const footer = `
                <div class="flex gap-2 justify-end">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="saveSchedule(); this.closest('.modal-overlay').remove()">保存</button>
                </div>
            `;
            
            Modal.show('设置定时报告', content, { footer });
        }

        // 保存定时设置
        function saveSchedule() {
            Message.success('定时报告设置已保存');
        }
    </script>
</body>
</html>

# Pre-commit hooks配置
# 在提交代码前自动运行代码质量检查

repos:
  # 通用hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        name: 删除行尾空白字符
      - id: end-of-file-fixer
        name: 确保文件以换行符结尾
      - id: check-yaml
        name: 检查YAML文件语法
      - id: check-json
        name: 检查JSON文件语法
      - id: check-toml
        name: 检查TOML文件语法
      - id: check-xml
        name: 检查XML文件语法
      - id: check-merge-conflict
        name: 检查合并冲突标记
      - id: check-case-conflict
        name: 检查文件名大小写冲突
      - id: check-added-large-files
        name: 检查大文件
        args: ['--maxkb=1000']
      - id: check-executables-have-shebangs
        name: 检查可执行文件是否有shebang
      - id: mixed-line-ending
        name: 检查混合行结束符
        args: ['--fix=lf']

  # Python代码格式化 - Black
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        name: Black代码格式化
        language_version: python3
        files: ^backend/
        args: [--line-length=88]

  # Python导入排序 - isort
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort导入排序
        files: ^backend/
        args: [--profile=black, --line-length=88]

  # Python代码检查 - flake8
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        name: Flake8代码检查
        files: ^backend/
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  # Python类型检查 - mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        name: MyPy类型检查
        files: ^backend/
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports, --no-strict-optional]

  # Python安全检查 - bandit
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        name: Bandit安全检查
        files: ^backend/
        args: [-r, -f, json, -o, bandit-report.json]
        exclude: ^backend/tests/

  # JavaScript/TypeScript代码检查 - ESLint
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.53.0
    hooks:
      - id: eslint
        name: ESLint代码检查
        files: ^frontend/.*\.(js|jsx|ts|tsx)$
        additional_dependencies:
          - eslint@8.53.0
          - '@typescript-eslint/eslint-plugin@6.10.0'
          - '@typescript-eslint/parser@6.10.0'
          - eslint-plugin-react-hooks@4.6.0
          - eslint-plugin-react-refresh@0.4.4

  # JavaScript/TypeScript代码格式化 - Prettier
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        name: Prettier代码格式化
        files: ^frontend/.*\.(js|jsx|ts|tsx|json|css|md)$
        additional_dependencies:
          - prettier@3.1.0
          - prettier-plugin-tailwindcss@0.5.7

  # Dockerfile检查 - hadolint
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        name: Hadolint Dockerfile检查
        files: .*Dockerfile.*

  # Shell脚本检查 - shellcheck
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        name: ShellCheck脚本检查
        files: \.sh$

  # 提交信息检查 - commitizen
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen
        name: 提交信息格式检查
        stages: [commit-msg]

  # 密钥检查 - detect-secrets
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        name: 检测敏感信息
        args: ['--baseline', '.secrets.baseline']
        exclude: package.lock.json

# 全局配置
default_language_version:
  python: python3.11
  node: 18.18.0

# 排除文件
exclude: |
  (?x)^(
    .*\.min\.(js|css)|
    .*\.bundle\.(js|css)|
    node_modules/|
    dist/|
    build/|
    __pycache__/|
    \.git/|
    \.venv/|
    venv/|
    \.pytest_cache/|
    \.mypy_cache/
  )$

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false

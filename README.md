# 自适应学习服务数据标注模块

基于知识空间理论的数据标注系统，为自适应学习系统提供高质量的知识结构数据。

## 项目概述

本项目旨在构建一个知识空间增强的标注模块，支持：

- 题目与知识点关联标注（Q-矩阵）
- 知识点先修关系维护
- 题目间关系标注
- 知识空间结构自动推理
- 与IRT、BKT等学习者模型协同

## 技术栈

### 前端
- React 18 + TypeScript
- Vite 构建工具
- Radix UI 组件库
- Tailwind CSS 样式框架

### 后端
- Python 3.11+
- FastAPI 框架
- SQLAlchemy ORM
- Alembic 数据库迁移

### 数据库
- PostgreSQL 15+
- LTREE 扩展（知识点层级）
- pgRouting 扩展（图算法）

### 部署
- Docker 容器化
- Dapr 微服务框架
- Docker Compose 本地开发

## 项目结构

```
annotation/
├── frontend/          # React前端应用
├── backend/           # FastAPI后端应用
├── database/          # 数据库相关文件
├── docker/            # Docker配置
├── docs/              # 项目文档
├── scripts/           # 工具脚本
├── tests/             # 测试文件
└── .github/           # CI/CD配置
```

## 快速开始

### 环境要求

- Node.js 18+
- Python 3.11+
- PostgreSQL 15+
- Docker & Docker Compose

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd annotation
```

2. 启动开发环境
```bash
docker-compose up -d
```

3. 安装依赖
```bash
# 前端
cd frontend
npm install

# 后端
cd ../backend
pip install -r requirements.txt
```

4. 数据库迁移
```bash
cd backend
alembic upgrade head
```

5. 启动服务
```bash
# 前端开发服务器
cd frontend
npm run dev

# 后端开发服务器
cd backend
uvicorn app.main:app --reload
```

## 开发指南

- [API 文档](docs/api/)
- [数据库设计](docs/design/)
- [部署指南](docs/deployment/)

## 贡献指南

请参考 [CONTRIBUTING.md](CONTRIBUTING.md)

## 许可证

[MIT License](LICENSE)
